#!/bin/bash
# 设置定时任务的脚本

echo "设置每周四的定时提醒任务..."

# 获取当前脚本的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_SCRIPT="$SCRIPT_DIR/weekly_reminder.py"

# 添加到crontab（每天上午9点检查）
# 0 9 * * * 表示每天上午9点执行
(crontab -l 2>/dev/null; echo "0 9 * * * cd $SCRIPT_DIR && python3 $PYTHON_SCRIPT") | crontab -

echo "✅ 定时任务已设置完成！"
echo "📋 任务详情：每天上午9点检查是否为周四并进行提醒"
echo "🔍 查看当前定时任务：crontab -l"
echo "❌ 删除定时任务：crontab -e"