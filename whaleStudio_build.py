#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_build.py
# @Time    : 2025/07/24 09:56
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import argparse
import json
import os
import sys
import threading
import time
import traceback
import oss2
import hashlib
import requests
import git
from queue import Queue
from datetime import datetime
from pymysql import connect
from loguru import logger
from oss2 import Auth, Bucket

CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))

logger.remove()
logger.add(
    os.path.join(
        CURRENT_DIRECTORY, "logs", "whaleStudio_build.log"
    ), format = "{time} | {level} | {message}", mode = 'a',
    encoding = 'utf-8', level = "INFO", rotation = "20 MB"
)

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))

MYSQL_HOST = "***********"
MYSQL_USER = "root"
MYSQL_PASSWORD = "QWer12#$"
MYSQL_DATABASE = "jenkins_db"
MYSQL_PORT = 3306

default_public_download_link = "https://whale-ops.oss-cn-wulanchabu.aliyuncs.com"
default_intranet_download_link = "https://whale-ops.oss-cn-wulanchabu-internal.aliyuncs.com"
webhook_url = "https://open.feishu.cn/open-apis/bot/v2/hook/053a9872-d18f-4d3d-a433-6c3fef797e4c"

send_confirmation_message_json = r'''
{{
  "config": {{
    "wide_screen_mode": true
  }},
  "elements": [

    {{
      "tag": "div",
      "text": {{
        "content": "**请点击[链接]({jenkins_url})进行{build_message}**",
        "tag": "lark_md"
      }}
    }}

  ],
  "header": {{
    "template": "blue",
    "title": {{
      "content": "📢  {branch}",
      "tag": "plain_text"
    }}
  }}
}}
'''

end_message_json = r'''
{{
  "config": {{
    "wide_screen_mode": true
  }},
  "elements": [
    {{
        "tag": "div",
        "text": {{
            "content": "{content}",
            "tag": "lark_md"
        }}
    }}
  ],
  "header": {{
    "template": "{color}",
    "title": {{
      "content": "📢  {branch}",
      "tag": "plain_text"
    }}
  }}
}}
'''
customer_list = {"zhongxinjiantou": "中信建投", "guangdalicai": "光大理财", "renshou": "人寿", "renbao": "人保"}


def connect_to_db():
    """
    Connect to the database.
    :return:
    """
    try:
        conn = connect(
            host = MYSQL_HOST, user = MYSQL_USER, password = MYSQL_PASSWORD, port = MYSQL_PORT,
            database = MYSQL_DATABASE
        )
        cursor = conn.cursor()
        return conn, cursor
    except Exception as e:
        logger.error(f"Failed to connect to the database: {e}")
        raise e


def get_file_md5_async(file_path, result_queue):
    """
    异步获取文件的md5
    :param file_path:
    :param result_queue:
    :return:
    """
    logger.info(f"Get the package MD5 asynchronously, start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    try:
        md5 = hashlib.md5()
        with open(file_path, 'rb') as f:
            while True:
                data = f.read(4096)
                if not data:
                    break
                md5.update(data)
        file_md5 = md5.hexdigest()
        result_queue.put(file_md5)
    except Exception as e:
        logger.error(f"get package md5 error: {e}")
        result_queue.put(None)
    finally:
        logger.info(f"Get the package MD5 asynchronously, end time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


# 数据库操作
class DBOperation:
    @staticmethod
    def record_minor_version_number(branch, version, customer = None):
        """
        Record the minor version number in the database. ｜ 记录数据库中的小版本号
        :param branch:
        :param version:
        :param customer:
        :return:
        """
        try:
            conn, cursor = connect_to_db()
            if customer:
                sql = f"INSERT INTO minor_version_number  (branch, customer, minor_version) VALUES ('{branch}', '{customer}', '{version}')"
            else:
                sql = f"INSERT INTO minor_version_number  (branch, minor_version) VALUES ('{branch}', '{version}')"
            logger.info(f"The SQL to record the minor version number is: {sql}")
            cursor.execute(sql)
            conn.commit()
            logger.info(f"The minor version number has been recorded in the database.")
        except Exception as e:
            logger.error(f"Failed to record the minor version number in the database: {e}")
            logger.error(traceback.format_exc())
            raise e


class BuildWhaleStudio:
    def __init__(
            self, operation, branch = None, customer = None, parser = None, file_path = None, jenkins_url = None,
            build_status = None, latest_version = None, commit_id = None, build_path = None
    ):
        self.operation = operation
        self.branch = branch
        self.customer = customer
        self.parser = parser
        self.file_path = file_path
        self.jenkins_url = jenkins_url
        self.build_status = build_status
        self.latest_version = latest_version
        # self.commit_id = commit_id
        self.build_path = build_path

        if not commit_id:
            logger.error(f"The commit id is not provided.")
            self.scheduler_id, self.scheduler_ui_id, self.tunnel_id, self.tunnel_web_id = "unknown", "unknown", "unknown", "unknown"
        else:
            self.scheduler_id, self.scheduler_ui_id, self.tunnel_id, self.tunnel_web_id = commit_id.split(",")

    def build(self):
        options = {
            "get_version": self.get_version,
            "update_commit_id": self.update_commit_id,
            "upload_to_oss": self.upload_to_oss,
            "send_confirmation_message": self.send_confirmation_message,
            # 发送结束消息
            "send_end_message": self.send_end_message,
            "delete_version": self.delete_version,
        }
        if self.operation not in options:
            logger.error(f"The operation {self.operation} is not supported.")
            self.parser.print_help()
            sys.exit(1)
        getattr(self, self.operation)()

    def delete_version(self):
        """
        Delete the version in the database. ｜ 删除数据库中的版本号
        :return:
        """
        try:
            conn, cursor = connect_to_db()
            if self.customer:
                sql = f"DELETE FROM minor_version_number WHERE minor_version = '{self.latest_version}' AND customer = '{self.customer}'"
            else:
                sql = f"DELETE FROM minor_version_number WHERE minor_version = '{self.latest_version}'"
            logger.info(f"The SQL to delete the version is: {sql}")
            cursor.execute(sql)
            conn.commit()
            logger.info(f"The version has been deleted in the database.")
        except Exception as e:
            logger.error(f"Failed to delete the version in the database: {e}")
            logger.error(traceback.format_exc())
            print("Failed to delete the version in the database.")

    def update_commit_id(self):
        """
        Update the commit id in the database. ｜ 更新数据库中的提交ID
        :return:
        """
        try:
            conn, cursor = connect_to_db()
            if self.customer:
                sql = f"UPDATE minor_version_number SET whalescheduler_commit = '{self.scheduler_id}', whalescheduler_ui_commit = '{self.scheduler_ui_id}', whaletunnel_commit = '{self.tunnel_id}', whaletunnel_web_commit = '{self.tunnel_web_id}' WHERE minor_version = '{self.latest_version}'  AND customer = '{self.customer}'"
            else:
                sql = f"UPDATE minor_version_number SET whalescheduler_commit = '{self.scheduler_id}', whalescheduler_ui_commit = '{self.scheduler_ui_id}', whaletunnel_commit = '{self.tunnel_id}', whaletunnel_web_commit = '{self.tunnel_web_id}' WHERE minor_version = '{self.latest_version}' AND customer IS NULL"
            logger.info(f"The SQL to update the commit id is: {sql}")
            cursor.execute(sql)
            conn.commit()
            logger.info(f"The commit id has been updated in the database.")
        except Exception as e:
            logger.error(f"Failed to update the commit id in the database: {e}")
            logger.error(traceback.format_exc())
            print("Failed to update the commit id in the database.")

    def get_download_link_and_md5(self):
        """
        Get the download link and MD5 of the package. ｜ 获取包的下载链接和MD5
        :return:
        """
        try:
            conn, cursor = connect_to_db()
            if self.customer:
                sql = f"SELECT public_download_link, intranet_download_link, package_md5sum FROM minor_version_number WHERE minor_version = '{self.latest_version}' AND customer = '{self.customer}'"
            else:
                sql = f"SELECT public_download_link, intranet_download_link, package_md5sum FROM minor_version_number WHERE minor_version = '{self.latest_version}'"
            logger.info(f"The SQL to get the download link and MD5 is: {sql}")
            cursor.execute(sql)
            result = cursor.fetchone()
            logger.info(f"The result of the SQL to get the download link and MD5 is: {result}")
            if result:
                return result[0], result[1], result[2]
            else:
                return None, None, None
        except Exception as e:
            logger.error(f"Failed to get the download link and MD5 of the package: {e}")
            logger.error(traceback.format_exc())
            raise e

    def get_commit_message(self):
        """
        Get the commit message of the latest commit. ｜ 获取最新提交的提交信息
        :return:
        """
        scheduler_path = os.path.join(
            self.build_path, "whalescheduler"
        )
        scheduler_ui_path = os.path.join(
            self.build_path, "whalescheduler/whalescheduler-ui"
        )
        tunnel_path = os.path.join(
            self.build_path, "whaletunnel"
        )
        tunnel_web_path = os.path.join(
            self.build_path, "whaletunnel-web"
        )

        # 1. 获取上一次的Commit ID
        if self.customer:
            sql = f"SELECT minor_version, whalescheduler_commit, whalescheduler_ui_commit, whaletunnel_commit, whaletunnel_web_commit FROM minor_version_number WHERE branch = '{self.branch}' AND customer = '{self.customer}' order by id desc limit 2"
        else:
            sql = f"SELECT minor_version, whalescheduler_commit, whalescheduler_ui_commit, whaletunnel_commit, whaletunnel_web_commit FROM minor_version_number WHERE branch = '{self.branch}' order by id desc limit 2"
        logger.info(f"The SQL to get the commit message is: {sql}")
        conn, cursor = connect_to_db()
        cursor.execute(sql)
        result = cursor.fetchall()
        if not result or len(result) != 2:
            logger.error(f"Failed to get the commit message, please check the database.")
            print("Failed to get the commit message, please check the database.")
            return None, None, None, None
        else:
            logger.info(f"The result of the SQL to get the commit message is: {result}")
            # 上一次的
            previous_version = result[1][0]
            print(f"The previous version is: {previous_version}")
            # 上一次的Commit ID
            previous_scheduler_id = result[1][1]
            previous_scheduler_ui_id = result[1][2]
            previous_tunnel_id = result[1][3]
            previous_tunnel_web_id = result[1][4]
            # 根据上一次的版本号，获取上一次到最新版本的提交信息
            if not previous_scheduler_id or self.scheduler_id == previous_scheduler_id:
                scheduler_commit_message = None
            else:
                scheduler_commit_message = self.get_commit_message_by_id(
                    scheduler_path, previous_scheduler_id
                )
            if not previous_scheduler_ui_id or self.scheduler_ui_id == previous_scheduler_ui_id:
                scheduler_ui_commit_message = None
            else:
                scheduler_ui_commit_message = self.get_commit_message_by_id(
                    scheduler_ui_path, previous_scheduler_ui_id
                )
            if not previous_tunnel_id or self.tunnel_id == previous_tunnel_id:
                tunnel_commit_message = None
            else:
                tunnel_commit_message = self.get_commit_message_by_id(
                    tunnel_path, previous_tunnel_id
                )
            if not previous_tunnel_web_id or self.tunnel_web_id == previous_tunnel_web_id:
                tunnel_web_commit_message = None
            else:
                tunnel_web_commit_message = self.get_commit_message_by_id(
                    tunnel_web_path, previous_tunnel_web_id
                )
            return scheduler_commit_message, scheduler_ui_commit_message, tunnel_commit_message, tunnel_web_commit_message

    def get_commit_message_by_id(self, path, commit_id):
        """
        Get the commit message by the commit id. ｜ 根据提交ID获取提交信息
        :param path:
        :param commit_id:
        :return:
        """

        # 获取最新的一次提交信息
        repo = git.Repo(path)

        # 获取上一次到本次的提交信息
        commits = list(repo.iter_commits(f"{commit_id}..HEAD"))
        if not commits:
            return None
        # 获取commit id 和 commit message
        old_commit_message = {}

        for commit in commits:
            # 获取commit id 和 commit message
            commit_id = f"{commit.hexsha:.7}"

            commit_message = "\n".join(
                list(
                    set(
                        [line.strip().lstrip("* ") for line in commit.message.split('\n') if
                         line.strip() and not line.startswith(
                             ("Co-authored-by", "* improve", "* update", "* optimize", "--")
                         )]
                    )
                )
            )

            # print([line.strip() for line in commit.message.split('\n') if line.strip()])
            old_commit_message[commit_id] = commit_message
        if not old_commit_message:
            return None
        # 最新一次提交信息
        return old_commit_message

    def get_current_commit_message(self, project_name):
        """

        :param project_name:
        :return:
        """
        if project_name == "whalescheduler":
            path = os.path.join(self.build_path, "whalescheduler")
        elif project_name == "whalescheduler-ui":
            path = os.path.join(self.build_path, "whalescheduler/whalescheduler-ui")
        elif project_name == "whaletunnel":
            path = os.path.join(self.build_path, "whaletunnel")
        elif project_name == "whaletunnel-web":
            path = os.path.join(self.build_path, "whaletunnel-web")
        else:
            logger.error(f"The project name {project_name} is not supported.")
            return None
        repo = git.Repo(path)
        # 获取最新的一次提交信息
        commit_message = [line.strip().lstrip("* ") for line in repo.head.commit.message.split("\n") if
                          line.strip() and not line.startswith(
                              ("Co-authored-by", "* improve", "* update", "* optimize", "--")
                          )]
        if not commit_message:
            return None
        return commit_message

    def send_end_message(self):
        """
        Send the end message. ｜ 发送结束消息
        :return:
        """
        # 1. 拆解COMMIT_ID

        logger.info(f"The scheduler id is: {self.scheduler_id}")
        logger.info(f"The scheduler ui id is: {self.scheduler_ui_id}")
        logger.info(f"The tunnel id is: {self.tunnel_id}")
        logger.info(f"The tunnel web id is: {self.tunnel_web_id}")
        public_download_link, intranet_download_link, package_md5 = self.get_download_link_and_md5()
        logger.info(f"The public download link is: {public_download_link}")
        logger.info(f"The intranet download link is: {intranet_download_link}")
        logger.info(f"The package MD5 is: {package_md5}")

        latest_version_message = f"<font color='green'>{self.latest_version}</font>"
        if self.customer:
            customer_name = customer_list.get(self.customer, self.customer)
            content = "**😝 客户名称：**<font color='green'>{customer}</font>\\n".format(
                customer = customer_name
            )
        else:
            content = ""
        # 打包完成时间
        build_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        if self.build_status == "success":
            if not public_download_link:
                public_download_link = "暂无"
            if not intranet_download_link:
                intranet_download_link = "暂无"
            if not package_md5:
                package_md5 = "暂无"
            # 发送结束消息

            content += f"**😎 本次打包成功，版本为** {latest_version_message} \\n**😎 本次打包完成时间** {build_end_time}\\n"
            content += f"😎 **WhaleScheduler Commit Id**: {self.scheduler_id}"
            content += f"\\n😎 WhaleScheduler UI Commit Id: {self.scheduler_ui_id}"
            content += f"\\n😎 WhaleTunnel Commit Id: {self.tunnel_id}"
            content += f"\\n😎 WhaleTunnel_Web Commit Id: {self.tunnel_web_id}"
            content += f"\\n😎 安装包MD5: {package_md5}"
            content += f"\\n😎 内网下载链接: {intranet_download_link}"
            content += f"\\n😎 外网下载链接: {public_download_link}"
            content += f"\\n👉 **Jenkins 直达**： {self.jenkins_url}"
            content += f"\\n"
            content += "---------- 版本变更记录 -----------"
            scheduler_commit_message, scheduler_ui_commit_message, tunnel_commit_message, tunnel_web_commit_message = self.get_commit_message()
            if scheduler_commit_message:
                content += f"\\n**👉 WhaleScheduler 基于上一次版本的变更记录:**"
                for commit_id, commit_message in scheduler_commit_message.items():
                    content += f"\\n {commit_id}:\\n {commit_message}"
            else:
                content += "\\n**👉 WhaleScheduler 无基于上一次版本的变更记录**"

                scheduler_current_commit_message = self.get_current_commit_message(
                    "whalescheduler"
                )
                if scheduler_current_commit_message:
                    content += f"\\n**👉 WhaleScheduler 本次提交内容: **"
                    for message in scheduler_current_commit_message:
                        content += f"\\n {message}"
                    content += f"\\n---------------------------"
            content += "\\n"
            if scheduler_ui_commit_message:
                content += f"\\n**👉 WhaleScheduler UI 基于上一次版本的变更记录:**"
                for commit_id, commit_message in scheduler_ui_commit_message.items():
                    content += f"\\n {commit_id}:\\n {commit_message}"
            else:
                content += f"\\n**👉 WhaleScheduler UI 无基于上一次版本的变更记录**"
                scheduler_ui_current_commit_message = self.get_current_commit_message(
                    "whalescheduler-ui"
                )
                if scheduler_ui_current_commit_message:
                    content += f"\\n**👉 WhaleScheduler UI 本次提交内容: **"
                    for message in scheduler_ui_current_commit_message:
                        content += f"\\n {message}"
                        content += f"\\n---------------------------"
            content += "\\n"
            if tunnel_commit_message:
                content += f"\\n**👉 WhaleTunnel 基于上一次版本的变更记录:**"
                for commit_id, commit_message in tunnel_commit_message.items():
                    content += f"\\n {commit_id}:\\n {commit_message}"
            else:
                content += f"\\n**👉 WhaleTunnel 无基于上一次版本的变更记录**"
                tunnel_current_commit_message = self.get_current_commit_message(
                    "whaletunnel"
                )
                if tunnel_current_commit_message:
                    content += f"\\n**👉 WhaleTunnel 本次提交内容: **"
                    for message in tunnel_current_commit_message:
                        content += f"\\n {message}"
                        content += f"\\n---------------------------"
            content += "\\n"
            if tunnel_web_commit_message:
                content += f"\\n**👉 WhaleTunnel_Web 基于上一次版本的变更记录:**"
                for commit_id, commit_message in tunnel_web_commit_message.items():
                    content += f"\\n {commit_id}:\\n {commit_message}"
            else:
                content += f"\\n**👉 WhaleTunnel_Web 无基于上一次版本的变更记录**"
                tunnel_web_current_commit_message = self.get_current_commit_message(
                    "whaletunnel-web"
                )
                if tunnel_web_current_commit_message:
                    content += f"\\n**👉 WhaleTunnel_Web 本次提交内容: **"
                    for message in tunnel_web_current_commit_message:
                        content += f"\\n {message}"
                        content += f"\\n---------------------------"
            # content += "\\n"
            body = end_message_json.format(
                branch = f"{self.branch} 分支打包完成",
                content = content,
                color = "green"
            )
        elif self.build_status == "error":
            content += f"**😎 本次打包失败，版本为** {latest_version_message} \\n**😎 本次打包完成时间** {build_end_time}\\n"
            # content += f"😎 **WhaleScheduler Commit Id**: {scheduler_id}"
            # content += f"\\n😎 WhaleScheduler UI Commit Id: {scheduler_ui_id}"
            # content += f"\\n😎 WhaleTunnel Commit Id: {tunnel_id}"
            # content += f"\\n😎 WhaleTunnel_Web Commit Id: {tunnel_web_id}"
            content += f"\\n👉 **Jenkins 直达**： {self.jenkins_url}"
            content += f"\\n"

            body = end_message_json.format(
                branch = f"{self.branch} 分支打包失败",
                content = content,
                color = "red"
            )
        elif self.build_status == "timeout":
            content += f"**{self.branch} 分支打包,等待输入超时,请检查日志**"
            content += f"\\n👉 **Jenkins 直达**： {self.jenkins_url}"
            body = end_message_json.format(
                branch = f"{self.branch} 分支打包超时",
                content = content,
                color = "gray"  ##
            )
        elif self.build_status == "cancel":
            content += f"**{self.branch} 分支打包取消**"
            content += f"\\n👉 **Jenkins 直达**： {self.jenkins_url}"
            body = end_message_json.format(
                branch = f"{self.branch} 分支打包取消",
                content = content,
                color = "gray"  ## 设置灰色
            )
        else:
            content += f"**{self.branch} 分支打包失败,请检查日志**"
            content += f"\\n👉 **Jenkins 直达**： {self.jenkins_url}"
            body = end_message_json.format(
                branch = f"{self.branch} 分支打包失败",
                content = content,
                color = "red"
            )

        try:
            data = json.dumps(
                {
                    "msg_type": "interactive",
                    "card": body
                }
            )

            try:
                with open(os.path.join(f"/data/{self.latest_version}.json"), "w") as f:
                    f.write(data)
            except Exception as e:
                logger.error(f"Failed to write the end message to file: {e}")
                logger.error(traceback.format_exc())

            logger.info(f"Send Feishu message: {data}")
            headers = {"Content-Type": "application/json"}

            res = requests.post(url = webhook_url, data = data, headers = headers)
            logger.info(res.text)
        except Exception as e:
            logger.error(f"Failed to send the end message: {e}")
            logger.error(traceback.format_exc())
            raise e

    def send_confirmation_message(self):
        """
        Send
        the
        confirmation
        message. ｜ 发送确认消息
        :return:
        """

        jenkins_url_split = self.jenkins_url.split("/")
        logger.info(f"The jenkins url is: {jenkins_url_split}")
        jenkins_job_name = jenkins_url_split[-3]
        jenkins_build_number = jenkins_url_split[-2]
        logger.info(f"The jenkins job name is: {jenkins_job_name}")
        logger.info(f"The jenkins build number is: {jenkins_build_number}")

        # 发送飞书消息
        body = json.dumps(
            {
                "msg_type": "interactive",
                "card": send_confirmation_message_json.format(
                    jenkins_url = self.jenkins_url,
                    branch = f"{self.branch}分支构建通知",
                    build_message = f"{self.branch}分支,第{jenkins_build_number}次构建"
                )
            }
        )
        logger.info(f"Send Feishu message: {body}")
        headers = {"Content-Type": "application/json"}

        res = requests.post(url = webhook_url, data = body, headers = headers)
        logger.info(res.text)

    def upload_to_oss(self):
        """
        Upload
        the
        package
        to
        OSS. ｜ 上传包到OSS
        :return:
        """
        print("\n\nStart to upload the package to OSS\n\n")
        if not os.path.isfile(self.file_path):
            logger.error(f"The package file does not exist, please check the package file path")
            logger.error(traceback.format_exc())
            sys.exit(1)
        try:
            AccessKeyID = "LTAI5tQ5SWAFG55MSEuQLFsY"
            AccessKeySecret = "******************************"
            Endpoint = "https://oss-cn-wulanchabu-internal.aliyuncs.com"
            # Endpoint = "https://oss-accelerate.aliyuncs.com"
            auth = Auth(AccessKeyID, AccessKeySecret)
            bucket = Bucket(auth, Endpoint, "whale-ops")
        except Exception as e:
            raise "The Aliyun OSS configuration error, please check the configuration"

        file_name = os.path.basename(self.file_path)
        try:
            if file_name.split("-")[-1].startswith("release"):
                oss_file_path = "three-in-one/release/"
            else:
                oss_file_path = "three-in-one/test/"
        except Exception as e:
            logger.error(f"Failed to get the oss file path: {e}")
            logger.error(traceback.format_exc())
            oss_file_path = "three-in-one/test"

        if self.customer:
            oss_file_path += f"{self.customer}/"

        # 异步获取文件md5
        md5_queue = Queue()
        md5_thread = threading.Thread(
            target = get_file_md5_async, args = (
                self.file_path, md5_queue
            )
        )
        md5_thread.start()
        logger.info(f"Start to upload the package to OSS, start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        try:
            start_time = time.time()
            print(f"Upload the package to OSS: {oss_file_path}{file_name}")
            logger.info(f"OSS Endpoint: {Endpoint}")
            logger.info(f"Upload the package to OSS, file name: {file_name}, oss file path: {oss_file_path}")
            logger.info(f"Local file path: {self.file_path}")
            print(f"Local file path: {self.file_path}")
            bucket.put_object_from_file(
                key = os.path.join(
                    oss_file_path, file_name
                ), filename = self.file_path
            )
            # 耗时
            cost_time = (time.time() - start_time) / 60
            print(f"Upload the package to OSS successfully, time consuming: {cost_time:.2f}m")
            logger.info(f"Upload the package to OSS successfully, time consuming: {cost_time:.2f}m")
        except Exception as e:
            logger.error(f"Failed to upload the package to OSS: {e}")
            logger.error(traceback.format_exc())
            raise e

        try:
            logger.info(f"Modify the file ACL to public read: {oss_file_path}{file_name}")
            bucket.put_object_acl(key = oss_file_path + file_name, permission = oss2.OBJECT_ACL_PUBLIC_READ)
            logger.info(f"Modify the file ACL to public read successfully")
        except Exception as e:
            raise e

        public_download_link = f"{default_public_download_link}/{oss_file_path}{file_name}"
        intranet_download_link = f"{default_intranet_download_link}/{oss_file_path}{file_name}"

        print(f"\n内网下载链接: {intranet_download_link}\n公网下载链接: {public_download_link}")

        # 等待MD5计算线程完成并获取结果
        md5_thread.join()
        package_md5 = md5_queue.get()
        print(f"安装包MD5: {package_md5}")
        logger.info(f"Upload the package to OSS end time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 更新数据库
        try:
            conn, cursor = connect_to_db()
            if self.customer:
                update_sql = f"UPDATE minor_version_number SET public_download_link = '{public_download_link}', intranet_download_link = '{intranet_download_link}', package_md5sum = '{package_md5}' WHERE minor_version = '{self.latest_version}' AND customer = '{self.customer}'"
            else:
                update_sql = f"UPDATE minor_version_number SET public_download_link = '{public_download_link}', intranet_download_link = '{intranet_download_link}', package_md5sum = '{package_md5}' WHERE minor_version = '{self.latest_version}' AND customer IS NULL"
            logger.info(f"The SQL to update the download link and MD5 is: {update_sql}")
            cursor.execute(update_sql)
            conn.commit()
            logger.info(f"The download link and MD5 have been updated in the database.")
        except Exception as e:
            logger.error(f"Failed to update the download link and MD5 in the database: {e}")
            logger.error(traceback.format_exc())

    def minor_version_update(self, version = None):
        """
        Update
        the
        minor
        version
        number in the
        database. ｜ 更新数据库中的小版本号
        :param
        version:
        :return:
        """
        if not version:
            version = self.branch
        logger.info(f"The {self.branch} version is: {version}")
        try:
            # 版本号 - 分支
            version, branch = version.split("-")
            # 分割版本号
            split_version = version.split(".")
        except Exception as e:
            logger.error(f"Failed to get the {self.branch} version: {e}")
            logger.error(traceback.format_exc())
            raise e
        if len(split_version) == 2:
            # 主版本号.次版本号
            major_version, minor_version = split_version
            # 主版本号.次版本号.修订号
            new_version = f"{major_version}.{minor_version}.1"
        elif len(split_version) == 3:
            # 主版本号.次版本号.修订号
            major_version, minor_version, patch_version = split_version
            # 主版本号.次版本号.修订号+1
            new_version = f"{major_version}.{minor_version}.{int(patch_version) + 1}"
        else:
            logger.error(f"The {self.branch} version is not in the correct format.")
            raise ValueError(f"The {self.branch} version is not in the correct format.")
        return f"{new_version}-{branch}"

    def get_version(self):
        logger.info(f"Get the {self.branch} version of whaleStudio.")
        try:
            conn, cursor = connect_to_db()
            if self.customer:
                get_version_sql = "SELECT minor_version FROM minor_version_number WHERE branch = '%s' AND customer is NULL ORDER BY id DESC LIMIT 1"
                cursor.execute(get_version_sql)
                result = cursor.fetchone()
                if result:
                    logger.info(f"The {self.branch} version is: {result[0]}")
                    new_version = result[0]
                else:
                    new_version = self.minor_version_update()
                logger.info(f"The new {self.branch} version is: {new_version}")
            else:
                get_version_sql = f"SELECT minor_version FROM minor_version_number WHERE branch = '{self.branch}'  ORDER BY id DESC LIMIT 1"
                logger.info(f"The SQL to get the {self.branch} version is: {get_version_sql}")
                cursor.execute(get_version_sql)
                result = cursor.fetchone()
                if result:
                    logger.info(f"The {self.branch} version is: {result[0]}")
                    new_version = self.minor_version_update(version = result[0])
                else:
                    new_version = self.minor_version_update()
                logger.info(f"The new {self.branch} version is: {new_version}")
            if self.customer:
                DBOperation.record_minor_version_number(
                    branch = self.branch, version = new_version, customer = self.customer
                )
            else:
                DBOperation.record_minor_version_number(
                    branch = self.branch, version = new_version
                )
            print(new_version)
        except Exception as e:
            logger.error(f"Failed to connect to the database: {e}")
            logger.error(traceback.format_exc())
            raise e


def main():
    parser = argparse.ArgumentParser(description = 'Build whaleStudio.')
    parser.add_argument('--operation', '-o', type = str, help = 'The operation of whaleStudio to build.')
    parser.add_argument('--branch', '-b', type = str, help = 'The branch of whaleStudio to build.')
    parser.add_argument('--customer', '-P', type = str, help = 'The customer name of whaleStudio to build.')
    parser.add_argument('--file', '-f', type = str, help = "package file path")
    parser.add_argument("--jenkins_url", type = str, help = "Jenkins URL")

    # 发送消息时使用
    parser.add_argument("--build_status", type = str, help = "The build status of whaleStudio")
    parser.add_argument("--latest_version", type = str, help = "The latest version of whaleStudio")
    parser.add_argument("--commit_id", type = str, help = "The commit id of whaleStudio")
    parser.add_argument("--build_path", type = str, help = "The build path of whaleStudio")

    argparse_args = parser.parse_args()
    try:
        operation = argparse_args.operation
        branch = argparse_args.branch
        customer = argparse_args.customer
        file_path = argparse_args.file
        jenkins_url = argparse_args.jenkins_url

        build_status = argparse_args.build_status
        latest_version = argparse_args.latest_version
        commit_id = argparse_args.commit_id
        build_path = argparse_args.build_path

        build_whaleStudio = BuildWhaleStudio(
            operation = operation, branch = branch, customer = customer, parser = parser,
            file_path = file_path,
            jenkins_url = jenkins_url,
            build_status = build_status,
            latest_version = latest_version,
            commit_id = commit_id,
            build_path = build_path

        )
        build_whaleStudio.build()
    except Exception as e:
        print('Please input the correct parameters.')
        raise e


if __name__ == '__main__':
    main()