#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : file_tool.py
# @Time    : 2025/06/05 17:11
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import yaml
import json
import hashlib

from app.common.logging_utils import debug_handler
from app.cluster.cluster_for_ssh.tools.command_execution import execute_command

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class FileTool:
    """File tool class. | 文件工具类"""

    def __init__(self, sftp_client, ssh_client = None):
        self.sftp_client = sftp_client
        self.ssh_client = ssh_client

    def is_file_exist(self, file_path):
        """
        Check if a file exists on the remote server. | 检查远程服务器上是否存在文件。
        :param file_path:
        :return:
        """
        try:
            file_attr = self.sftp_client.stat(file_path)
            if file_attr.st_mode & 0o170000 == 0o100000:  # Check if it's a regular file. | 检查是否为普通文件。
                return True
            else:
                return False
        except FileNotFoundError:
            return False

    def get_file_md5sum(self, file_path):
        """
        Get the md5sum of a file on the remote server. | 获取远程服务器上的文件的 md5sum。
        :param file_path:
        :return:
        """
        try:
            file_md5sum = execute_command(
                ssh_client = self.ssh_client,
                command = f"md5sum {file_path}",
            )
            if file_md5sum[0] == 0:
                # Extract the md5sum from the command output. | 从命令输出中提取 md5sum。
                return file_md5sum[1].split()[0].strip()
            else:
                return None
        except Exception as e:
            debug_handler(message = f"Failed to get md5sum of file {file_path}: {e}")
            return None

    def read_file(self, file_path, file_type = "txt"):
        """
        Read a file from the remote server. | 从远程服务器读取文件。
        :param file_path:
        :param file_type:
        :return:
        """
        try:
            with self.sftp_client.open(file_path, 'r') as f:
                match file_type:
                    case "json":
                        return True, json.load(f)
                    case "yaml":
                        return True, yaml.safe_load(f)
                    case _:
                        return True, f.readlines()
        except Exception as e:
            return None, str(e)

    def write_file(self, file_path, content, file_type = "txt"):
        """
        Write content to a file on the remote server. | 将内容写入远程服务器上的文件。
        :param file_path:
        :param content:
        :param file_type:
        :return:
        """
        try:
            with self.sftp_client.open(file_path, 'w') as f:
                match file_type:
                    case "json":
                        json.dump(content, f, ensure_ascii = False, indent = 4)
                    case "yaml":
                        yaml.dump(content, f, allow_unicode = True, sort_keys = False)
                    case _:
                        if isinstance(content, list):
                            f.write("\n".join(content))
                        else:
                            f.write(content)
        except Exception as e:
            return False, str(e)
        return True, ""

    def delete_file(self, file_path):
        """
        Delete a file on the remote server. | 删除远程服务器上的文件。
        :param file_path:
        :return:
        """
        try:
            self.sftp_client.remove(file_path)
        except Exception as e:
            return False, str(e)
        return True, ""

    def push_file(self, local_file_path, remote_file_path):
        """
        Push a local file to the remote server. | 将本地文件推送到远程服务器。
        :param local_file_path:
        :param remote_file_path:
        :return:
        """
        try:
            self.sftp_client.put(local_file_path, remote_file_path)
        except Exception as e:
            return False, str(e)
        return True, ""
