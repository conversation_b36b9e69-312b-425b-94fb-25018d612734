#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_operation.py
# @Time    : 2025/06/06 18:07
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import time
import traceback
from app.config.deployment_config import deployment_config, service_operation_command
from app.common.logging_utils import debug_handler, info_handler, error_handler, warning_handler, title_handler
from app.cluster.cluster_for_ssh.tools.command_execution import execute_command
from app.cluster.cluster_for_ssh.tools.node_connect import node_connect
from app.cluster.cluster_for_ssh.tools.file_tool import FileTool
from app.cluster.config.cluster_node_config import get_services_by_ip

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class WhaleStudioServiceOperation:
    def __init__(self, host, ssh_client, sftp_client, **kwargs):
        self.host = host
        self.ssh_client = ssh_client
        self.sftp_client = sftp_client
        self.result_dict = kwargs.get("result_dict", {})

        # 用户提供的服务列表
        self.user_input_services = kwargs.get("user_input_services", [])
        # 是否需要根据节点角色过滤服务
        self.use_node_roles = kwargs.get("use_node_roles", False)
        #
        self.operation_type = kwargs.get("operation_type", None)  # start or stop
        self.service_roles = get_services_by_ip(
            ip = self.host
        )

        # 根据节点角色配置处理服务列表
        self._process_services_by_roles()

    def run(self):
        """
        Run the service operation.
        :return:
        """

        match self.operation_type:
            case "start":
                return self.start_services()
            case "stop":
                return self.stop_services()
            case "restart":
                return self.restart_services()
            case "status":
                return self.get_services_status()
            case "logs":
                return self.get_services_log()
            case _:
                return False, "Invalid operation type."

    def start_services(self):
        """
        Start the specified services.
        :return:
        """
        start_time = time.time()
        title_handler(message = f"Starting services on host: {self.host}".center(80, "*"))

        # 主机上设置的角色列表

        for service in self.user_input_services:
            if service not in self.service_roles:
                debug_handler(
                    message = f"Host: {self.host} service: `{service}` not found in service roles, skipping start operation."
                )
                continue
            if self.get_service_pids(service_name = service):
                warning_handler(
                    message = f"Host: {self.host} service: `{service}` is already running, skipping start operation."
                )
                continue
            command = service_operation_command(service_name = service, operation_name = "start")
            debug_handler(message = f"Command to start service `{service}` on host {self.host}: {command}")
            if not command:
                error_handler(message = f"No command found for service: `{service}` on host: {self.host}")
                continue
            try:
                recode, output, error = execute_command(
                    ssh_client = self.ssh_client,
                    command = command
                )
                debug_handler(
                    message = f"Command executed: {command}, recode: {recode}, output: {output}, error: {error}"
                )
                if recode != 0:
                    error_handler(message = f"Host: {self.host} service: `{service}` start failed. {error}")
                    continue
                info_handler(message = f"Host: {self.host} service: `{service}` started successfully.")
            except Exception as e:
                error_handler(message = f"Host: {self.host} service: `{service}` start failed. Error: {e}")
                traceback.print_exc()
        end_time = time.time()
        time_consumption = end_time - start_time
        if time_consumption > 60:
            self.result_dict[self.host] += [
                f"Service start time consuming {time_consumption / 60:.2f} minutes."
            ]
        else:
            self.result_dict[self.host] += [
                f"Service start time consuming {time_consumption:.2f} seconds."
            ]
        return True, "Start services successfully."

    def stop_services(self):
        """
        Stop the specified services.
        :return:
        """
        start_time = time.time()
        title_handler(message = f"Stopping services on host: {self.host}".center(80, "*"))
        for service in self.user_input_services:
            if service not in self.service_roles:
                debug_handler(
                    message = f"Host: {self.host} service: `{service}` not found in service roles, skipping stop operation."
                )
                continue
            service_pids = self.get_service_pids(service_name = service)
            if not service_pids:
                warning_handler(
                    message = f"Host: {self.host} service: `{service}` is not running, skipping stop operation."
                )
                continue
            command = "kill -9 " + " ".join(service_pids)
            debug_handler(message = f"Command to stop service `{service}` on host {self.host}: {command}")
            try:
                recode, output, error = execute_command(
                    ssh_client = self.ssh_client,
                    command = command
                )
                debug_handler(
                    message = f"Command executed: {command}, recode: {recode}, output: {output}, error: {error}"
                )
                if recode != 0:
                    error_handler(message = f"Host: {self.host} service: `{service}` stop failed. {error}")
                    continue
                info_handler(message = f"Host: {self.host} service: `{service}` stopped successfully.")
            except Exception as e:
                error_handler(message = f"Host: {self.host} service: `{service}` stop failed. Error: {e}")
                debug_handler(message = traceback.format_exc())
        end_time = time.time()
        time_consumption = end_time - start_time
        if time_consumption > 60:
            self.result_dict[self.host] += [
                f"Service stop time consuming {time_consumption / 60:.2f} minutes."
            ]
        else:
            self.result_dict[self.host] += [
                f"Service stop time consuming {time_consumption:.2f} seconds."
            ]
        return True, "Stop services successfully."

    def restart_services(self):
        """
        Restart the specified services.
        :return:
        """
        start_time = time.time()
        title_handler(message = f"Restarting services on host: {self.host}".center(80, "*"))
        for service in self.user_input_services:
            if service not in self.service_roles:
                debug_handler(
                    message = f"Service `{service}` not found in service roles."
                )
                continue
            process_status = self.get_service_pids(service_name = service)
            if process_status:
                stop_service_command = "kill -9 " + " ".join(process_status)
                debug_handler(
                    message = f"Command to stop service `{service}` on host {self.host}: {stop_service_command}"
                )
                try:
                    recode, output, error = execute_command(
                        ssh_client = self.ssh_client,
                        command = stop_service_command
                    )
                    debug_handler(
                        message = f"Command executed: {stop_service_command}, recode: {recode}, output: {output}, error: {error}"
                    )
                    if recode != 0:
                        error_handler(message = f"Host: {self.host} service: `{service}` stop failed. {error}")
                        continue
                except Exception as e:
                    error_handler(message = f"Host: {self.host} service: `{service}` stop failed. Error: {e}")
                    debug_handler(message = traceback.format_exc())
            start_service_command = service_operation_command(service_name = service, operation_name = "start")
            debug_handler(
                message = f"Command to start service `{service}` on host {self.host}: {start_service_command}"
            )
            if not start_service_command:
                error_handler(message = f"No command found for service: `{service}` on host: {self.host}")
                continue
            try:
                recode, output, error = execute_command(
                    ssh_client = self.ssh_client,
                    command = start_service_command
                )
                debug_handler(
                    message = f"Command executed: {start_service_command}, recode: {recode}, output: {output}, error: {error}"
                )
                if recode != 0:
                    error_handler(message = f"Host: {self.host} service: `{service}` restart failed. {error}")
                    continue
                info_handler(message = f"Host: {self.host} service: `{service}` restarted successfully.")
            except Exception as e:
                error_handler(message = f"Host: {self.host} service: `{service}` restart failed. Error: {e}")
                debug_handler(message = traceback.format_exc())
        end_time = time.time()
        time_consumption = end_time - start_time
        if time_consumption > 60:
            self.result_dict[self.host] += [
                f"Service restart time consuming {time_consumption / 60:.2f} minutes."
            ]
        else:
            self.result_dict[self.host] += [
                f"Service restart time consuming {time_consumption:.2f} seconds."
            ]
        return True, "Restart services successfully."

    def get_services_status(self):
        """
        Get the status of the specified services.
        :return:
        """
        start_time = time.time()
        title_handler(message = f"Getting status of services on host: {self.host}".center(80, "*"))
        for service in self.user_input_services:
            if service not in self.service_roles:
                debug_handler(
                    message = f"Host: {self.host} service: `{service}` not found in service roles, skipping status operation."
                )
                continue
            service_pids = self.get_service_pids(service_name = service)
            if not service_pids:
                info_handler(message = f"Host: {self.host} service: `{service}` is not running.")
                continue
            info_handler(message = f"Host: {self.host} service: `{service}` is running.")
        end_time = time.time()
        time_consumption = end_time - start_time
        if time_consumption > 60:
            self.result_dict[self.host] += [
                f"Service status getting time consuming {time_consumption / 60:.2f} minutes."
            ]
        else:
            self.result_dict[self.host] += [
                f"Service status getting time consuming {time_consumption:.2f} seconds."
            ]
        return True, "Get services status successfully."

    def get_services_log(self):
        """
        Get the log of the specified services.
        :return:
        """
        service_name = self.user_input_services
        service_logs_path = os.path.join(
            deployment_config.cluster_nodes.get(
                self.host, {}
            ).get(
                "service_log_dir", None
            ) or deployment_config.service_log_dir, f"{service_name}-server", f"whalescheduler-{service_name}.log"
        )
        if not FileTool(
                sftp_client = self.sftp_client,
        ).is_file_exist(
            file_path = service_logs_path
        ):
            error_handler(message = f"Host: {self.host} service: `{service_name}` log file not found.")
            error_handler(
                message = "Please check the service log directory and service name in the deployment configuration file."
            )
            error_handler(message = f"Service log directory: {service_logs_path}")
        try:
            logs_status, logs_output, logs_error = self.ssh_client.exec_command(
                f"tail -200f {service_logs_path}"
            )
            while not logs_status.channel.exit_status_ready():
                logs_output_line = logs_output.readline()
                if logs_output_line:
                    print(logs_output_line.rstrip())
        except KeyboardInterrupt:
            warning_handler(message = "User interrupted the log retrieval.")
            return True, "Log retrieval interrupted by user."
        except Exception as e:
            error_handler(message = f"Failed to get service log for {service_name} on host {self.host}: {e}")
            debug_handler(message = traceback.format_exc())
            return False, "Failed to get service log."
        return True, "Get services log successfully."

    def get_service_pids(self, service_name):
        """
        Get the pids of the specified service.
        :param service_name:
        :return:
        """
        command = service_operation_command(service_name = service_name, operation_name = "status")
        if not command:
            debug_handler(message = f"No command found for service: {service_name} on host: {self.host}")
            return None
        try:
            recode, output, error = execute_command(
                ssh_client = self.ssh_client,
                command = command
            )
            debug_handler(message = f"Command executed: {command}, recode: {recode}, output: {output}, error: {error}")
            if recode != 0 and not output:
                return None
            if output:
                return [
                    line.strip() for line in output.splitlines() if line.strip()
                ]
            else:
                return None
        except Exception as e:
            debug_handler(message = f"Failed to get service pids for {service_name} on host {self.host}: {e}")
            return None

    def _process_services_by_roles(self):
        """
        根据节点角色配置处理服务列表
        Process service list based on node roles configuration
        """
        if self.use_node_roles and self.user_input_services == "all":
            # 当用户输入 "all" 且需要根据节点角色过滤时，使用节点的角色配置
            self.user_input_services = self.service_roles
            debug_handler(
                message = f"Host: {self.host} - Using node roles configuration: {self.user_input_services}"
            )
        elif isinstance(self.user_input_services, list):
            # 用户指定了具体服务，需要与节点角色配置取交集
            # 只操作该节点实际配置的服务
            original_services = self.user_input_services.copy()
            self.user_input_services = [
                service for service in self.user_input_services
                if service in self.service_roles
            ]

            # 记录被过滤掉的服务
            filtered_services = set(original_services) - set(self.user_input_services)
            if filtered_services:
                debug_handler(
                    message = f"Host: {self.host} - Services filtered out (not in node roles): {list(filtered_services)}"
                )

            debug_handler(
                message = f"Host: {self.host} - Final service list after role filtering: {self.user_input_services}"
            )


def service_operation(host, *args, **kwargs):
    """
    Perform service operations on the specified host.
    :param host:
    :param args:
    :param kwargs:
    :return:
    """

    result_dict = kwargs.get("result_dict", {})
    ssh_connect_start_time = time.time()
    ssh_client, sftp_client, connect_result = node_connect(host)
    if not ssh_client or not sftp_client:
        return False, connect_result
    result_dict[host] = []
    ssh_connect_end_time = time.time()
    ssh_connect_time_consumption = ssh_connect_end_time - ssh_connect_start_time
    if ssh_connect_time_consumption > 60:
        result_dict[host] += [
            f"SSH connection time consuming {ssh_connect_time_consumption / 60:.2f} minutes."
        ]
    else:
        result_dict[host] += [
            f"SSH connection time consuming {ssh_connect_time_consumption:.2f} seconds."
        ]
    return WhaleStudioServiceOperation(host, ssh_client, sftp_client, **kwargs).run()
