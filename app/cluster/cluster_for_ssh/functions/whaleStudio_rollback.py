#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_rollback.py
# @Time    : 2025/06/16 18:16
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0
import json
import os
import sys
import time
import traceback

from app.config.deployment_config import deployment_config
from app.common.logging_utils import title_handler, debug_handler, warning_handler, error_handler, info_handler
from app.cluster.cluster_for_ssh.tools.node_connect import node_connect
from app.cluster.cluster_for_ssh.tools.file_tool import FileTool
from app.cluster.cluster_for_ssh.tools.path_tool import PathTool

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def rollback(rollback_script, sftp_client):
    """
    Rollback Whale Studio to the previous version. | 恢复Whale Studio到上一个版本
    :param rollback_script: 回退脚本
    :return: None
    """
    start_time = time.time()
    path_tool = PathTool(sftp_client)
    delete_status, delete_result = path_tool.delete_symlink(list(rollback_script.keys()))
    if not delete_status:
        error_handler(message = f"Error occurred while deleting symlinks: {delete_result}")
        return False, delete_result, None
    create_status, create_result = path_tool.create_symlink(rollback_script)
    if not create_status:
        error_handler(message = f"Error occurred while creating symlinks: {create_result}")
        return False, create_result, None
    end_time = time.time()
    time_consumption = end_time - start_time
    if time_consumption > 60:
        time_consumption_str = f"{time_consumption / 60:.2f} minutes"
    else:
        time_consumption_str = f"{time_consumption:.2f} seconds"
    return True, "Rollback succeeded.", time_consumption_str


# 版本号解析
def parse_version(file_tool, previous_version_file_path):
    """
    Parse the version string to a tuple. | 解析版本号字符串为元组
    :param version_str: 版本号字符串
    :return: 版本号元组
    """
    read_previous_version_status, previous_versions = file_tool.read_file(
        file_path = previous_version_file_path,
    )
    if not read_previous_version_status:
        return False, "Error occurred while reading previous version file."
    # 解析上一次的 whalescheduler 版本号
    for line in previous_versions:
        if line.startswith("git.build.version="):
            previous_version = line.split("=")[1].strip()
            return True, previous_version
    else:
        return False, "Previous version not found in version.properties file."


def rollback_whale_studio(host, *args, **kwargs):
    """
    Rollback Whale Studio to the previous version. | 恢复Whale Studio到上一个版本
    :return: None
    """
    warning_handler(message = f"Rollback host: Whale Studio version of {host}.Please wait...")
    result_dict = kwargs.get("result_dict", {})
    ssh_connect_start_time = time.time()
    ssh_client, sftp_client, connect_result = node_connect(host)
    if not ssh_client or not sftp_client:
        return False, connect_result
    result_dict[host] = []
    ssh_connect_end_time = time.time()
    ssh_connect_time_consumption = ssh_connect_end_time - ssh_connect_start_time
    if ssh_connect_time_consumption > 60:
        result_dict[host] += [
            f"SSH connection time consuming {ssh_connect_time_consumption / 60:.2f} minutes."
        ]
    else:
        result_dict[host] += [
            f"SSH connection time consuming {ssh_connect_time_consumption:.2f} seconds."
        ]

    rollback_to_previous_status = False

    try:
        title_handler(message = f"Perform pre deployment check on host {host}".center(100, "="))
        # 进行节点的回退操作
        debug_handler(message = f"Start to rollback Whale Studio on host {host}.")
        # 1. 获取当前目录下是否有 主机_rollback_whale_studio.sh 文件
        rollback_script_path = os.path.join(CURRENT_DIRECTORY, f"{host}_current_symlink.json")
        if not os.path.isfile(rollback_script_path):
            warning_handler(message = f"No rollback script found on host {host}.")
            return False, "No rollback script found on host."
    except Exception as e:
        debug_handler(message = f"Error occurred while performing pre deployment check on host {host}.")
        return False, str(e)
    file_tool = FileTool(sftp_client, ssh_client)
    try:
        # 读取 rollback_script_path 文件
        with open(rollback_script_path, "r") as f:
            rollback_script = json.load(f)
            if not rollback_script:
                warning_handler(message = f"No rollback script found on host {host}.")
                return False, "No rollback script found on host."
        # 获取 rollback_script 里面的 whalescheduler 版本号
        for target_path in rollback_script.values():
            if target_path.endswith("whalescheduler"):
                whaleScheduler_dir_path = target_path
                break
        else:
            warning_handler(message = f"No whalescheduler found in rollback script on host {host}.")
            return False, "No whalescheduler found in rollback script."
        # 使用远程工具获取上一次的 whalescheduler 版本号
        # 上一次的版本号文件路径
        previous_version_file_path = os.path.join(whaleScheduler_dir_path, "version.properties")
        if not file_tool.is_file_exist(
                file_path = previous_version_file_path
        ):
            error_handler(
                message = f"Previous version file not found on host {host}. Cannot revert back to the previous version."
            )
            return False, "Previous version file not found on host."

        # 解析上一次的 whalescheduler 版本号
        show_previous_version_status, previous_version = parse_version(
            file_tool = file_tool,
            previous_version_file_path = previous_version_file_path,
        )
        if not show_previous_version_status:
            error_handler(
                message = "Error occurred while parsing previous version. Cannot revert back to the previous version."
            )
            return False, "Error occurred while parsing previous version."
        # 获取当前的 whalescheduler 版本号
        current_version_file_path = os.path.join(
            deployment_config.deployment_dir, "current", "whalestudio", "version.properties"
        )
        if not file_tool.is_file_exist(
                file_path = current_version_file_path
        ):
            warning_handler(message = f"Current version file not found on host {host}.")
            # 直接回退到上一次的 whalescheduler 版本
            rollback_status, rollback_result, rollback_time_consumption = rollback(
                rollback_script = rollback_script,
                sftp_client = sftp_client,
            )
            if rollback_status:
                result_dict[host] += [
                    rollback_time_consumption
                ]
                info_handler(
                    message = f"Host: {host} rollback succeeded. Current version of whaleStudio is {previous_version}."
                )
                rollback_to_previous_status = True
                return True, "Rollback succeeded."
            else:
                error_handler(message = f"Host: {host} rollback failed Reason for error: {rollback_result}")
                return False, rollback_result

        # 读取当前的 whalescheduler 版本号
        current_version_status, current_version = parse_version(
            file_tool = file_tool,
            previous_version_file_path = current_version_file_path,
        )
        if current_version == previous_version:
            warning_handler(message = f"Whale Studio on host {host} is already at the previous version.")
            rollback_to_previous_status = True
            return True, "Whale Studio is already at the previous version."
        # 回退到上一次的 whalescheduler 版本
        rollback_status, rollback_result, rollback_time_consumption = rollback(
            rollback_script = rollback_script,
            sftp_client = sftp_client,
        )
        if rollback_status:
            result_dict[host] += [
                f"Rollback succeeded. time consuming: {rollback_time_consumption}"
            ]
            info_handler(
                message = f"Host: {host} rollback succeeded. Current version of whaleStudio is {previous_version}."
            )
            rollback_to_previous_status = True
            return True, "Rollback succeeded."
        error_handler(message = f"Host: {host} rollback failed Reason for error: {rollback_result}")
        return False, rollback_result

    except Exception as e:
        error_handler(message = f"Error occurred while parsing rollback script on host {host}. Error: {str(e)}")
        debug_handler(message = traceback.format_exc())
        return False, f"Error occurred while parsing rollback script."
    finally:
        if rollback_to_previous_status:
            # 删除当前的 rollback_script 文件
            try:
                os.remove(rollback_script_path)
            except Exception as e:
                debug_handler(message = f"Error occurred while deleting rollback script file. Error: {str(e)}")
                debug_handler(message = traceback.format_exc())
