#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_install.py
# @Time    : 2025/06/06 09:23
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import time
import json
import traceback
from app.config.deployment_config import deployment_config
from app.common.logging_utils import debug_handler, info_handler, warning_handler, title_handler
from app.cluster.cluster_for_ssh.tools.node_connect import node_connect
from app.cluster.cluster_for_ssh.tools.path_tool import PathTool
from app.cluster.cluster_for_ssh.tools.file_tool import FileTool
from app.cluster.cluster_for_ssh.tools.command_execution import execute_command
from app.cluster.cluster_for_ssh.functions.whaleStudio_configuration_update import ConfigurationFileUpdate
from app.cluster.cluster_for_ssh.functions.pre_check import SSHClusterPreCheck

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class WhaleStudioInstall:
    """
    Install Whale Studio.
    """

    def __init__(self, host, ssh_client, sftp_client, **kwargs):
        self.host = host
        self.ssh_client = ssh_client
        self.sftp_client = sftp_client
        self.result_dict = kwargs.get("result_dict", {})
        self.kwargs = kwargs

        self.latest_package_path = kwargs.get(
            "latest_package_path", None
        )
        self.latest_package_name = kwargs.get(
            "latest_package_name", ""
        )
        self.latest_package_md5sum = kwargs.get(
            "latest_package_md5sum", None
        )
        installation_time = kwargs.get(
            "installation_time", None
        )
        self.extract_dir = os.path.join(
            deployment_config.deployment_dir, "current_package",
            f"{self.latest_package_name.split('.tar.gz')[0]}_{installation_time}"
        )

        self.symlink_dir = os.path.join(
            deployment_config.deployment_dir, "current"
        )

        # 远端安装包存储路径
        self.remote_package_path = os.path.join(
            deployment_config.deployment_dir, "installation_packages"
        )

        self.file_tool = FileTool(
            sftp_client = sftp_client,
            ssh_client = ssh_client
        )

        self.path_tool = PathTool(
            sftp_client = sftp_client
        )

    def run(self):
        """
        Install Whale Studio.
        """
        title_handler(message = f"Installing Whale Studio on {self.host}".center(100, "="))
        # 1. 检查远端是否存在安装包
        # 2. 如果不存在，则上传安装包
        # 3. 解压安装包
        # 4. 记录当前软连接
        # 5. 更新软连接
        # 6. 修改配置文件
        # install_whale_studio_start_time = time.time()
        for ssh_cluster_install_task in [
            self.push_package,  # 上传安装包
            self.extract_package,  # 解压安装包
            self.update_soft_link,  # 更新软连接
            self.modify_config,  # 修改配置文件
        ]:
            start_time = time.time()
            task_run_status, task_run_message = ssh_cluster_install_task()
            if not task_run_status:
                return False, task_run_message
            end_time = time.time()
            time_consumption = end_time - start_time
            if time_consumption > 60:
                self.result_dict[self.host] += [
                    f"{task_run_message} time consuming {time_consumption / 60:.2f} minutes."
                ]
            else:
                self.result_dict[self.host] += [
                    f"{task_run_message} time consuming {time_consumption:.2f} seconds."
                ]
        # install_whale_studio_end_time = time.time()
        # install_whale_studio_time_consumption = install_whale_studio_end_time - install_whale_studio_start_time
        # if install_whale_studio_time_consumption > 60:
        #     warning_handler(
        #         message = f"Whale Studio installation on {self.host} time consuming {install_whale_studio_time_consumption / 60:.2f} minutes."
        #     )
        # else:
        #     warning_handler(
        #         message = f"Whale Studio installation on {self.host} time consuming {install_whale_studio_time_consumption:.2f} seconds."
        #     )
        return True, "Install Whale Studio successfully."

    def push_package(self):
        """
        Upload the installation package to the remote host.
        """
        warning_handler(message = f"Pushing installation package to host {self.host}. Please wait...")
        # 1. 检查远端是否存在安装包
        if self.file_tool.is_file_exist(
                file_path = os.path.join(
                    deployment_config.package_dir, self.latest_package_name
                )
        ) and self.file_tool.get_file_md5sum(
            file_path = os.path.join(
                deployment_config.package_dir, self.latest_package_name
            )
        ) == self.latest_package_md5sum:
            self.remote_package_path = os.path.join(deployment_config.package_dir, self.latest_package_name)
            debug_handler(
                message = f"Installation package {self.latest_package_name} already exists in local package directory {deployment_config.package_dir}."
            )
            info_handler(
                message = f"There is an installation package {self.latest_package_name} in the remote service {self.host}, which will be used for installation."
            )
            return True, "Installation package already exists on remote host."
        elif self.file_tool.is_file_exist(
                file_path = os.path.join(
                    self.remote_package_path, self.latest_package_name
                )
        ) and self.file_tool.get_file_md5sum(
            file_path = os.path.join(
                self.remote_package_path, self.latest_package_name
            )
        ) == self.latest_package_md5sum:
            self.remote_package_path = os.path.join(
                self.remote_package_path, self.latest_package_name
            )
            debug_handler(
                message = f"Installation package {self.latest_package_name} already exists in remote package directory {self.remote_package_path}."
            )
            info_handler(
                message = f"Installation package {self.latest_package_name} already exists on {self.host}."
            )
            return True, "Installation package already exists on remote host."

        else:
            warning_handler(
                message = f"Installation package {self.latest_package_name} does not exist on {self.host}. Uploading..."
            )
        # 上传安装包到远端服务器
        if not self.path_tool.is_directory_exist(
                self.remote_package_path
        ):
            create_dir_status, create_dir_message = self.path_tool.create_directory(
                directory_path = self.remote_package_path
            )
            if not create_dir_status:
                return False, create_dir_message
        push_package_status, push_package_message = self.file_tool.push_file(
            local_file_path = os.path.join(
                deployment_config.package_dir, self.latest_package_name
            ),
            remote_file_path = os.path.join(
                self.remote_package_path, self.latest_package_name
            )
        )
        if not push_package_status:
            return False, push_package_message
        info_handler(message = f"Installation package {self.latest_package_name} pushed to {self.host} successfully.")
        self.remote_package_path = os.path.join(
            self.remote_package_path, self.latest_package_name
        )

        return True, "Installation package pushed successfully."

    def extract_package(self):
        """
        Extract the installation package on the remote host.
        """
        warning_handler(message = f"Extracting installation package on host {self.host}. Please wait...")
        # 1. 检查解压目录是否存在
        if not self.path_tool.is_directory_exist(self.extract_dir):
            create_dir_status, create_dir_message = self.path_tool.create_directory(
                directory_path = self.extract_dir
            )
            if not create_dir_status:
                return False, create_dir_message
        debug_handler(message = f"Extracting installation package to directory {self.extract_dir}.")
        # 2. 解压安装包
        extract_status, extract_output, extract_error = execute_command(
            ssh_client = self.ssh_client,
            command = f"tar -xzf {self.remote_package_path} -C {self.extract_dir}",
        )
        if extract_status != 0:
            return False, f"Failed to extract installation package. Error: {extract_error}"

        # 获取一下解压后的子目录内容是否包含 ['whalescheduler', 'whaletunnel', 'datasource']
        sub_dir_list = self.path_tool.sub_directory_list(
            directory_path = self.extract_dir
        )
        if not sub_dir_list or not all(
                sub_dir in sub_dir_list for sub_dir in ['whalescheduler', 'whaletunnel', 'datasource']
        ):
            return False, f"Failed to extract installation package. Error: {extract_error}"
        info_handler(
            message = f"The installation package {self.latest_package_name} has been unpacked and completed"
        )
        return True, "Installation package extracted successfully."

    def update_soft_link(self):
        """
        Update the soft link to point to the new installation directory.
        """
        warning_handler(message = f"Updating soft link on host {self.host}. Please wait...")
        # 1. 判断当前是否存在软连接
        if self.path_tool.is_directory_exist(
                directory_path = self.symlink_dir
        ):
            recorded_symlink = self.path_tool.get_symlink(
                symlink_path = self.symlink_dir
            )
            if recorded_symlink:
                debug_handler(message = f"Current soft link is {recorded_symlink}.")
                # 将当前软连接记录到 {host}_current_symlink.json 文件中
                try:
                    with open(
                            os.path.join(CURRENT_DIRECTORY, f"{self.host}_current_symlink.json"), "w"
                    ) as f:
                        json.dump(
                            recorded_symlink, f, indent = 4, ensure_ascii = False
                        )
                        debug_handler(
                            message = f"Current soft link has been recorded to {os.path.join(CURRENT_DIRECTORY, f'{self.host}_current_symlink.json')}."
                        )
                    debug_handler(
                        message = f"Current soft link {recorded_symlink} has been recorded to {self.host}_current_symlink.json."
                    )
                except Exception as e:
                    debug_handler(
                        message = f"Failed to record current soft link {recorded_symlink} to {self.host}_current_symlink.json. Error: {e}"
                    )
                    debug_handler(message = traceback.format_exc())
                # 删除当前软连接
                delete_symlink_status, delete_symlink_message = self.path_tool.delete_symlink(
                    symlink_paths = recorded_symlink
                )
                if not delete_symlink_status:
                    return False, delete_symlink_message
            debug_handler(message = f"Current soft link {recorded_symlink} deleted successfully.")
            # 新的软连接对应关系
            new_symlink_dict = {
                os.path.join(
                    self.symlink_dir, "whalestudio"
                ): os.path.join(
                    self.extract_dir, "whalescheduler"
                ),
                os.path.join(
                    self.symlink_dir, "datasource"
                ): os.path.join(
                    self.extract_dir, "datasource"
                )
            }
            if deployment_config.deploy_whaletunnel:
                new_symlink_dict[os.path.join(
                    self.symlink_dir, "whaletunnel"
                )] = os.path.join(
                    self.extract_dir, "whaletunnel"
                )
            # 创建新的软连接
            create_symlink_status, create_symlink_message = self.path_tool.create_symlink(
                symlink_dict = new_symlink_dict
            )
            if not create_symlink_status:
                return False, create_symlink_message
        else:
            debug_handler(message = f"Current soft link does not exist.")
            create_symlink_status, create_symlink_message = self.path_tool.create_directory(
                directory_path = self.symlink_dir
            )
            if not create_symlink_status:
                # error_handler(message = f"Failed to create symlink directory {self.symlink_dir}. Error: {create_symlink_message}")
                return False, create_symlink_message
        info_handler(message = f"Soft link updated successfully on {self.host}.")
        return True, "Soft link updated successfully."

    def modify_config(self):
        """
        Modify the configuration file for Whale Studio.
        """
        title_handler(message = f"Modifying configuration file on host {self.host}".center(100, "="))
        return ConfigurationFileUpdate(
            host = self.host,
            ssh_client = self.ssh_client,
            sftp_client = self.sftp_client,
            **self.kwargs
        ).run()


def install_whale_studio(host, *args, **kwargs):
    """
    Install Whale Studio. | 安装鲸鱼工作室
    """
    result_dict = kwargs.get("result_dict", {})
    ssh_connect_start_time = time.time()
    ssh_client, sftp_client, connect_result = node_connect(host)
    if not ssh_client or not sftp_client:
        return False, connect_result
    result_dict[host] = []
    ssh_connect_end_time = time.time()
    ssh_connect_time_consumption = ssh_connect_end_time - ssh_connect_start_time
    if ssh_connect_time_consumption > 60:
        result_dict[host] += [
            f"SSH connection time consuming {ssh_connect_time_consumption / 60:.2f} minutes."
        ]
    else:
        result_dict[host] += [
            f"SSH connection time consuming {ssh_connect_time_consumption:.2f} seconds."
        ]
    try:
        if not deployment_config.skip_pre_check:
            title_handler(message = f"Perform pre deployment check on host {host}".center(100, "="))
            # 先进行环境检查
            pre_check_start_time = time.time()
            pre_check_status, pre_check_message = SSHClusterPreCheck(
                host = host,
                result_dict = result_dict,
                ssh_client = ssh_client,
                sftp_client = sftp_client,
            ).run()
            if not pre_check_status:
                return False, pre_check_message
            pre_check_end_time = time.time()
            pre_check_time_consumption = pre_check_end_time - pre_check_start_time
            if pre_check_time_consumption > 60:
                warning_handler(
                    message = f"Pre deployment check time for host: {pre_check_time_consumption / 60:2f} minutes"
                )
            else:
                warning_handler(message = f"Pre deployment check time for host: {pre_check_time_consumption:.2f} seconds")
        return WhaleStudioInstall(
            host = host,
            ssh_client = ssh_client,
            sftp_client = sftp_client,
            **kwargs
        ).run()

    except Exception as e:
        debug_handler(message = f"Failed to install Whale Studio on {host}. Error: {e}")
        debug_handler(message = traceback.format_exc())
        return False, f"Failed to install Whale Studio. Error: {e}"
