#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_uninstall.py
# @Time    : 2025/06/09 10:22
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import time
import traceback
from app.config.deployment_config import deployment_config
from app.common.logging_utils import debug_handler, error_handler, info_handler, warning_handler
from app.cluster.cluster_for_ssh.functions.whaleStudio_operation import WhaleStudioServiceOperation
from app.cluster.cluster_for_ssh.tools.node_connect import node_connect
from app.cluster.cluster_for_ssh.tools.command_execution import execute_command
from app.cluster.cluster_for_ssh.tools.path_tool import PathTool

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def whaleStudio_uninstall(host, *args, **kwargs):
    """Uninstall Whale Studio. | 卸载 Whale Studio"""
    # 1. 停止所有的 whalestudio 服务
    # 2. 删除部署目录
    result_dict = kwargs.get("result_dict", {})
    ssh_connect_start_time = time.time()
    ssh_client, sftp_client, connect_result = node_connect(host)
    if not ssh_client or not sftp_client:
        return False, connect_result
    result_dict[host] = []
    ssh_connect_end_time = time.time()
    ssh_connect_time_consumption = ssh_connect_end_time - ssh_connect_start_time
    if ssh_connect_time_consumption > 60:
        result_dict[host] += [
            f"SSH connection time consuming {ssh_connect_time_consumption / 60:.2f} minutes."
        ]
    else:
        result_dict[host] += [
            f"SSH connection time consuming {ssh_connect_time_consumption:.2f} seconds."
        ]
    kwargs[
        "operation_type"
    ] = "stop"

    try:
        stop_whale_studio_status, stop_whale_studio_result = WhaleStudioServiceOperation(
            host = host,
            ssh_client = ssh_client,
            sftp_client = sftp_client,
            **kwargs
        ).run()
        if not stop_whale_studio_status:
            return False, stop_whale_studio_result
        warning_handler(message = f"Uninstall Whale Studio on host: {host}. Please wait...")
        # 待删除目录
        for directory in [
            os.path.join(
                deployment_config.deployment_dir, "current"
            ),
            os.path.join(
                deployment_config.deployment_dir, "current_package"
            ),
            os.path.join(
                deployment_config.deployment_dir, "installation_pacakges"
            ),
            deployment_config.package_dir
        ]:

            if not PathTool(
                    sftp_client = sftp_client
            ).is_directory_exist(
                directory_path = directory
            ):
                warning_handler(message = f"Host: {host} Directory: {directory} does not exist.")
                continue

            debug_handler(message = f"Host: {host} Deleting directory: {directory}")
            status, stdout, stderr = execute_command(
                ssh_client = ssh_client,
                command = f"rm -r {directory} || true"
            )
            if status == 0:
                info_handler(message = f"Host: {host} Directory: {directory} deleted successfully.")
                debug_handler(message = f"Host: {host} Directory: {directory} deleted successfully.")
            else:
                debug_handler(message = f"Host: {host} Directory: {directory} deletion failed. Error: {stderr}")
                return False, f"Directory: {directory} deletion failed. Error: {stderr}"
        return True, "Uninstall Whale Studio Completed."
    except Exception as e:
        error_handler(message = f"Host: {host} Uninstall Whale Studio failed. Error: {e}")
        debug_handler(message = traceback.format_exc())
        return False, f"Uninstall Whale Studio failed.Error: {e}"
