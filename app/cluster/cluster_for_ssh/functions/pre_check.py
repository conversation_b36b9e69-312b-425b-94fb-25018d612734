#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : pre_check.py
# @Time    : 2025/06/05 15:36
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import traceback
import time
from datetime import datetime

from app.config.deployment_config import deployment_config, load_env_command
from app.common.logging_utils import info_handler, error_handler, debug_handler, warning_handler, title_handler
from app.cluster.cluster_for_ssh.tools.node_connect import node_connect
from app.cluster.cluster_for_ssh.tools.command_execution import execute_command
from app.cluster.cluster_for_ssh.tools.path_tool import PathTool
from app.cluster.cluster_for_ssh.tools.file_tool import FileTool

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def convert_to_number(s):
    try:
        # 先尝试转换为 int
        return int(s)
    except ValueError:
        try:
            # 如果失败，尝试转换为 float
            return float(s)
        except ValueError:
            # 如果都不是，返回 None 或抛出异常
            return None


class SSHClusterPreCheck:
    """
    This class is used to check the environment before starting the SSH cluster.
    """

    def __init__(self, host, result_dict, ssh_client, sftp_client):
        self.host = host
        self.result_dict = result_dict
        self.ssh_client = ssh_client
        self.sftp_client = sftp_client
        self.path_tool = PathTool(sftp_client)
        self.file_tool = FileTool(sftp_client)

    def run(self):
        """
        This function is used to check the environment before starting the SSH cluster.
        | 这个函数用于检查SSH集群启动前的环境。
        """
        for pre_check_task in [
            # 检查时间差异
            self.check_time_difference,
            # # 检查Java版本
            self.check_java_version,
            # # 检查磁盘权限
            self.check_disk_permissions,
            # # 检查磁盘空间
            self.check_disk_space,
            # # 检查内存
            self.check_free_memory,
        ]:
            start_time = time.time()
            pre_check_task_status, pre_check_task_result = pre_check_task()
            if not pre_check_task_status:
                return False, pre_check_task_result
            end_time = time.time()
            time_consumption = end_time - start_time
            if time_consumption > 60:
                self.result_dict[self.host] += [
                    f"{pre_check_task_result} time consuming {time_consumption / 60:.2f} minutes."
                ]
            else:
                self.result_dict[self.host] += [
                    f"{pre_check_task_result} time consuming {time_consumption:.2f} seconds."
                ]

        return True, "All pre-check tasks passed."

    def _parse_java_version(self, java_version_output):
        """
        This function is used to parse the Java version from the output of the "java -version" command.
        | 这个函数用于从“java -version”命令的输出中解析Java版本。
        """
        version_line = java_version_output.splitlines()[0]
        version_info = version_line.split()[2].strip('"')
        return version_info.split(".")

    def check_time_difference(self):
        """
        This function is used to check the time difference between the local machine and the remote machine.
        | 这个函数用于检查本地机器和远程机器的时间差异。
        """
        # 1. 获取本地时间
        local_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        # 2. 获取远程时间
        remote_time_command = "date +'%Y-%m-%d %H:%M:%S'"
        execute_result = execute_command(
            ssh_client = self.ssh_client,
            command = remote_time_command
        )
        if execute_result[0] != 0:
            return False, f"Failed to get remote time: {execute_result[1]}"
        remote_time = execute_result[1].strip()
        # 3. 计算时间差异
        time_difference = abs(
            datetime.strptime(remote_time, "%Y-%m-%d %H:%M:%S") - datetime.strptime(
                local_time, "%Y-%m-%d %H:%M:%S"
            )
        )

        # 4. 记录结果
        if time_difference.total_seconds() > 10:
            warning_handler(
                message = f"Time difference between local and remote host is too large: {time_difference}. "
                          f"Please ensure the time is synchronized."
            )
            return False, f"Time difference is too large: {time_difference}."
        info_handler(message = f"Host: {self.host} Time difference check passed. Time difference: {time_difference}.")
        return True, "Time difference check"

    def check_java_version(self):
        """
        This function is used to check the Java version on the remote machine.
        | 这个函数用于检查远程机器上的Java版本。
        """
        execute_result = execute_command(
            ssh_client = self.ssh_client,
            command = f"{load_env_command()} && java -version"
        )
        if execute_result[0] != 0:
            return False, f"Failed to get Java version: {execute_result[2]}"
        java_version_output = execute_result[2].strip()
        java_version = self._parse_java_version(java_version_output)

        if int(java_version[0]) != 1 and int(java_version[1]) != 8:
            error_handler(
                message = f"Host: {self.host} Unsupported Java version: {java_version_output}. "
                          f"Please install Java 8 or higher."
            )
            return False, f"Unsupported Java version: {java_version_output}."
        if "openjdk" in java_version_output.lower():
            info_handler(message = f"Host: {self.host} Using OpenJDK version: {'.'.join(java_version)}")
        else:
            if java_version[2] < "0_151" or java_version[2] > "0_371":
                """如果Java版本小于1.8.0_151或大于1.8.0_371，则提示用户修改Java版本"""
                error_handler(
                    message = f"Host: {self.host} Unsupported Java version: {java_version_output}. "
                              f"Please install Java 1.8.0_151 - 1.8.0_371 or openjdk"
                )
                return False, f"Unsupported Java version: {java_version_output}."
        info_handler(message = f"Host: {self.host} Java version check passed: {'.'.join(java_version)}")
        return True, "Java version check"

    def check_disk_permissions(self):
        """
        This function is used to check the disk permissions on the remote machine.
        | 这个函数用于检查远程机器上的磁盘权限。
        """
        if not self.path_tool.is_directory_exist(
                directory_path = deployment_config.deployment_dir
        ):
            debug_handler(message = f"Deployment directory does not exist: {deployment_config.deployment_dir}")
            # 需要创建目录
            execute_result = self.path_tool.create_directory(
                directory_path = deployment_config.deployment_dir
            )
            if not execute_result[0]:
                return False, f"Failed to create deployment directory: {execute_result[1]}"
        # 检查部署目录是否有写权限
        # 写入一个文件
        write_file_result = self.file_tool.write_file(
            file_path = os.path.join(deployment_config.deployment_dir, "test_write_permission.txt"),
            content = "This is a test file to check write permission.",
            file_type = "text"
        )
        if not write_file_result[0]:
            return False, f"Failed to write file: {write_file_result[1]}"
        # 删除文件
        delete_file_result = self.file_tool.delete_file(
            file_path = os.path.join(deployment_config.deployment_dir, "test_write_permission.txt")
        )
        if not delete_file_result[0]:
            return False, f"Failed to delete file: {delete_file_result[1]}"
        info_handler(message = f"Host: {self.host} Disk permission check passed: {deployment_config.deployment_dir}")
        return True, "Disk permission check"

    def check_disk_space(self):
        """
        This function is used to check the disk space on the remote machine.
        | 这个函数用于检查远程机器上的磁盘空间。
        """
        # 1. 获取磁盘使用率
        execute_result = execute_command(
            ssh_client = self.ssh_client,
            command = f"df -h {deployment_config.deployment_dir}"
        )
        if execute_result[0] != 0:
            return False, f"Failed to get disk usage: {execute_result[1]}"
        disk_usage_output = execute_result[1].strip()
        disk_usage_lines = disk_usage_output.splitlines()
        if len(disk_usage_lines) < 2:
            return False, "Failed to parse disk usage output."
        # 2. 获取磁盘可用空间
        available_space = disk_usage_lines[1].split()[3]
        available_space_size = int(available_space[:-1])
        # 3. 计算磁盘使用率
        if available_space[-1] == 'M':
            available_space_size = available_space_size / 1024
        elif available_space[-1] == 'T':
            available_space_size = available_space_size * 1024
        if available_space_size < 30:
            # error_handler(
            #     message = f"Available space is less than 30GB, Current available space is {available_space_size}GB."
            # )
            return False, f"Available space is less than 30GB: {available_space_size}GB."
        info_handler(
            message = f"Host: {self.host} Disk space check passed: {deployment_config.deployment_dir} ({available_space_size}GB available)."
        )
        return True, "Disk space check"

    def check_free_memory(self):
        """
        This function is used to check the free memory on the remote machine.
        | 这个函数用于检查远程机器上的可用内存。
        :return:
        """
        # 1. 获取内存使用率
        execute_result = execute_command(
            ssh_client = self.ssh_client,
            command = "free -h | grep Mem"
        )
        if execute_result[0] != 0:
            return False, f"Failed to get memory usage: {execute_result[1]}"
        memory_usage_output = execute_result[1].strip()
        parts = memory_usage_output.split()
        memory_used = parts[2][:-1]
        memory_total = parts[6][:-1]
        debug_handler(message = f"Memory used: {memory_used}, Memory total: {memory_total}")
        # 2. 判断可用内存是否大于 8GB

        memory_total_size = convert_to_number(memory_total)
        if memory_total_size < 8:
            return False, f"Available memory is less than 8GB: {memory_total_size}GB."
        info_handler(message = f"Host: {self.host} Memory check passed: {memory_total_size}GB available.")
        return True, "Memory check"


def pre_check_ssh_node(host, *args, **kwargs):
    """
    This function is used to check the environment before starting the SSH cluster.
    | 这个函数用于检查SSH集群启动前的环境。
    """
    result_dict = kwargs.get("result_dict", {})
    ssh_connect_start_time = time.time()
    ssh_client, sftp_client, connect_result = node_connect(host)
    if not ssh_client or not sftp_client:
        return False, connect_result
    result_dict[host] = []
    ssh_connect_end_time = time.time()
    ssh_connect_time_consumption = ssh_connect_end_time - ssh_connect_start_time
    if ssh_connect_time_consumption > 60:
        result_dict[host] += [
            f"SSH connection time consuming {ssh_connect_time_consumption / 60:.2f} minutes."
        ]
    else:
        result_dict[host] += [
            f"SSH connection time consuming {ssh_connect_time_consumption:.2f} seconds."
        ]

    try:
        return SSHClusterPreCheck(
            host = host,
            result_dict = result_dict,
            ssh_client = ssh_client,
            sftp_client = sftp_client,
        ).run()
    except Exception as e:
        debug_handler(message = f"Failed to check SSH cluster pre-check: {e}")
        debug_handler(message = traceback.format_exc())
        return False, f"Failed to check SSH cluster pre-check: {e}"
