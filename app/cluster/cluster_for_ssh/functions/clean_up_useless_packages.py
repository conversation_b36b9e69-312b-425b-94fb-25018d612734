#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : clean_up_useless_packages.py
# @Time    : 2025/06/09 09:36
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0
import json
import os
import sys
import time
import traceback
from app.config.deployment_config import deployment_config
from app.common.logging_utils import debug_handler, warning_handler, info_handler, error_handler
from app.cluster.cluster_for_ssh.tools.node_connect import node_connect
from app.cluster.cluster_for_ssh.tools.command_execution import execute_command
from app.cluster.cluster_for_ssh.tools.file_tool import FileTool
from app.cluster.cluster_for_ssh.tools.path_tool import PathTool

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class CleanUpUselessPackages:
    """
    Clean up useless packages. | 清理无用的软件包
    """

    def __init__(self, host, result_dict, ssh_client, sftp_client):
        self.host = host
        self.result_dict = result_dict
        self.ssh_client = ssh_client
        self.sftp_client = sftp_client
        self.file_tool = FileTool(sftp_client = sftp_client, ssh_client = ssh_client)
        self.path_tool = PathTool(sftp_client = sftp_client)

    def run(self):
        """
        Run the clean up useless packages. | 执行清理无用的软件包
        :return: None
        """
        for task in [
            self.clean_up_compressed_packages,  # 清理无用的软件包
            # 清理无用的解压文件
            self.clean_up_extracted
        ]:
            task_start_time = time.time()
            task_result, task_message = task()
            if not task_result:
                return False, task_message

            task_end_time = time.time()
            task_time_consumption = task_end_time - task_start_time
            if task_time_consumption > 60:
                self.result_dict[self.host] += [
                    f"{task_message} time consuming {task_time_consumption / 60:.2f} minutes."
                ]
            else:
                self.result_dict[self.host] += [
                    f"{task_message} time consuming {task_time_consumption:.2f} seconds."
                ]
        return True, "Clean up useless packages successfully."

    def clean_up_compressed_packages(self):
        """
        Clean up compressed packages. | 清理压缩包
        :return: None
        """
        # 直接清理掉 /{deployment_dir}/installation_packages/ 目录下的压缩包
        warning_handler(message = f"Clean up compressed packages on {self.host}.Please wait...")
        sub_file_list = self.path_tool.sub_file_list(
            directory_path = os.path.join(
                deployment_config.deployment_dir, "installation_packages"
            )
        )
        if not sub_file_list:
            info_handler(message = f"No compressed packages found on {self.host}.")
            return True, "No compressed packages found."

        delete_file_flag = True
        error_message = ""
        for file_name in sub_file_list:
            file_path = os.path.join(
                deployment_config.deployment_dir, "installation_packages", file_name
            )
            delete_file_status, delete_file_message = self.file_tool.delete_file(file_path)
            if not delete_file_status:
                delete_file_flag = False
                error_message += f"\tDelete compressed package {file_path} failed: {delete_file_message}\n"
                error_handler(message = f"Failed to delete compressed package {file_path}: {delete_file_message}")
                continue
        if not delete_file_flag:
            return False, "Failed to delete compressed packages."
        info_handler(message = f"Clean up compressed packages on {self.host} successfully.")
        return True, "Clean up compressed packages "

    def clean_up_extracted(self):
        """
        Clean up extracted files. | 清理解压文件
        :return: None
        """
        # 1. 获取远端解压目录下的所有子目录
        # 2. 获取远端软连接目录下的所有软连接的指向
        # 3. 获取当前目录下的记录文件
        # 4. 使用解压目录下面的所有文件，去除记录文件，去除软连接指向的文件
        # 5. 删除其余的解压文件
        warning_handler(message = f"Clean up extracted files on {self.host}.Please wait...")
        sub_directory_list = [os.path.join(
            deployment_config.deployment_dir, "current_package", sub_directory
        ) for sub_directory in self.path_tool.sub_directory_list(
            os.path.join(
                deployment_config.deployment_dir, "current_package"
            )
        )]
        if not sub_directory_list:
            info_handler(message = f"No extracted files found on {self.host}.")
            return True, "No extracted files found."

        # 获取远端软连接目录下的所有软连接的指向
        remote_link_list = self.path_tool.get_symlink(
            os.path.join(
                deployment_config.deployment_dir, "current"
            )
        )
        if not remote_link_list:
            debug_handler(message = f"No soft links found on {self.host}.")
        # 使用sub_directory_list去除 remote_link_list
        sub_directory_list = list(
            set(sub_directory_list) - set(
                [
                    os.path.dirname(link_path) for link_path in remote_link_list.values()
                ]
            )
        )

        # 判断是否有记录文件
        record_file_path = os.path.join(
            CURRENT_DIRECTORY, f"{self.host}_current_symlink.json"
        )
        if not os.path.isfile(record_file_path):
            record_symlink_dict = []
        else:
            with open(record_file_path, "r") as f:
                record_symlink_dict = json.load(f)
            record_symlink_dict = [
                os.path.dirname(link_path) for link_path in record_symlink_dict.values()
            ]
            debug_handler(message = f"Record symlink list: {record_symlink_dict}")
        # 去除记录文件
        sub_directory_list = list(
            set(sub_directory_list) - set(
                record_symlink_dict
            )
        )

        if not sub_directory_list:
            info_handler(message = f"No extracted files found on {self.host}.")
            return True, "No extracted files found."

        delete_dir_flag = True
        error_message = ""

        for sub_directory in sub_directory_list:
            # 去除记录文件
            debug_handler(message = f"Clean up extracted files in {sub_directory}.")
            status, stdout, stderr = execute_command(
                ssh_client = self.ssh_client,
                command = f"rm -r {sub_directory} || true"
            )
            if status != 0:
                delete_dir_flag = False
                error_message += f"\tDelete extracted files in {sub_directory} failed: {stderr}\n"
                error_handler(message = f"Failed to delete extracted files in {sub_directory}: {stderr}")
                continue
            debug_handler(message = f"Delete extracted files in {sub_directory} successfully.")
        if not delete_dir_flag:
            return False, error_message
        info_handler(message = f"Clean up extracted files on {self.host} successfully.")
        return True, "Clean up extracted files"


def clean_up_useless_packages(host, *args, **kwargs):
    """
    Clean up useless packages. | 清理无用的软件包
    :return: None
    """
    result_dict = kwargs.get("result_dict", {})
    ssh_connect_start_time = time.time()
    ssh_client, sftp_client, connect_result = node_connect(host)
    if not ssh_client or not sftp_client:
        return False, connect_result
    result_dict[host] = []
    ssh_connect_end_time = time.time()
    ssh_connect_time_consumption = ssh_connect_end_time - ssh_connect_start_time
    if ssh_connect_time_consumption > 60:
        result_dict[host] += [
            f"SSH connection time consuming {ssh_connect_time_consumption / 60:.2f} minutes."
        ]
    else:
        result_dict[host] += [
            f"SSH connection time consuming {ssh_connect_time_consumption:.2f} seconds."
        ]

    try:
        return CleanUpUselessPackages(
            host = host,
            result_dict = result_dict,
            ssh_client = ssh_client,
            sftp_client = sftp_client,
        ).run()
    except Exception as e:
        debug_handler(message = f"Failed to check SSH cluster pre-check: {e}")
        debug_handler(message = traceback.format_exc())
        return False, f"Failed to check SSH cluster pre-check: {e}"
