#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : ssh_service_manager.py
# @Time    : 2025/07/25
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

"""
SSH 模式的服务管理器实现
SSH mode service manager implementation
"""

import time
import traceback
from typing import List, Optional, Tuple
from app.common.service_manager import ServiceManager, ServiceOperationFactory
from app.config.deployment_config import service_operation_command
from app.common.logging_utils import debug_handler, error_handler, title_handler
from app.cluster.cluster_for_ssh.tools.command_execution import execute_command


class SSHServiceManager(ServiceManager):
    """
    SSH 模式的服务管理器
    SSH mode service manager
    """
    
    def __init__(self, host: str, ssh_client, sftp_client, **kwargs):
        self.ssh_client = ssh_client
        self.sftp_client = sftp_client
        super().__init__(host, **kwargs)
    
    def _execute_operation_impl(self) -> <PERSON><PERSON>[bool, str]:
        """
        执行具体的服务操作
        Execute specific service operation
        """
        operation_map = {
            "start": ServiceOperationFactory.create_start_operation,
            "stop": ServiceOperationFactory.create_stop_operation,
            "restart": ServiceOperationFactory.create_restart_operation,
            "status": ServiceOperationFactory.create_status_operation,
        }
        
        operation_factory = operation_map.get(self.operation_type)
        if not operation_factory:
            return False, f"Unsupported operation: {self.operation_type}"
        
        title_handler(message = f"{self.operation_type.capitalize()} services on host: {self.host}".center(80, "*"))
        
        operation = operation_factory(self)
        return operation.execute()
    
    def get_service_status(self, service_name: str) -> Optional[List[str]]:
        """
        获取服务状态（进程ID列表）
        Get service status (list of process IDs)
        """
        command = service_operation_command(service_name = service_name, operation_name = "status")
        if not command:
            debug_handler(message = f"No status command found for service: {service_name} on host: {self.host}")
            return None
        
        try:
            recode, output, error = execute_command(
                ssh_client = self.ssh_client,
                command = command
            )
            debug_handler(
                message = f"Status command executed: {command}, recode: {recode}, output: {output}, error: {error}"
            )
            
            if recode != 0 and not output:
                return None
            
            if output:
                return [line.strip() for line in output.splitlines() if line.strip()]
            else:
                return None
                
        except Exception as e:
            debug_handler(message = f"Failed to get service status for {service_name} on host {self.host}: {e}")
            return None
    
    def start_service(self, service_name: str) -> Tuple[bool, str]:
        """
        启动服务
        Start service
        """
        command = service_operation_command(service_name = service_name, operation_name = "start")
        if not command:
            return False, f"No start command found for service: {service_name}"
        
        debug_handler(message = f"Starting service `{service_name}` on host {self.host}: {command}")
        
        try:
            recode, output, error = execute_command(
                ssh_client = self.ssh_client,
                command = command
            )
            debug_handler(
                message = f"Start command executed: {command}, recode: {recode}, output: {output}, error: {error}"
            )
            
            if recode != 0:
                return False, f"Start command failed with code {recode}: {error}"
            
            return True, f"Service {service_name} started successfully"
            
        except Exception as e:
            error_handler(message = f"Failed to start service {service_name} on host {self.host}: {e}")
            debug_handler(message = traceback.format_exc())
            return False, str(e)
    
    def stop_service(self, service_name: str) -> Tuple[bool, str]:
        """
        停止服务
        Stop service
        """
        # 获取服务进程ID
        pids = self.get_service_status(service_name)
        if not pids:
            return True, f"Service {service_name} is not running"
        
        # 使用 kill 命令停止服务
        stop_command = "kill -9 " + " ".join(pids)
        debug_handler(message = f"Stopping service `{service_name}` on host {self.host}: {stop_command}")
        
        try:
            recode, output, error = execute_command(
                ssh_client = self.ssh_client,
                command = stop_command
            )
            debug_handler(
                message = f"Stop command executed: {stop_command}, recode: {recode}, output: {output}, error: {error}"
            )
            
            if recode != 0:
                return False, f"Stop command failed with code {recode}: {error}"
            
            return True, f"Service {service_name} stopped successfully"
            
        except Exception as e:
            error_handler(message = f"Failed to stop service {service_name} on host {self.host}: {e}")
            debug_handler(message = traceback.format_exc())
            return False, str(e)


def ssh_service_operation(host, *args, **kwargs):
    """
    SSH 模式的服务操作入口函数
    SSH mode service operation entry function
    """
    from app.cluster.cluster_for_ssh.tools.node_connect import node_connect
    
    result_dict = kwargs.get("result_dict", {})
    
    # 建立 SSH 连接
    ssh_connect_start_time = time.time()
    ssh_client, sftp_client, connect_result = node_connect(host)
    if not ssh_client or not sftp_client:
        return False, connect_result
    
    result_dict[host] = []
    ssh_connect_end_time = time.time()
    ssh_connect_time_consumption = ssh_connect_end_time - ssh_connect_start_time
    
    if ssh_connect_time_consumption > 60:
        result_dict[host].append(
            f"SSH connection time: {ssh_connect_time_consumption / 60:.2f} minutes"
        )
    else:
        result_dict[host].append(
            f"SSH connection time: {ssh_connect_time_consumption:.2f} seconds"
        )
    
    # 创建服务管理器并执行操作
    manager = SSHServiceManager(host, ssh_client, sftp_client, **kwargs)
    return manager.execute_operation()


# 为了保持向后兼容性，保留原有的函数名
def service_operation(host, *args, **kwargs):
    """
    保持向后兼容性的函数
    Function for backward compatibility
    """
    return ssh_service_operation(host, *args, **kwargs)
