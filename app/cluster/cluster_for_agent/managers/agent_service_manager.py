#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : agent_service_manager.py
# @Time    : 2025/07/25
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

"""
Agent 模式的服务管理器实现
Agent mode service manager implementation
"""

import time
import traceback
from typing import List, Optional, Tuple
from app.common.service_manager import ServiceManager, ServiceOperationFactory
from app.config.deployment_config import service_operation_command
from app.common.logging_utils import debug_handler, error_handler, warning_handler, title_handler
from app.cluster.cluster_for_agent.tools.node_connect import NodeAgentConnect


class AgentServiceManager(ServiceManager):
    """
    Agent 模式的服务管理器
    Agent mode service manager
    """
    
    def __init__(self, host: str, agent_client, **kwargs):
        self.agent_client = agent_client
        super().__init__(host, **kwargs)
    
    def _execute_operation_impl(self) -> <PERSON><PERSON>[bool, str]:
        """
        执行具体的服务操作
        Execute specific service operation
        """
        if self.operation_type == "logs":
            return self._get_logs()
        
        operation_map = {
            "start": ServiceOperationFactory.create_start_operation,
            "stop": ServiceOperationFactory.create_stop_operation,
            "restart": ServiceOperationFactory.create_restart_operation,
            "status": ServiceOperationFactory.create_status_operation,
        }
        
        operation_factory = operation_map.get(self.operation_type)
        if not operation_factory:
            return False, f"Unsupported operation: {self.operation_type}"
        
        warning_handler(
            message = f"{self.operation_type} service operation is starting on {self.host}. please wait..."
        )
        
        # 准备参数并调用 Agent
        operation_kwargs = self.kwargs.copy()
        operation_kwargs["service_operation_command"] = service_operation_command
        operation_kwargs["user_input_services"] = self.target_services
        
        try:
            service_operation_result = self.agent_client.root.service_operation(**operation_kwargs)
            
            if not service_operation_result[0]:
                return False, service_operation_result[1]
            
            # 处理结果
            for service, operation_result in service_operation_result[1].items():
                status = operation_result.get("status")
                message = f"Host: {self.host} Service: {operation_result.get('message')}"
                
                if status == "warning":
                    warning_handler(message = message)
                elif status == "error":
                    error_handler(message = message)
                else:
                    debug_handler(message = message)
            
            return True, "Agent service operation completed successfully"
            
        except Exception as e:
            error_handler(message = f"Agent service operation failed on {self.host}: {e}")
            debug_handler(message = traceback.format_exc())
            return False, str(e)
    
    def get_service_status(self, service_name: str) -> Optional[List[str]]:
        """
        获取服务状态（进程ID列表）
        Get service status (list of process IDs)
        
        注意：Agent 模式下这个方法主要用于接口兼容性，
        实际的状态查询通过 Agent 的 service_operation 方法处理
        """
        try:
            # 这里可以调用 Agent 的状态查询方法
            # 但由于 Agent 模式的架构，通常通过统一的 service_operation 处理
            return None
        except Exception as e:
            debug_handler(message = f"Failed to get service status for {service_name} on host {self.host}: {e}")
            return None
    
    def start_service(self, service_name: str) -> Tuple[bool, str]:
        """
        启动服务
        Start service
        
        注意：Agent 模式下这个方法主要用于接口兼容性，
        实际的服务启动通过 Agent 的 service_operation 方法处理
        """
        return True, f"Service {service_name} start operation delegated to agent"
    
    def stop_service(self, service_name: str) -> Tuple[bool, str]:
        """
        停止服务
        Stop service
        
        注意：Agent 模式下这个方法主要用于接口兼容性，
        实际的服务停止通过 Agent 的 service_operation 方法处理
        """
        return True, f"Service {service_name} stop operation delegated to agent"
    
    def _get_logs(self) -> Tuple[bool, str]:
        """
        获取服务日志
        Get service logs
        """
        service_name = self.kwargs.get("user_input_services", "api")
        
        # 判断远端是否已存在日志文件
        service_logs_file_path = service_operation_command(
            service_name = service_name,
            operation_name = "logs"
        )
        
        try:
            if not self.agent_client.root.is_exist_file(service_logs_file_path):
                error_message = f"The {service_name} service log file does not exist on {self.host}."
                error_handler(message = error_message)
                return False, error_message
            
            # 获取日志内容
            for line in self.agent_client.root.tail_log(service_logs_file_path, 200):
                print(line)
            
            return True, f"Successfully retrieved logs for {service_name} from {self.host}"
            
        except Exception as e:
            error_message = f"Failed to get logs for {service_name} from {self.host}: {e}"
            error_handler(message = error_message)
            debug_handler(message = traceback.format_exc())
            return False, error_message


def agent_service_operation(host, *args, **kwargs):
    """
    Agent 模式的服务操作入口函数
    Agent mode service operation entry function
    """
    result_dict = kwargs.get("result_dict", {})
    
    # 建立 Agent 连接
    agent_connect_start_time = time.time()
    node_agent_connect = NodeAgentConnect(node_ip = host)
    agent_client, agent_connect_result = node_agent_connect.get_node_conn()
    
    if not agent_client:
        return False, agent_connect_result
    
    result_dict[host] = []
    kwargs["result_dict"] = result_dict
    
    agent_connect_end_time = time.time()
    agent_connect_time_consumption = agent_connect_end_time - agent_connect_start_time
    
    if agent_connect_time_consumption > 60:
        result_dict[host].append(
            f"Agent connection time: {agent_connect_time_consumption / 60:.2f} minutes"
        )
    else:
        result_dict[host].append(
            f"Agent connection time: {agent_connect_time_consumption:.2f} seconds"
        )
    
    # 创建服务管理器并执行操作
    manager = AgentServiceManager(host, agent_client, **kwargs)
    return manager.execute_operation()


# 为了保持向后兼容性，保留原有的函数名
def service_operation(host, *args, **kwargs):
    """
    保持向后兼容性的函数
    Function for backward compatibility
    """
    return agent_service_operation(host, *args, **kwargs)
