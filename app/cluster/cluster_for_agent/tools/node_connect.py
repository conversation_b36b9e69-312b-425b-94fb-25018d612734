#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : node_connect.py
# @Time    : 2025/06/09 11:21
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0
import json
import os
import sys
import traceback

import rpyc

from app.config.deployment_config import deployment_config
from app.common.logging_utils import debug_handler
from app.cluster.cluster_for_agent.tools.crypto_utils import SecureChannel

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class NodeAgentConnect:
    """
    This class is used to connect to the node agent.
    """

    def __init__(self, node_ip):
        self.node_ip = node_ip
        self.conn = None
        self.conn_message = None

        self.crypto = SecureChannel(
            master_key = "WhaleStudios2025"
        )

    def connect_node(self):
        """
        Connect to the node with the specified IP address.
        :param node_ip:
        """
        node_connect_port = deployment_config.cluster_nodes.get(
            self.node_ip, {}
        ).get(
            "agent_port", 18888
        )
        try:
            self.conn = rpyc.connect(
                host = self.node_ip,
                port = node_connect_port,
                config = {
                    "sync_request_timeout": 6000,
                }
            )
            debug_handler(message = f"Successfully connected to node {self.node_ip}.")
            return True, "Connection successful."
        except Exception as e:
            debug_handler(message = f"Failed to connect to node {self.node_ip}. Error: {e}.")
            debug_handler(message = traceback.format_exc())

            return False, f"Connection failed. Error: {e}"

    def node_login(self):
        """
        Login to the node.
        :return:
        """
        try:
            response = self.conn.root.login(
                json.dumps(
                    {
                        "username": "admin",
                        "password": "dolphinscheduler123"
                    }
                )
            )
            response_dict = json.loads(response)
            if not response_dict.get("success"):
                return False, response_dict.get("response")
            else:
                debug_handler(message = f"Successfully logged in to node {self.node_ip}.")
        except Exception as e:
            debug_handler(message = f"Failed to login to node {self.node_ip}. Error: {e}.")
            debug_handler(message = traceback.format_exc())
            return False, f"Failed to login to node. Error: {e}"
        return self.conn, "Login successfully."

    def get_node_conn(self):
        node_connect_status, node_connect_message = self.connect_node()
        if not node_connect_status:
            return False, node_connect_message
        return self.node_login()
