#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : package_download.py
# @Time    : 2025/06/09 18:42
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys
import threading
import traceback
import base64

from http.server import HTTPServer, BaseHTTPRequestHandler

from app.config.deployment_config import deployment_config
from app.common.logging_utils import debug_handler, error_handler, info_handler

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class DirectoryListingHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        # 检查认证
        client_ip = self.client_address[0]
        request_path = self.path
        info_handler(message = f"Client {client_ip} requested path: {request_path}")
        if not self.authenticate():
            self.send_response(401)
            self.send_header("WWW-Authenticate", 'Basic realm="File Download"')
            self.end_headers()
            self.wfile.write(b"401 - Unauthorized")
            info_handler(message = f"Client {client_ip} authentication failed.")
            return

        # 获取请求路径
        path = self.path[1:]  # 去掉开头的斜杠
        full_path = os.path.join(deployment_config.package_dir, path)

        if os.path.isdir(full_path):
            # 如果是目录，返回文件列表
            self.send_response(200)
            self.send_header("Content-type", "text/html")
            self.end_headers()
            self.wfile.write(self.generate_directory_listing(full_path).encode("utf-8"))
            info_handler(message = f"Client {client_ip} requested directory: {full_path}")
        elif os.path.isfile(full_path):
            # 如果是文件，返回文件内容
            self.send_response(200)
            self.send_header("Content-type", "application/octet-stream")
            self.send_header("Content-Disposition", f'attachment; filename="{os.path.basename(full_path)}"')
            self.end_headers()
            with open(full_path, "rb") as f:
                self.wfile.write(f.read())
            info_handler(message = f"Client {client_ip} requested file: {full_path}")
        else:
            # 路径不存在，返回 404
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b"404 - Not Found")

    def authenticate(self):
        """验证请求的认证信息"""
        auth_header = self.headers.get("Authorization")
        if auth_header:
            # 解码认证信息
            auth_type, auth_token = auth_header.split(" ", 1)
            if auth_type.lower() == "basic":
                decoded_token = base64.b64decode(auth_token).decode("utf-8")
                username, password = decoded_token.split(":", 1)
                # 检查用户名和密码是否匹配
                if username == "admin" and password == "dolphinscheduler123":  # 替换为你的认证逻辑
                    return True
        return False

    def generate_directory_listing(self, path):
        """生成目录列表的 HTML 页面"""
        files = os.listdir(path)
        html = "<html><head><title>Directory Listing</title></head><body>"
        html += "<h1>Directory Listing</h1>"
        html += "<ul>"
        for file in files:
            file_path = os.path.join(path, file)
            if os.path.isdir(file_path):
                html += f'<li><a href="{file}/">{file}/</a></li>'
            else:
                html += f'<li><a href="{file}">{file}</a></li>'
        html += "</ul></body></html>"
        return html


class FileDownloadServer:
    # def __init__(self, port, directory):
    #     self.port = port
    #     self.directory = directory
    #     self.server = None
    #     self.thread = None

    def start(self):
        try:
            # 切换到指定目录
            os.chdir(deployment_config.package_dir)
            # 启动 HTTP 服务
            self.server = HTTPServer(
                (deployment_config.local_ip, deployment_config.download_port), DirectoryListingHandler
            )
            self.thread = threading.Thread(target = self.server.serve_forever)
            self.thread.start()
            debug_handler(
                message = f"File download server started. Listening on port {deployment_config.download_port}."
            )
        except Exception as e:
            error_handler(message = f"Failed to start file download server. {e}")
            debug_handler(message = traceback.format_exc())
            sys.exit(1)

    def stop(self):
        if self.server:
            # 关闭 HTTP 服务
            self.server.shutdown()
            self.server.server_close()
            self.thread.join()
            debug_handler(message = "File download server stopped.")
