#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : clean_up_useless_packages.py
# @Time    : 2025/06/11 15:50
# <AUTHOR> chenyi<PERSON>i
# @Version : 1.0

import os
import sys
import time

from app.config.deployment_config import deployment_config
from app.common.logging_utils import warning_handler
from app.cluster.cluster_for_agent.tools.node_connect import NodeAgentConnect

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def clean_up_useless_packages(host, *args, **kwargs):
    """
    Clean up useless packages on the remote host.
    :param host: The remote host to clean up.
    :param args: The arguments of the function.
    :param kwargs: The keyword arguments of the function.
    :return: None.
    """
    warning_handler(message = f"Clean up useless packages on host {host}. Please wait...")
    result_dict = kwargs.get("result_dict", {})
    agent_connect_start_time = time.time()
    node_agent_connect = NodeAgentConnect(node_ip = host)
    agent_client, agent_connect_result = node_agent_connect.get_node_conn()
    if not agent_client:
        return False, agent_connect_result
    result_dict[host] = []
    agent_connect_end_time = time.time()
    agent_connect_time_consumption = agent_connect_end_time - agent_connect_start_time
    if agent_connect_time_consumption > 60:
        result_dict[host] += [
            f"Agent connect time consumption {agent_connect_time_consumption / 60:.2f} minutes."
        ]
    else:
        result_dict[host] += [
            f"Agent connect time consumption {agent_connect_time_consumption:.2f} seconds."
        ]

    # 远程主机清理结果
    clean_up_result = agent_client.root.clean_up_useless_packages(
        deployment_config.deployment_dir
    )
    if clean_up_result[0]:
        result_dict[host] += clean_up_result[1]
    else:
        return False, clean_up_result[1]
    return True, ""
