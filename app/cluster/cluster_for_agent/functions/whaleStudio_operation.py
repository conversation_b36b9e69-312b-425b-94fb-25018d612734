#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_operation.py
# @Time    : 2025/06/11 10:31
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import time
import traceback
import json
from app.config.deployment_config import service_operation_command
from app.common.logging_utils import error_handler, debug_handler, warning_handler, info_handler
from app.cluster.cluster_for_agent.tools.node_connect import NodeAgentConnect
from app.cluster.config.cluster_node_config import get_services_by_ip

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class WhaleStudioServiceOperation:
    """
    Whale Studio service operation class.
    """

    def __init__(self, host, agent_client, **kwargs):
        self.host = host
        self.result_dict = kwargs.get("result_dict", {})
        self.agent_client = agent_client
        self.kwargs = kwargs
        self.operation_type = kwargs.get("operation_type")

    def run(self):
        """
        Run the Whale Studio service operation.
        :return:
        """
        if self.operation_type == "logs":
            self.get_logs()
            return True, "Whale Studio service operation is successful."
        warning_handler(
            message = f"{self.kwargs.get('operation_type')} service operation is starting on {self.host}. please wait..."
        )
        self.kwargs["service_operation_command"] = service_operation_command
        start_time = time.time()

        user_input_services = self.kwargs.get("user_input_services", "api")
        use_node_roles = self.kwargs.get("use_node_roles", False)
        node_services = get_services_by_ip(self.host)

        # 根据节点角色配置处理服务列表
        if use_node_roles and user_input_services == "all":
            # 当用户输入 "all" 且需要根据节点角色过滤时，使用节点的角色配置
            new_user_input_services = node_services
            debug_handler(
                message = f"Host: {self.host} - Using node roles configuration: {new_user_input_services}"
            )
        elif isinstance(user_input_services, list):
            # 用户指定了具体服务，需要与节点角色配置取交集
            new_user_input_services = []
            for service in node_services:
                if service in user_input_services:
                    new_user_input_services.append(service)

            # 记录被过滤掉的服务
            filtered_services = set(user_input_services) - set(new_user_input_services)
            if filtered_services:
                debug_handler(
                    message = f"Host: {self.host} - Services filtered out (not in node roles): {list(filtered_services)}"
                )

            debug_handler(
                message = f"Host: {self.host} - Final service list after role filtering: {new_user_input_services}"
            )
        else:
            # 兼容旧的逻辑
            new_user_input_services = []
            for service in node_services:
                if service in user_input_services:
                    new_user_input_services.append(service)

        self.kwargs["user_input_services"] = new_user_input_services

        service_operation_result = self.agent_client.root.service_operation(
            **self.kwargs
        )
        end_time = time.time()
        time_consumption = end_time - start_time
        if time_consumption > 60:
            self.result_dict[self.host] += [
                f"{self.kwargs.get('operation_type')} service operation time consumption {time_consumption / 60:.2f} minutes."
            ]
        else:
            self.result_dict[self.host] += [
                f"{self.kwargs.get('operation_type')} service operation time consumption {time_consumption:.2f} seconds."
            ]
        if not service_operation_result[0]:
            return False, service_operation_result[1]
        for service, operation_result in service_operation_result[1].items():
            status = operation_result.get("status")
            message = f"Host: {self.host} Service: {operation_result.get('message')}"

            match status:
                case "warning":
                    warning_handler(message = message)
                case "error":
                    error_handler(message = message)
                case "success":
                    info_handler(message = message)
        return True, "Whale Studio service operation is successful."

    def get_logs(self):
        service_name = self.kwargs.get("user_input_services", "api")
        # 1. 判断远端是否已存在日志文件
        service_logs_file_path = service_operation_command(
            service_name = service_name,
            operation_name = "logs"
        )
        if not self.agent_client.root.is_exist_file(service_logs_file_path):
            error_handler(message = f"The {service_name} service does not exist on {self.host}.")
            return False, f"The {service_name} service does not exist on {self.host}."
            # 2. 请求
        for line in self.agent_client.root.tail_log(
                service_logs_file_path,
                200
        ):
            print(line)


def service_operation(host, *args, **kwargs):
    """
    Perform service operations on the specified host.
    :param host:
    :param args:
    :param kwargs:
    :return:
    """

    result_dict = kwargs.get("result_dict", {})
    agent_connect_start_time = time.time()
    node_agent_connect = NodeAgentConnect(node_ip = host)
    agent_client, agent_connect_result = node_agent_connect.get_node_conn()
    if not agent_client:
        return False, agent_connect_result
    result_dict[host] = []
    kwargs["result_dict"] = result_dict
    agent_connect_end_time = time.time()
    agent_connect_time_consumption = agent_connect_end_time - agent_connect_start_time
    if agent_connect_time_consumption > 60:
        result_dict[host] += [
            f"Agent connect time consumption {agent_connect_time_consumption / 60:.2f} minutes."
        ]
    else:
        result_dict[host] += [
            f"Agent connect time consumption {agent_connect_time_consumption:.2f} seconds."
        ]
    try:
        return WhaleStudioServiceOperation(
            host, agent_client, **kwargs
        ).run()
    except Exception as e:
        error_handler(message = f"Failed to install Whale Studio on {host}. Error: {e}")
        debug_handler(message = traceback.format_exc())
        return False, f"Failed to install Whale Studio on {host}."
