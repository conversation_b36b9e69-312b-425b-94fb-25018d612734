#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_db_operation.py
# @Time    : 2025/06/11 14:13
# <AUTHOR> chenyi<PERSON>i
# @Version : 1.0
import json
import os
import sys
import threading
import time
import traceback

from app.common.logging_utils import error_handler, debug_handler, info_handler, warning_handler
from app.config.deployment_config import deployment_config, load_env_command
from app.config.scheduler_config import metabase_logs_save_path, metabase_initialization_config

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class DBOperation:
    def __init__(self, operation_type, agent_client, show_log = False):
        self.operation_type = operation_type
        self.agent_client = agent_client
        self.show_log = show_log

        self.metabase_logs_save_path = os.getenv(
            "LOGS_PATH", os.path.join(
                metabase_logs_save_path(), f"{operation_type}.log"
            )
        )

        self.metabase_init_path = os.path.join(
            deployment_config.deployment_dir,
            "current",
            "whalestudio",
            "tools",
            "bin",
        )
        self.stop_log_thread = threading.Event()

    def run(self):

        # 1. 修改远端数据库初始化配置文件
        metabase_config_path = os.path.join(
            deployment_config.deployment_dir,
            "current",
            "whalestudio",
            "tools",
            "conf",
            "whalescheduler_env.sh"
        )

        update_metabase_config_result = self.agent_client.root.modify_file(
            file_type = "text",
            file_path = metabase_config_path,
            content = metabase_initialization_config()
        )

        if not update_metabase_config_result[0]:
            error_handler(
                message = f"Modify the metabase initialization configuration file failed.Error: {update_metabase_config_result[1]}"
            )
            return False, update_metabase_config_result[1]

        match self.operation_type:
            case "db_init":
                display_name = "Initialize Metabase"
            case "db_upgrade":
                display_name = "Upgrade Metabase"
            case _:
                error_handler(message = "Database operation type error!")
                sys.exit(1)

        # 启动一个独立线程
        # log_thread = threading.Thread(
        #     target = self._tail_logs,
        # )
        # log_thread.daemon = True  # 作用: 主线程结束时, 子线程也结束
        # log_thread.start()

        if self.show_log:
            warning_handler(
                message = f"Sorry, Agent deployment mode {display_name} does not currently support real-time log output. Please be patient .."
            )

        metabase_operation_result = self.agent_client.root.metabase_operation(
            f"cd {self.metabase_init_path} && {load_env_command()} && /bin/bash upgrade-schema.sh"
        )
        self.stop_log_thread.set()
        try:
            if not metabase_operation_result[0]:
                error_handler(message = f"{display_name} failed!")
                error_handler(message = "Please check the log file for more details.")
                error_handler(message = f"Log file path: {self.metabase_logs_save_path}")
                return False, metabase_operation_result[1]
            info_handler(message = f"{display_name} succeeded!")
            warning_handler(message = f"Log file path: {self.metabase_logs_save_path}")
            return True, ""
        except Exception as e:
            error_handler(message = "Failed to execute the metabase operation.")
            error_handler(message = str(e))
            return False, str(e)
        finally:
            # self.stop_log_thread.set()
            # log_thread.join()
            try:
                with open(self.metabase_logs_save_path, "w") as f:
                    f.write(metabase_operation_result[1])
            except Exception as e:
                debug_handler(message = "Failed to save the log file of metabase initialization.")
                debug_handler(message = str(e))
                debug_handler(message = traceback.format_exc())

            if self.show_log:
                sys.stdout.flush()

    # def _tail_logs(self):
    #     """
    #     Tail the log file of metabase initialization.
    #     :return:
    #     """
    #
    #     # 去请求agent_client获取日志文件路径
    #     log_file_is_exist = False
    #     for i in range(10):
    #         if not self.agent_client.root.is_exist_file(
    #                 os.path.join(
    #                     self.metabase_init_path, "logs", "whalescheduler-tools.log"
    #                 )
    #         ):
    #             p
    #             time.sleep(1)
    #         else:
    #             log_file_is_exist = True
    #             break
    #     if not log_file_is_exist:
    #         error_handler(message = "Failed to get the log file path of metabase initialization.")
    #         return
    #
    #     for line in self.agent_client.root.tail_log(
    #             os.path.join(
    #                 self.metabase_init_path, "logs", "whalescheduler-tools.log"
    #             ),
    #             200
    #     ):
    #
    #         if self.show_log:
    #             print(line, flush = True)
