#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_uninstall.py
# @Time    : 2025/06/11 16:35
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import time
import traceback
from app.config.deployment_config import deployment_config
from app.common.logging_utils import error_handler, debug_handler
from app.cluster.cluster_for_agent.tools.node_connect import NodeAgentConnect
from app.cluster.cluster_for_agent.functions.whaleStudio_operation import WhaleStudioServiceOperation

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def whaleStudio_uninstall(host, *args, **kwargs):
    """
    :param host:
    :param args:
    :param kwargs:
    :return:
    """
    result_dict = kwargs.get("result_dict", {})
    agent_connect_start_time = time.time()
    node_agent_connect = NodeAgentConnect(node_ip = host)
    agent_client, agent_connect_result = node_agent_connect.get_node_conn()
    if not agent_client:
        return False, agent_connect_result
    result_dict[host] = []
    kwargs["result_dict"] = result_dict
    agent_connect_end_time = time.time()
    agent_connect_time_consumption = agent_connect_end_time - agent_connect_start_time
    if agent_connect_time_consumption > 60:
        result_dict[host] += [
            f"Agent connect time consumption {agent_connect_time_consumption / 60:.2f} minutes."
        ]
    else:
        result_dict[host] += [
            f"Agent connect time consumption {agent_connect_time_consumption:.2f} seconds."
        ]

    kwargs["operation_type"] = "stop"

    stop_service_status, stop_service_result = WhaleStudioServiceOperation(
        host = host,
        agent_client = agent_client,
        **kwargs
    ).run()
    if not stop_service_status:
        return False, stop_service_result

    start_time = time.time()
    try:
        uninstall_result = agent_client.root.uninstall_whale_studio(deployment_config.deployment_dir)
        if not uninstall_result[0]:
            return False, uninstall_result[1]
    except Exception as e:
        error_handler(message = f"Failed to uninstall whale studio on {host}. Error: {e}")
        debug_handler(message = traceback.format_exc())

    uninstall_time_consumption = time.time() - start_time
    if uninstall_time_consumption > 60:
        result_dict[host] += [
            f"Uninstall time consumption {uninstall_time_consumption / 60:.2f} minutes."
        ]
    else:
        result_dict[host] += [
            f"Uninstall time consumption {uninstall_time_consumption:.2f} seconds."
        ]
    return True, f"Host {host} uninstall successfully."
