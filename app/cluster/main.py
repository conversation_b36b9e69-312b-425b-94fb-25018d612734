#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : main.py
# @Time    : 2025/06/03 17:03
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import subprocess
import sys
import time
import concurrent
import traceback
from multiprocessing import Manager
from datetime import datetime
from concurrent.futures import ProcessPoolExecutor, as_completed

from app.config.deployment_config import deployment_config
from app.common.package import get_latest_package
from app.common.logging_utils import info_handler, error_handler, debug_handler, warning_handler, title_handler, console
from app.common.utils import task_running_time
from app.cluster.config.cluster_node_config import get_ip_by_service

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class ClusterMain:
    """
    Cluster main class. | 集群主类
    """

    def __init__(self, operation_type, *args, **kwargs):
        """
        Init the cluster main class. | 初始化集群主类
        :param operation_type:
        :param args:
        :param kwargs:
        """
        self.operation_type = operation_type
        self.kwargs = kwargs
        if not deployment_config.cluster_nodes:
            error_handler(message = "No cluster nodes found in the configuration file.")
            error_handler(message = "Please configure the cluster nodes before executing the operation.")
            sys.exit(1)
        self.default_host_lists = list(deployment_config.cluster_nodes.keys())
        # 用户提供的主机列表
        self.user_input_hosts = self.kwargs.get('host', "")
        if not self.user_input_hosts:
            self.valid_host_lists = self.default_host_lists
        else:
            self.user_input_hosts = self.user_input_hosts.split(",")
            # 不合法的主机列表
            self.invalid_host_lists = set(self.user_input_hosts) - set(self.default_host_lists)
            # 合法的主机列表
            self.valid_host_lists = list(set(self.user_input_hosts) - self.invalid_host_lists)
            # 合法的主机列表优先级更高
            if self.invalid_host_lists:
                error_handler(message = "Invalid host list: ")
                for host in self.invalid_host_lists:
                    error_handler(message = f"    - {host}")
            if self.invalid_host_lists and not self.valid_host_lists:
                error_handler(
                    message = "After excluding invalid hosts, the list of valid hosts is empty. Please check the configuration file or input host list."
                )
                sys.exit(1)
            if not self.invalid_host_lists and not self.valid_host_lists:
                error_handler(
                    message = "The list of valid hosts is empty. Please check the configuration file or input host list."
                )
                sys.exit(1)

        manager = Manager()
        self.failed_nodes = manager.dict()
        self.result_dict = manager.dict()

        self.process_pool_size = min(deployment_config.process_pool_size, len(self.valid_host_lists))
        self.kwargs['result_dict'] = self.result_dict
        self.download_server = None

    @task_running_time(task_name = "Cluster Operation")
    def run(self):
        """
        Run the cluster main class. | 运行集群主类
        :return:
        """
        operation_descriptions = {
            "install": "Install WhaleStudio on the cluster nodes",
            "pre_check": "Pre-check the cluster nodes before deployment",
            "uninstall": "Uninstall WhaleStudio from the cluster nodes",
            "start": "Start the WhaleStudio service on the cluster nodes",
            "stop": "Stop the WhaleStudio service on the cluster nodes",
            "status": "Check the status of the WhaleStudio service on the cluster nodes",
            "restart": "Restart the WhaleStudio service on the cluster nodes",
            "config_update": "Update the WhaleStudio configuration on the cluster nodes",
            "clean_packages": "Clean the WhaleStudio packages on the cluster nodes",
            "logs": "Get the WhaleStudio logs from the cluster nodes",
            "db_init": "Initialize the Metabase database on the cluster nodes",
            "db_upgrade": "Upgrade the Metabase database on the cluster nodes",
            "rollback": "Rollback the WhaleStudio service on the cluster nodes"
        }
        title_handler(
            message = f"{operation_descriptions.get(self.operation_type, 'Unknown Operation').center(100, '=')}"
        )

        if self.operation_type == "logs":
            self.kwargs['operation_type'] = "logs"
            service_name = self.kwargs.get('operation_value', "api")
            self.kwargs['user_input_services'] = service_name
            # 获取服务所在的主机列表
            ip_bay_service = get_ip_by_service(service_name)
            if not ip_bay_service:
                error_handler(
                    message = f"The service {service_name} is not running on any node. Please check the service status."
                )
                sys.exit(1)
            # 判断用户提供的主机列表是否包含服务所在的主机
            if not set(self.valid_host_lists) & set(ip_bay_service):
                error_handler(
                    message = f"The service {service_name} is not running on any node in the input host list. Please check the service status or input host list."
                )
                sys.exit(1)
            # 如果用户仅提供了一个主机列表,且该主机列表不包含服务所在的主机,则使用服务所在的主机列表
            if len(self.valid_host_lists) == 1 and self.valid_host_lists[0] not in ip_bay_service:
                error_handler(
                    message = f"The service {service_name} is not running on the input host {self.valid_host_lists[0]}. Using the service host list instead."
                )
                sys.exit(1)
            # 获取交集
            self.valid_host_lists = list(set(self.valid_host_lists) & set(ip_bay_service))
            debug_handler(message = f"Valid hosts for service {service_name}: {self.valid_host_lists}")
            if len(self.valid_host_lists) > 1:
                # 需要用户选择其一
                warning_handler(
                    message = f"Multiple nodes ({len(self.valid_host_lists)}) are running the service `{service_name}`. Please specify the node to operate on."
                )
                for index in range(5):
                    for i, host in enumerate(self.valid_host_lists):
                        info_handler(message = f"{i + 1}. {host}")
                    user_input = input("Please enter the number of the node to operate on: ")
                    if user_input.isdigit() and int(user_input) > 0 and int(user_input) <= len(self.valid_host_lists):
                        self.valid_host_lists = [self.valid_host_lists[int(user_input) - 1]]
                        break
                    else:
                        error_handler(
                            message = "Invalid input. Please enter a number between 1 and the number of nodes."
                        )
            else:
                debug_handler(
                    message = f"Only one node ({self.valid_host_lists[0]}) is running the service {service_name}. Proceeding with the operation."
                )
            # 获取日志
            # 判断哪个服务可以登陆
            with console.status(f"[bold green]Getting logs from the cluster nodes...\n[/bold green]"):
                try:
                    self.task_submit(
                        host = self.valid_host_lists[0]
                    )

                except KeyboardInterrupt:
                    error_handler(message = "Operation interrupted by user.")
                    sys.exit(1)
                except Exception as e:
                    error_handler(message = f"Error occurred while getting the logs: {e}")
                    traceback.print_exc()
            if self.failed_nodes:
                error_handler(message = "The following nodes failed to retrieve logs:")
                for host, message in self.failed_nodes.items():
                    error_handler(message = f"    - {host}: {message}")
            return
        if self.operation_type == "install":
            # 需要进行获取最新安装包的获取
            latest_package_info = get_latest_package()
            self.kwargs["latest_package_path"] = latest_package_info[0]
            self.kwargs["latest_package_name"] = latest_package_info[1]
            self.kwargs["latest_package_md5sum"] = latest_package_info[2]
            self.kwargs["latest_package_size"] = latest_package_info[3]
            self.kwargs["installation_time"] = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            self.kwargs["deployment_dir"] = deployment_config.deployment_dir
            self.kwargs["deploy_whaletunnel"] = deployment_config.deploy_whaletunnel
            # 判断主机列表中是否存在使用 agent 部署的节点
            agent_nodes = [
                host for host, node_config in deployment_config.cluster_nodes.items()
                if node_config.get("deployment_type", "ssh") == "agent"
            ]
            if agent_nodes:
                from app.cluster.cluster_for_agent.tools.package_download import FileDownloadServer
                self.download_server = True
                download_server = FileDownloadServer()
                download_server.start()
            #     self.download_server = FileDownloadServer()
            #     self.download_server.start()

        if self.operation_type == "rollback":
            warning_handler(
                message = "Please make sure you have a backup of the database before rolling back Whale Studio."
            )
            for index in range(3):
                # 给用户三次机会确认回滚
                confirm_message = f"Are you sure you want to roll back Whale Studio to the previous version? (This action cannot be undone) (Y/N): "
                confirm_status = input(confirm_message)
                if confirm_status.lower() in ["y", "yes"]:
                    break
                elif confirm_status.lower() in ["n", "no"]:
                    sys.exit(0)
                else:
                    warning_handler(message = "Invalid input! Please enter Y or N.")

        if self.operation_type in [
            "db_init", "db_upgrade"
        ]:
            self.kwargs['operation_type'] = self.operation_type
            self.metabase_init()
            return
        if self.operation_type == "uninstall":
            """删除集群节点上的WhaleStudio"""
            warning_handler(
                message = "This operation will uninstall WhaleStudio from the cluster nodes:"
            )
            for host in self.valid_host_lists:
                warning_handler(message = f"    - {host}")

            user_input_flag = False
            try:
                for i in range(5):
                    user_input = input("Are you sure you want to continue? (y/n): ")

                    if user_input.strip().lower() == "y":
                        user_input_flag = True
                        break
                    elif user_input.strip().lower() == "n":
                        sys.exit(0)
                    else:
                        error_handler(message = "Invalid input. Please enter y or n.")
                if not user_input_flag:
                    warning_handler(message = "Operation cancelled by user.")
                    sys.exit(0)
                default_service_list = ["api", "master", "worker", "alert"]
                if deployment_config.deploy_whaletunnel:
                    default_service_list.append("whaletunnel")
                self.kwargs['user_input_services'] = default_service_list
            except KeyboardInterrupt:
                error_handler(message = "Operation interrupted by user.")
                sys.exit(1)
            except Exception as e:
                error_handler(message = f"Error occurred while getting user input: {e}")
                debug_handler(message = traceback.format_exc())
                sys.exit(1)
        if self.operation_type in [
            "start", "stop", "status", "restart", "logs"
        ]:
            self.kwargs['operation_type'] = self.operation_type
            self._process_service_operations()
                self.kwargs['user_input_services'] = valid_service_lists
        remaining_hosts = self.valid_host_lists.copy()
        with console.status(
                f"[bold green]{operation_descriptions.get(self.operation_type, 'Unknown Operation')} on {len(self.valid_host_lists)} nodes...\n[/bold green]"
        ):
            try:
                with ProcessPoolExecutor(
                        max_workers = self.process_pool_size
                ) as executor:
                    task_futures = {}
                    while remaining_hosts and len(task_futures) < self.process_pool_size:
                        host = remaining_hosts.pop(0)
                        future = executor.submit(self.task_submit, host)
                        task_futures[future] = host
                    while task_futures:
                        done_futures, _ = concurrent.futures.wait(
                            task_futures.keys(),
                            return_when = concurrent.futures.FIRST_COMPLETED
                        )
                        for future in done_futures:
                            task_futures.pop(future)
                            if remaining_hosts:
                                new_host = remaining_hosts.pop(0)
                                new_future = executor.submit(self.task_submit, new_host)
                                task_futures[new_future] = new_host
            except Exception as e:
                error_handler(message = f"Error occurred while executing the operation: {e}")
                traceback.print_exc()
                sys.exit(1)
        if self.failed_nodes:
            error_handler(message = "The following nodes failed during the operation:")
            for host, message in self.failed_nodes.items():
                error_handler(message = f"    - {host}: {message}")
        if self.result_dict:
            info_handler(message = "Completed node operation time:")
            for host, elapsed_time in self.result_dict.items():
                info_handler(message = f"{host} {self.operation_type} time:")
                for line in elapsed_time:
                    info_handler(message = f"    - {line}")
        if self.download_server:
            download_server.stop()

    def task_submit(self, host):
        """
        Submit the task to the process pool. | 提交任务到进程池
        :param host:
        :return:
        """
        warning_handler(f"submitting task to process pool for host: {host}".center(100, "="))
        try:
            if deployment_config.cluster_nodes.get(host, {}).get("deployment_type", "ssh") == "ssh":
                # 1. 测试SSH连接
                self.ssh_task_submit(host)
            else:
                self.agent_task_submit(host)
        except KeyboardInterrupt:
            error_handler(message = "Operation interrupted by user.")
            sys.exit(1)
        except Exception as e:
            error_handler(message = f"Error occurred while executing the operation on {host}: {e}")
            debug_handler(message = traceback.format_exc())
            self.failed_nodes[host] = str(e)

    def agent_task_submit(self, agent_node):
        from app.cluster.cluster_for_agent.functions.pre_check import pre_check_agent_node
        from app.cluster.cluster_for_agent.functions.whaleStudio_install import install_whale_studio
        from app.cluster.cluster_for_agent.functions.whaleStudio_configuration_update import update_configuration_file
        from app.cluster.cluster_for_agent.functions.whaleStudio_operation import service_operation
        from app.cluster.cluster_for_agent.functions.clean_up_useless_packages import clean_up_useless_packages
        from app.cluster.cluster_for_agent.functions.whaleStudio_uninstall import whaleStudio_uninstall
        from app.cluster.cluster_for_agent.functions.whaleStudio_rollback import rollback_whale_studio
        # 操作对应的函数
        operation_functions = {
            "install": install_whale_studio,
            "pre_check": pre_check_agent_node,
            "uninstall": whaleStudio_uninstall,
            "start": service_operation,
            "stop": service_operation,
            "status": service_operation,
            "restart": service_operation,
            "config_update": update_configuration_file,
            "clean_packages": clean_up_useless_packages,
            "logs": service_operation,
            "rollback": rollback_whale_studio,
        }
        start_time = time.time()
        # 其他参数
        other_args = self.kwargs.copy()
        other_args.pop("host")
        try:
            func = operation_functions.get(self.operation_type, None)
            if callable(func):
                node_operation_status, node_operation_message = func(
                    host = agent_node, **other_args
                )
                if node_operation_status:
                    info_handler(message = f"The operation {self.operation_type} on {agent_node} is successful.")
                else:
                    self.failed_nodes[agent_node] = node_operation_message
                    return
            else:
                error_handler(message = f"The operation {self.operation_type} is not supported on {agent_node}.")
                return

        except Exception as e:
            error_handler(message = f"Error occurred while executing the operation on {agent_node}: {e}")
            debug_handler(message = traceback.format_exc())
            return
        end_time = time.time()

        # 耗时
        elapsed_time = end_time - start_time
        if elapsed_time > 60:
            elapsed_time = f"{elapsed_time / 60:.2f} minutes"
        else:
            elapsed_time = f"{elapsed_time:.2f} seconds"
        warning_handler(
            message = f"The operation {self.operation_type} on {agent_node} is completed in {elapsed_time}."
        )

    def metabase_init(self):
        """
        Initialize the Metabase database. | 初始化 Metabase 数据库
        :return:
        """
        node_connect_status = False
        with console.status(
                f"[bold green]Initializing Metabase database on {len(self.valid_host_lists)} nodes...\n[/bold green]"
        ):
            for host in self.valid_host_lists:
                try:
                    if deployment_config.cluster_nodes.get(host, {}).get("deployment_type", "ssh") == "ssh":
                        from app.cluster.cluster_for_ssh.tools.node_connect import node_connect
                        from app.cluster.cluster_for_ssh.functions.whaleStudio_db_operation import DBOperation
                        ssh_client, sftp_client, message = node_connect(host)
                        if not ssh_client:
                            debug_handler(message = f"Failed to connect to {host} via SSH: {message}")
                            continue
                        node_connect_status = True
                        DBOperation(
                            operation_type = self.operation_type,
                            show_log = self.kwargs.get(
                                "operation_value", None
                            ),
                            ssh_client = ssh_client,
                            sftp_client = sftp_client,
                        ).run()
                        break
                    else:
                        from app.cluster.cluster_for_agent.tools.node_connect import NodeAgentConnect
                        from app.cluster.cluster_for_agent.functions.whaleStudio_db_operation import DBOperation
                        agent_client, agent_connect_result = NodeAgentConnect(
                            node_ip = host
                        ).get_node_conn()
                        if not agent_client:
                            debug_handler(message = f"Failed to connect to {host} via agent: {agent_connect_result}")
                            continue
                        node_connect_status = True
                        DBOperation(
                            operation_type = self.operation_type,
                            show_log = self.kwargs.get(
                                "operation_value", None
                            ),
                            agent_client = agent_client,
                        ).run()

                except KeyboardInterrupt:
                    error_handler(message = "Operation interrupted by user.")
                    sys.exit(1)
        if not node_connect_status:
            error_handler(message = "Failed to connect to any node via SSH or agent.")
            error_handler(message = "Please check the SSH or agent configuration.")
            sys.exit(1)

    def ssh_task_submit(self, ssh_node):
        from app.cluster.cluster_for_ssh.functions.pre_check import pre_check_ssh_node
        from app.cluster.cluster_for_ssh.functions.whaleStudio_install import install_whale_studio
        from app.cluster.cluster_for_ssh.functions.whaleStudio_configuration_update import update_configuration_file
        from app.cluster.cluster_for_ssh.functions.whaleStudio_operation import service_operation
        from app.cluster.cluster_for_ssh.functions.clean_up_useless_packages import clean_up_useless_packages
        from app.cluster.cluster_for_ssh.functions.whaleStudio_uninstall import whaleStudio_uninstall
        from app.cluster.cluster_for_ssh.functions.whaleStudio_rollback import rollback_whale_studio
        operation_functions = {
            "install": install_whale_studio,
            "pre_check": pre_check_ssh_node,
            "uninstall": whaleStudio_uninstall,
            "start": service_operation,
            "stop": service_operation,
            "status": service_operation,
            "restart": service_operation,
            "config_update": update_configuration_file,
            "clean_packages": clean_up_useless_packages,
            "logs": service_operation,
            "rollback": rollback_whale_studio,
        }
        start_time = time.time()

        # 其他参数
        other_args = self.kwargs.copy()
        other_args.pop("host")

        try:
            func = operation_functions.get(self.operation_type, None)
            if callable(func):
                node_operation_status, node_operation_message = func(
                    host = ssh_node, **other_args
                )
                if node_operation_status:
                    info_handler(message = f"The operation {self.operation_type} on {ssh_node} is successful.")
                else:
                    self.failed_nodes[ssh_node] = node_operation_message
                    return
            else:
                error_handler(message = f"The operation {self.operation_type} is not supported on {ssh_node}.")
                return

        except Exception as e:
            error_handler(message = f"Error occurred while executing the operation on {ssh_node}: {e}")
            debug_handler(message = traceback.format_exc())
            return
        end_time = time.time()

        # 耗时
        elapsed_time = end_time - start_time
        if elapsed_time > 60:
            elapsed_time = f"{elapsed_time / 60:.2f} minutes"
        else:
            elapsed_time = f"{elapsed_time:.2f} seconds"
        warning_handler(message = f"The operation {self.operation_type} on {ssh_node} is completed in {elapsed_time}.")

    def _process_service_operations(self):
        """
        处理服务操作，根据节点角色配置过滤服务列表
        Process service operations, filter service list based on node roles configuration
        """
        default_service_list = ["api", "master", "worker", "alert"]
        if deployment_config.deploy_whaletunnel:
            default_service_list.append("whaletunnel")

        # 用户提供的服务列表
        user_input_service_list = self.kwargs.get('operation_value', "all")

        if user_input_service_list.lower() == "all":
            # 当用户输入 "all" 时，不再使用全部服务列表，而是根据每个节点的角色配置来确定
            # 这里设置一个标记，让每个节点根据自己的角色配置来处理
            self.kwargs['user_input_services'] = "all"  # 保持 "all" 标记
            self.kwargs['use_node_roles'] = True  # 新增标记，表示需要根据节点角色过滤
        else:
            # 用户指定了具体服务，需要验证服务名称的有效性
            user_input_service = user_input_service_list.split(",")
            user_input_service = [service.strip() for service in user_input_service if service.strip()]

            # 不合法的服务列表
            invalid_service_lists = set(user_input_service) - set(default_service_list)
            # 合法的服务列表
            valid_service_lists = list(set(user_input_service) - invalid_service_lists)

            if invalid_service_lists:
                error_handler(message = "Invalid service list: ")
                for service in invalid_service_lists:
                    error_handler(message = f"    - {service}")

            if not valid_service_lists and invalid_service_lists:
                error_handler(
                    message = "After excluding invalid services, the list of valid services is empty. Please check the input service list."
                )
                sys.exit(1)

            if not valid_service_lists:
                error_handler(
                    message = "The list of valid services is empty. Please check the input service list."
                )
                sys.exit(1)

            self.kwargs['user_input_services'] = valid_service_lists
            self.kwargs['use_node_roles'] = False  # 用户指定了具体服务，不需要根据节点角色过滤
