#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : logging_utils.py
# @Time    : 2025/06/03 16:46
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys
import traceback
from functools import lru_cache
from loguru import logger
from rich.console import Console
# from .utils import get_parent_directory

console = Console()

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


@lru_cache(maxsize = 1)
def setup_logging(logs_path = None, level = "INFO"):
    """
    Set up logging configuration.
    """
    # Set the log level and format
    os.environ["LOGLEVEL"] = level
    try:
        if not logs_path:
            logs_path = os.path.join(
                ".", "logs", "deployment.log"
            )

        if not hasattr(setup_logging, 'has_configured'):
            logger.remove()
            logger.add(
                logs_path, format = "[{level}] | {time} | {message}", mode = 'a',
                encoding = 'utf-8', level = level, rotation = "20 MB"
            )
            setattr(setup_logging, 'has_configured', True)
    # 如果磁盘空间不足，抛出异常
    except OSError as e:
        console.print(f"[bold red]Error setting up logging: {e}[/bold red]")
        console.print(f"[bold red]Logs path: {logs_path}[/bold red]")
        console.print(f"[bold red]{traceback.format_exc()}[/bold red]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[bold red]Error setting up logging: {e}[/bold red]")
        console.print(f"[bold red]Logs path: {logs_path}[/bold red]")
        console.print(f"[bold red]{traceback.format_exc()}[/bold red]")
        sys.exit(1)


def error_handler(message):
    """
    Error handler for loguru logger.
    """
    console.print(f"[bold red]{message}[/bold red] ")
    logger.error(message)


def info_handler(message):
    """
    Info handler for loguru logger.
    """
    logger.info(message)
    console.print(f"[bold green]{message}[/bold green]")


def debug_handler(message):
    """
    Debug handler for loguru logger.
    """
    logger.debug(message)


def warning_handler(message):
    """
    Warning handler for loguru logger.
    """
    console.print(f"[bold yellow]{message}[/bold yellow] ")
    logger.warning(message)


def title_handler(message):
    """
    Title handler for loguru logger.
    """
    console.print(f"[bold blue]{message}[/bold blue]")
    logger.info(message)
