#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : scheduler_config.py
# @Time    : 2025/06/04 09:46
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import re
import sys
import yaml

from app.config.deployment_config import deployment_config, load_env_command
from app.common.logging_utils import error_handler, debug_handler
from app.common.utils import escape_special_characters, string_to_variable_name

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def metabase_logs_save_path():
    """
    metabase_logs_save_path | Metabase日志保存路径
    :return:
    """
    return os.path.join(
        ".", "logs"
    )


# 其他需要同步的文件列表
def other_files_to_sync():
    """
    other_files_to_sync | 其他需要同步的文件列表
    :return:
    """
    other_files_path = os.getenv(
        "OTHER_FILES_PATH", os.path.join(
            ".", "config", "other_files"
        )
    )
    if not os.path.isdir(other_files_path):
        debug_handler(message = f"No other files to sync found in {other_files_path}.")
        return []

    other_files_list = [
        os.path.join(
            other_files_path,
            file
        ) for file in os.listdir(other_files_path) if os.path.isfile(os.path.join(other_files_path, file))
    ]
    debug_handler(message = f"Other files to sync: {other_files_list}")
    return other_files_list


# 调度启动配置
def scheduler_startup_config():
    """
    scheduler_startup_config | 调度启动配置
    :return:
    """
    scheduler_config = SchedulerConfig()
    return scheduler_config.metabase_config() + \
        scheduler_config.register_config() + \
        scheduler_config.environment_config() + \
        scheduler_config.logging_config() + \
        scheduler_config.reset_user_password_config() + \
        scheduler_config.resource_center_config() + \
        scheduler_config.whaleTunnel_config() + \
        scheduler_config.other_config()


def metabase_initialization_config():
    """
    metabase_initialization_config | Metabase初始化配置
    :return:
    """
    match deployment_config.metabase.get("type", "mysql").lower():
        case "mysql":
            return MetabaseConfig().mysql_config()
        case _:
            return SchedulerConfig().metabase_config()


class SchedulerConfig:
    """
    SchedulerConfig | 调度配置类
    """

    def register_config(self):
        """
        register_config | 注册调度配置
        :return:
        """
        match deployment_config.registration_center.get("type", "zookeeper").lower():
            case "zookeeper":
                return RegisterConfig.zookeeper_config()
            case "mysql":
                return RegisterConfig.mysql_config()
            case _:
                error_handler(
                    message = f"Unsupported registration center type: {deployment_config.registration_center.get('type')}"
                )
                sys.exit(1)

    def metabase_config(self):
        """
        metabase_config | Metabase配置
        :return:
        """
        metabase_config = MetabaseConfig()

        match deployment_config.metabase.get("type", "mysql").lower():
            case "mysql":
                if deployment_config.metabase.get("sharding_enable", False):
                    return metabase_config.mysql_sharding_config()
                else:
                    return metabase_config.mysql_config()
            case "postgresql" | "postgres" | "pg":
                return metabase_config.postgresql_config()
            case "dm" | "dameng":
                return metabase_config.dm_config()
            case "highgo" | "highgodb" | "highgo-db":
                return metabase_config.HighGo_config()
            case "kingbase":
                return metabase_config.KingBase_config()
            case "opengauss" | "gaussdb" | "gauss" | "gauss-db":
                return metabase_config.OpenGauss_config()
            case "oceanbase":
                return metabase_config.OceanBase_config()
            case _:
                error_handler(message = f"Unsupported Metabase type: {deployment_config.metabase.get('type')}")
                sys.exit(1)

    def environment_config(self, high_priority_config = None):
        """
        environment_config | 环境配置
        :return:
        """
        scheduler_env_config = deployment_config.whalestudio_environment
        if not high_priority_config:
            high_priority_config = []
        if not scheduler_env_config:
            debug_handler(message = "No whalestudio environment configuration found in deployment.yaml.")
            scheduler_env_config = {}
        else:
            scheduler_env_config = {
                string_to_variable_name(string = key): value for key, value in scheduler_env_config.items() if
                string_to_variable_name(string = key) not in high_priority_config
            }

        environment_file = os.path.join(
            ".", "config", "environment.yaml"
        )

        if os.path.isfile(environment_file):
            try:
                with open(environment_file, "r") as f:
                    environment_config = yaml.safe_load(f)
            except Exception as e:
                error_handler(message = f"Failed to load environment configuration from {environment_file}: {e}")
            if environment_config:
                environment_file_config = {
                    string_to_variable_name(string = key): value for key, value in environment_config.items() if
                    string_to_variable_name(string = key) not in scheduler_env_config
                }
            else:
                environment_file_config = {}

            scheduler_env_config.update(environment_file_config)

        scheduler_env_list = [
            '# WhaleStudio environment configuration'
        ]
        for key, value in scheduler_env_config.items():
            scheduler_env_list.append(f'export {key}="{escape_special_characters(value)}"')
        debug_handler(message = f"WhaleStudio environment configuration: {scheduler_env_config}")
        return scheduler_env_list

    def logging_config(self, log_path = None):
        """
        logging_config | 日志配置
        :return:
        """
        if not log_path:
            log_path = deployment_config.service_log_dir or os.path.join(
                deployment_config.deployment_dir, "logs"
            )

        scheduler_logging_config = [
            "# Logging configuration",
            f'export WORKFLOW_LOG_DIR="{log_path}"',
            f'export TASK_LOG_DIR="{log_path}"',
            f'export SERVER_LOG_DIR="{log_path}"'
        ]
        debug_handler(message = f"Logging configuration: {scheduler_logging_config}")
        return scheduler_logging_config

    def reset_user_password_config(self):
        """
        reset_user_password_config | 重置用户密码配置
        :return:
        """
        if not deployment_config.reset_password_email:
            debug_handler(message = "No reset password email configuration found in deployment.yaml.")
            return []

        smtp_host = deployment_config.reset_password_email.get("smtp_host", "")
        if isinstance(smtp_host, list):
            error_handler(message = f"Invalid SMTP host configuration: {smtp_host}")
            error_handler(message = "Expected a single host string, not a list.")
            sys.exit(1)
        smtp_port = deployment_config.reset_password_email.get("smtp_port", "")
        smtp_username = deployment_config.reset_password_email.get("smtp_username", "")
        smtp_password = deployment_config.reset_password_email.get("smtp_password", "")

        reset_password_email_config = [
            "# Reset password email configuration",
            f'export SPRING_MAIL_HOST="{smtp_host}"',
            f'export SPRING_MAIL_PORT="{smtp_port}"',
            f'export SPRING_MAIL_USERNAME="{smtp_username}"',
            f'export SPRING_MAIL_PASSWORD="{smtp_password}"',
        ]
        debug_handler(message = f"Reset password email configuration: {reset_password_email_config}")
        return reset_password_email_config

    # 资源中心配置
    def resource_center_config(self):
        """
        resource_center_config | 资源中心配置
        :return:
        """
        if not deployment_config.resource_center:
            debug_handler(message = "No resource center configuration found in deployment.yaml.")
            return []

        match deployment_config.resource_center.get("type", "local").lower():
            case "local" | "localfile":
                return ResourceCenterConfig.local_config()
            case "hdfs":
                return ResourceCenterConfig.hdfs_config()
            case "s3":
                return ResourceCenterConfig.s3_config()
            case "oss":
                return ResourceCenterConfig.oss_config()
            # case "minio":
            #     return ResourceCenterConfig.minio_config()
            case _:
                error_handler(
                    message = f"Unsupported resource center type: {deployment_config.resource_center.get('type')}"
                )
                return []

    def whaleTunnel_config(self):
        """
        datasource_config | 数据源配置
        :return:
        """
        whaleTunnel_config = [
            "# WhaleTunnel configuration",
            f'export WT_BASEDIR_PATH="{os.path.join(deployment_config.deployment_dir, "current", "datasource")}"'
        ]
        if deployment_config.deploy_whaletunnel:
            whaleTunnel_config.append(
                f'export SEATUNNEL_HOME="{os.path.join(deployment_config.deployment_dir, "current", "whaletunnel")}"'
            )
            # 其他必要配置
        return whaleTunnel_config

    def other_config(self):
        """
        other_config | 其他必要配置
        :return:
        """
        scheduler_other_config = [
            "# Other necessary configuration",
            f'export SUDO_ENABLE="{str(deployment_config.tenant_enable).lower()}"',
            f'export DATASOURCE_ENCRYPTED="{str(deployment_config.datasource_encrypted).lower()}"',
            f'export DATA_BASEDIR_PATH="{deployment_config.data_basedir_path or os.path.join(deployment_config.deployment_dir, "user_data")}"',
            f'export API_WORKFLOW_ADDRESS="{deployment_config.api_workflow_address}"',
        ]
        debug_handler(message = f"Other necessary configuration: {scheduler_other_config}")
        return scheduler_other_config


class RegisterConfig:
    """
    RegisterConfig | 注册中心配置类
    """

    @staticmethod
    def zookeeper_config():
        """
        zookeeper_config | Zookeeper注册中心配置
        :return:
        """
        hosts = deployment_config.registration_center.get("host", "localhost")
        if isinstance(hosts, str):
            hosts = [
                host.strip() for host in hosts.split(",") if host.strip()
            ]
        elif isinstance(hosts, list):
            hosts = [
                host.strip() for host in hosts if host.strip()
            ]
        else:
            error_handler(
                message = f"Invalid Zookeeper hosts configuration: {hosts}"
            )
            sys.exit(1)
        port = deployment_config.registration_center.get("port", 2181)

        hosts_str = ",".join(
            f"{host}:{port}" for host in hosts
        )

        timeout = deployment_config.registration_center.get("timeout", 60)
        namespace = deployment_config.registration_center.get("namespace", "whalestudio")
        username = deployment_config.registration_center.get("username", None)
        password = deployment_config.registration_center.get("password", None)

        zookeeper_registration_config = [
            "# Zookeeper registration center configuration",
            'export REGISTRY_TYPE="zookeeper"',
            f'export REGISTRY_ZOOKEEPER_CONNECT_STRING="{hosts_str}"',
            f'export REGISTRY_ZOOKEEPER_CONNECTION_TIMEOUT="{timeout}s"',
            f'export REGISTRY_ZOOKEEPER_BLOCK_UNTIL_CONNECTED="{timeout}s"',
            f'export REGISTRY_ZOOKEEPER_NAMESPACE="{namespace}"'
        ]
        if username and password:
            zookeeper_registration_config.append(
                f'export REGISTRY_ZOOKEEPER_DIGEST="{username}:{password}"'
            )
        debug_handler(message = f"Zookeeper registration center configuration: {zookeeper_registration_config}")
        return zookeeper_registration_config

    @staticmethod
    def mysql_config():
        """
        mysql_config | MySQL注册中心配置
        :return:
        """
        host = deployment_config.registration_center.get("host", "localhost")
        if isinstance(host, list):
            error_handler(message = f"Invalid MySQL host configuration: {host}")
            error_handler(message = "Expected a single host string, not a list.")
            sys.exit(1)
        port = deployment_config.registration_center.get("port", 3306)
        database = deployment_config.registration_center.get("database", "whalestudio")
        username = deployment_config.registration_center.get("username", "root")
        password = deployment_config.registration_center.get("password", "root")

        mysql_registration_config = [
            "# MySQL registration center configuration",
            f'export REGISTRY_TYPE="jdbc"',
            f'export REGISTRY_HIKARI_CONFIG_DRIVER_CLASS_NAME="com.mysql.cj.jdbc.Driver"',
            f'export REGISTRY_HIKARI_CONFIG_JDBC_URL="jdbc:mysql://{host}:{port}/{database}?useSSL=false"',
            f'export REGISTRY_HIKARI_CONFIG_USERNAME="{username}"',
            f'export REGISTRY_HIKARI_CONFIG_PASSWORD="{password}"',
        ]
        debug_handler(message = f"MySQL registration center configuration: {mysql_registration_config}")
        return mysql_registration_config


class MetabaseConfig:
    def __init__(self):
        self.metabase_host = deployment_config.metabase.get("host", "localhost")
        self.metabase_port = deployment_config.metabase.get("port", 3306)
        self.metabase_database = deployment_config.metabase.get("database", "metabase")
        self.metabase_username = deployment_config.metabase.get("username", "root")
        self.metabase_password = escape_special_characters(
            string = deployment_config.metabase.get("password", "root")
        )
        self.sharding_enable = deployment_config.metabase.get("sharding_enable", False)
        self.metabase_ssl = deployment_config.metabase.get("ssl", False)
        self.metabase_schema = deployment_config.metabase.get("schema", None)
        self.metabase_uppercase = deployment_config.metabase.get("uppercase", False)
        self.metabase_remove_back_quote = deployment_config.metabase.get("remove_back_quote", False)
        self.metabase_jdbc_url = deployment_config.metabase.get("jdbc_url", None)
        if isinstance(self.metabase_host, list):
            error_handler(message = f"Invalid Metabase host configuration: {self.metabase_host}")
            error_handler(message = "Expected a single host string, not a list.")
            sys.exit(1)

    def mysql_config(self):
        """
        mysql_config | MySQL Metabase配置
        :return:
        """
        jdbc_url = f"jdbc:mysql://{self.metabase_host}:{self.metabase_port}/{self.metabase_database}?useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&useSSL=false" or self.metabase_jdbc_url
        mysql_metabase_config = [
            "# MySQL Metabase configuration",
            'export DATABASE="mysql"',
            'export SPRING_PROFILES_ACTIVE=${DATABASE}',
            'export SPRING_DATASOURCE_DRIVER_CLASS_NAME="com.mysql.cj.jdbc.Driver"',
            "export SPRING_SHARDINGSPHERE_ENABLED=false",
            f'export SPRING_DATASOURCE_URL="{jdbc_url}"',
            f'export SPRING_DATASOURCE_USERNAME="{self.metabase_username}"',
            f'export SPRING_DATASOURCE_PASSWORD="{self.metabase_password}"',
        ]
        debug_handler(message = f"MySQL Metabase configuration: {mysql_metabase_config}")
        return mysql_metabase_config

    def mysql_sharding_config(self):
        """
        mysql_sharding_config | MySQL分片Metabase配置
        :return:
        """
        jdbc_url = f"jdbc:mysql://{self.metabase_host}:{self.metabase_port}/{self.metabase_database}?useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&useSSL=false" or self.metabase_jdbc_url
        mysql_sharding_metabase_config = [
            "# MySQL sharding Metabase configuration",
            'export DATABASE="mysql"',
            'export SPRING_PROFILES_ACTIVE=${DATABASE}',
            'export SPRING_SHARDINGSPHERE_DATASOURCE_DB1_DRIVER_CLASS_NAME="com.mysql.cj.jdbc.Driver"',
            'export SPRING_SHARDINGSPHERE_ENABLED=true',
            f'export SPRING_SHARDINGSPHERE_DATASOURCE_DB1_JDBC_URL="{jdbc_url}"',
            f'export SPRING_SHARDINGSPHERE_DATASOURCE_DB1_USERNAME="{self.metabase_username}"',
            f'export SPRING_SHARDINGSPHERE_DATASOURCE_DB1_PASSWORD="{self.metabase_password}"',
        ]
        debug_handler(message = f"MySQL sharding Metabase configuration: {mysql_sharding_metabase_config}")
        return mysql_sharding_metabase_config

    def postgresql_config(self):
        """
        postgresql_config | PostgreSQL Metabase配置
        """
        if self.metabase_jdbc_url:
            jdbc_url = self.metabase_jdbc_url
        else:
            jdbc_url = f"jdbc:postgresql://{self.metabase_host}:{self.metabase_port}/{self.metabase_database}"
            if self.metabase_schema:
                jdbc_url += f"?currentSchema={self.metabase_schema}"
        postgresql_metabase_config = [
            "# PostgreSQL Metabase configuration",
            'export DATABASE="postgresql"',
            'export SPRING_PROFILES_ACTIVE=${DATABASE}',
            'export SPRING_DATASOURCE_DRIVER_CLASS_NAME="org.postgresql.Driver"',
            'export SPRING_SHARDINGSPHERE_ENABLED=false',
            f'export SPRING_DATASOURCE_URL="{jdbc_url}"',
            f'export SPRING_DATASOURCE_USERNAME="{self.metabase_username}"',
            f'export SPRING_DATASOURCE_PASSWORD="{self.metabase_password}"',
            "export SPRING_QUARTZ_PROPERTIES_ORG_QUARTZ_JOBSTORE_DRIVERDELEGATECLASS=org.quartz.impl.jdbcjobstore.PostgreSQLDelegate"
        ]
        if self.metabase_remove_back_quote:
            postgresql_metabase_config += [
                'export SQL_INTERCEPTOR_ENABLE=true',
                'export SQL_INTERCEPTOR_REMOVE_BACK_QUOTE=true'
            ]
        debug_handler(message = f"PostgreSQL Metabase configuration: {postgresql_metabase_config}")
        return postgresql_metabase_config

    def dm_config(self):
        """
        dm_config | DM Metabase配置
        :return:
        """
        jdbc_url = f"jdbc:dm://{self.metabase_host}:{self.metabase_port}" or self.metabase_jdbc_url
        dm_metabase_config = [
            "# DM Metabase configuration",
            'export DATABASE="dm"',
            'export SPRING_PROFILES_ACTIVE=${DATABASE}',
            'export SPRING_DATASOURCE_DRIVER_CLASS_NAME="dm.jdbc.driver.DmDriver"',
            "export SPRING_SHARDINGSPHERE_ENABLED=false",
            f'export SPRING_DATASOURCE_URL="{jdbc_url}"',
            f'export SPRING_DATASOURCE_USERNAME="{self.metabase_username}"',
            f'export SPRING_DATASOURCE_PASSWORD="{self.metabase_password}"',
        ]
        debug_handler(message = f"DM Metabase configuration: {dm_metabase_config}")
        return dm_metabase_config

    def HighGo_config(self):
        """
        HighGo_config | HighGoDB Metabase配置
        :return:
        """
        jdbc_url = f'"jdbc:highgo://{self.metabase_host}:{self.metabase_port}/{self.metabase_database}"' or self.metabase_jdbc_url
        high_go_metabase_config = [
            "# HighGoDB Metabase configuration",
            'export DATABASE="highgo"',
            'export SPRING_PROFILES_ACTIVE=${DATABASE}',
            'export SPRING_DATASOURCE_DRIVER_CLASS_NAME="com.highgo.jdbc.Driver"',
            "export SPRING_SHARDINGSPHERE_ENABLED=false",
            f'export SPRING_DATASOURCE_URL="{jdbc_url}"',
            f'export SPRING_DATASOURCE_USERNAME="{self.metabase_username}"',
            f'export SPRING_DATASOURCE_PASSWORD="{self.metabase_password}"',
        ]
        if self.metabase_remove_back_quote:
            high_go_metabase_config += [
                'export SQL_INTERCEPTOR_ENABLE=true',
                'export SQL_INTERCEPTOR_REMOVE_BACK_QUOTE=true'
            ]
        debug_handler(message = f"HighGoDB Metabase configuration: {high_go_metabase_config}")
        return high_go_metabase_config

    def KingBase_config(self):
        """
        kingbase_config | Kingbase Metabase配置
        :return:
        """
        jdbc_url = f"jdbc:kingbase8://{self.metabase_host}:{self.metabase_port}/{self.metabase_database}" or self.metabase_jdbc_url
        king_base_metabase_config = [
            "# Kingbase Metabase configuration",
            'export DATABASE="kingbase"',
            'export SPRING_PROFILES_ACTIVE=${DATABASE}',
            'export SPRING_DATASOURCE_DRIVER_CLASS_NAME="com.kingbase8.Driver"',
            "export SPRING_SHARDINGSPHERE_ENABLED=false",
            f'export SPRING_DATASOURCE_URL="{jdbc_url}"',
            f'export SPRING_DATASOURCE_USERNAME="{self.metabase_username}"',
            f'export SPRING_DATASOURCE_PASSWORD="{self.metabase_password}"',
        ]
        if self.metabase_uppercase or self.metabase_remove_back_quote:
            king_base_metabase_config += [
                'export SQL_INTERCEPTOR_ENABLE=true',
            ]
        if self.metabase_uppercase:
            king_base_metabase_config += [
                'export SQL_INTERCEPTOR_REMOVE_BACK_QUOTE=true'
            ]
        if self.metabase_remove_back_quote:
            king_base_metabase_config += [
                'export SQL_INTERCEPTOR_REMOVE_BACK_QUOTE=true'
            ]
        debug_handler(message = f"Kingbase Metabase configuration: {king_base_metabase_config}")
        return king_base_metabase_config

    def OpenGauss_config(self):
        """
        OpenGauss_config | OpenGauss Metabase配置
        :return:
        """
        if self.metabase_jdbc_url:
            jdbc_url = self.metabase_jdbc_url
        else:
            jdbc_url = f"jdbc:opengauss://{self.metabase_host}:{self.metabase_port}/{self.metabase_database}"
            if self.metabase_schema:
                jdbc_url += f"?currentSchema={self.metabase_schema}"
        open_gauss_metabase_config = [
            "# OpenGauss Metabase configuration",
            'export DATABASE="gauss"',
            'export SPRING_PROFILES_ACTIVE=${DATABASE}',
            'export SPRING_DATASOURCE_DRIVER_CLASS_NAME="org.opengauss.jdbc.Driver"',
            'export SPRING_SHARDINGSPHERE_ENABLED=false',
            f'export SPRING_DATASOURCE_URL="{jdbc_url}"',
            f'export SPRING_DATASOURCE_USERNAME="{self.metabase_username}"',
            f'export SPRING_DATASOURCE_PASSWORD="{self.metabase_password}"',
            "export SPRING_QUARTZ_PROPERTIES_ORG_QUARTZ_JOBSTORE_DRIVERDELEGATECLASS=org.quartz.impl.jdbcjobstore.PostgreSQLDelegate"
        ]
        if self.metabase_remove_back_quote:
            open_gauss_metabase_config += [
                'export SQL_INTERCEPTOR_ENABLE=true',
                'export SQL_INTERCEPTOR_REMOVE_BACK_QUOTE=true'
            ]
        debug_handler(message = f"OpenGauss Metabase configuration: {open_gauss_metabase_config}")
        return open_gauss_metabase_config

    def OceanBase_config(self):
        """
        OceanBase_config | OceanBase Metabase配置
        :return:
        """
        jdbc_url = f'"jdbc:oceanbase://{self.metabase_host}:{self.metabase_port}/{self.metabase_database}"' or self.metabase_jdbc_url
        ocean_base_metabase_config = [
            "# OceanBase Metabase configuration",
            'export DATABASE="oceanbase"',
            'export SPRING_PROFILES_ACTIVE=${DATABASE}',
            'export SPRING_DATASOURCE_DRIVER_CLASS_NAME="com.oceanbase.jdbc.Driver"',
            "export SPRING_SHARDINGSPHERE_ENABLED=false",
            f'export SPRING_DATASOURCE_URL="{jdbc_url}"',
            f'export SPRING_DATASOURCE_USERNAME="{self.metabase_username}"',
            f'export SPRING_DATASOURCE_PASSWORD="{self.metabase_password}"',
        ]
        debug_handler(message = f"OceanBase Metabase configuration: {ocean_base_metabase_config}")
        return ocean_base_metabase_config


class ResourceCenterConfig:
    """
    ResourceCenterConfig | 资源中心配置类
    """

    @staticmethod
    def local_config():
        """
        local_config | 本地资源中心配置
        :return:
        """
        resource_center_path = deployment_config.resource_center.get(
            "local_file_path", os.path.join(
                deployment_config.deployment_dir, "resources"
            )
        )
        local_resource_center_config = [
            "# Local resource center configuration",
            'export RESOURCE_STORAGE_TYPE="hdfs"',
            f'export RESOURCE_UPLOAD_PATH="{resource_center_path}"'
        ]
        debug_handler(message = f"Local resource center configuration: {local_resource_center_config}")
        return local_resource_center_config

    @staticmethod
    def hdfs_config():
        """
        hdfs_config | HDFS资源中心配置
        :return:
        """
        hdfs_url = deployment_config.resource_center.get("hdfs_url", "hdfs://localhost:9000")
        hdfs_file_path = deployment_config.resource_center.get("hdfs_file_path", "/data/whalestudio/resources")
        hdfs_user = deployment_config.resource_center.get("hdfs_user", "hdfs")

        kerberos_enabled = deployment_config.resource_center.get("kerberos_enabled", False)
        krb5_conf_path = deployment_config.resource_center.get("krb5_conf_path", None)
        keytab_path = deployment_config.resource_center.get("keytab_path", None)
        keytab_username = deployment_config.resource_center.get("keytab_username", None)

        hdfs_resource_center_config = [
            "# HDFS resource center configuration",
            'export RESOURCE_STORAGE_TYPE="HDFS"',
            f'export RESOURCE_UPLOAD_PATH="{hdfs_file_path}"',
            f'export FS_DEFAULTFS="{hdfs_url}"',
            f'export HDFS_ROOT_USER="{hdfs_user}"'
        ]
        if kerberos_enabled:
            hdfs_resource_center_config += [
                'export HADOOP_SECURITY_AUTHENTICATION_STARTUP_STATE"true"',
                f'export JAVA_SECURITY_KRB5_CONF_PATH="{krb5_conf_path}"',
                f'export LOGIN_USER_KEYTAB_PATH="{keytab_path}"',
                f'export LOGIN_USER_KEYTAB_USERNAME="{keytab_username}"'
            ]
        debug_handler(message = f"HDFS resource center configuration: {hdfs_resource_center_config}")
        return hdfs_resource_center_config

    @staticmethod
    def s3_config():
        """
        s3_config | S3资源中心配置
        :return:
        """
        bucket_name = deployment_config.resource_center.get("bucket_name", "whalestudio-resources")
        no_authentication_required = deployment_config.resource_center.get("no_authentication_required", False)
        access_key_id = deployment_config.resource_center.get("access_key_id", None)
        secret_access_key = deployment_config.resource_center.get("secret_access_key", None)
        endpoint = deployment_config.resource_center.get("endpoint", None)
        region = deployment_config.resource_center.get("region", None)

        s3_resource_center_config = [
            "# S3 resource center configuration",
            f'export RESOURCE_STORAGE_TYPE="S3"',
            f'export RESOURCE_UPLOAD_PATH="{bucket_name}"'
        ]
        if not no_authentication_required:
            debug_handler(message = "S3 resource center authentication required")
            s3_resource_center_config += [
                'export AWS_CREDENTIALS_PROVIDER_TYPE="AWSStaticCredentialsProvider"',
                f'export AWS_ACCESS_KEY_ID="{access_key_id}"',
                f'export AWS_SECRET_ACCESS_KEY="{secret_access_key}"'
            ]
            if endpoint:
                s3_resource_center_config += [
                    'export AWS_ENDPOINT="{endpoint}"'
                ]
            if region:
                s3_resource_center_config += [
                    f'export AWS_REGION="{region}"'
                ]
        else:
            debug_handler(message = "S3 resource center no authentication required")
            s3_resource_center_config += [
                'export AWS_CREDENTIALS_PROVIDER_TYPE="InstanceProfileCredentialsProvider"'
            ]
        debug_handler(message = f"S3 resource center configuration: {s3_resource_center_config}")
        return s3_resource_center_config

    @staticmethod
    def oss_config():
        """
        oss_config | OSS资源中心配置
        :return:
        """
        bucket_name = deployment_config.resource_center.get("bucket_name", "whalestudio-resources")
        access_key_id = deployment_config.resource_center.get("access_key_id", None)
        secret_access_key = deployment_config.resource_center.get("secret_access_key", None)
        region = deployment_config.resource_center.get("region", None)
        endpoint = deployment_config.resource_center.get("endpoint", None)
        oss_resource_center_config = [
            "# OSS resource center configuration",
            'export RESOURCE_STORAGE_TYPE="OSS"',
            f'export RESOURCE_ALIBABA_CLOUD_ACCESS_KEY_ID="{access_key_id}"',
            f'export RESOURCE_ALIBABA_CLOUD_ACCESS_KEY_SECRET="{secret_access_key}"',
            f'export RESOURCE_ALIBABA_CLOUD_REGION="{region}"',
            f'export RESOURCE_ALIBABA_CLOUD_OSS_BUCKET_NAME="{bucket_name}"',
            f'export RESOURCE_ALIBABA_CLOUD_OSS_ENDPOINT="{endpoint}"'
        ]
        debug_handler(message = f"OSS resource center configuration: {oss_resource_center_config}")
        return oss_resource_center_config
