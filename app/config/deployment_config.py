#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : deployment_config.py
# @Time    : 2025/06/03 16:51
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys
import yaml
import traceback
from functools import lru_cache
from dataclasses import dataclass
# from app.common.utils import get_parent_directory
from app.common.logging_utils import info_handler, error_handler, debug_handler

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


@dataclass
class DeploymentConfigSchema:
    root_user: bool = False
    roles_memory_size: dict = None
    maintenance_token: str = None  # 维护模式token
    keep_package: bool = True  # 是否保留安装包
    # single_machine: bool = True  # 是否单机部署
    deployment_mode: str = "standalone"

    deploy_whaletunnel: bool = False  # 是否部署whaletunnel
    deployment_dir: str = "/data/whalestudio"  # 部署目录
    package_dir: str = "/data/whalestudio/packages"
    service_log_dir: str = "/data/whalestudio/logs"  # 服务日志目录
    data_basedir_path: str = "/data/whalestudio/data_basedir"  # 用户数据缓存目录
    datasource_encrypted: bool = True  # 数据源加密
    tenant_enable: bool = True  # 是否启用租户模式
    api_workflow_address: str = "http://localhost:12345"  # API 地址
    local_ip: str = "127.0.0.1"  # 本地IP地址
    download_port: int = 18889  # 下载端口
    reset_password_email: dict = None  # 重置密码邮件配置
    registration_center: dict = None  # 注册中心配置
    metabase: dict = None
    system_environment_path: str = "/etc/profile"  # 系统环境变量配置文件路径
    process_pool_size: int = 10  # 进程池大小
    unified_deployment_user: dict = None  # 统一部署用户配置
    cluster_nodes: dict = None  # 集群节点配置
    whalestudio_environment: dict = None
    resource_center: dict = None  # 资源中心配置
    skip_pre_check: bool = False  # 是否跳过预检查

    def __post_init__(self):
        """
        Initialize the deployment schema. | 初始化部署模式
        """
        if self.roles_memory_size is None:
            self.roles_memory_size = {
                "api": 2,
                "master": 4,
                "worker": 6,
                "alert": 1,
                "whaletunnel": 8
            }
        for service in [
            "api",
            "master",
            "worker",
            "alert",
            "whaletunnel"
        ]:
            if service not in self.roles_memory_size:
                match service:
                    case "api":
                        self.roles_memory_size[service] = 2
                    case "master":
                        self.roles_memory_size[service] = 4
                    case "worker":
                        self.roles_memory_size[service] = 6
                    case "alert":
                        self.roles_memory_size[service] = 1
                    case "whaletunnel":
                        self.roles_memory_size[service] = 8


@lru_cache(maxsize = 1)
def _deployment_config():
    """
    Get the deployment configuration. | 获取部署配置
    :return:
    """
    # deployment_config_path = os.path.join(
    #     get_parent_directory(
    #         path = CURRENT_DIRECTORY,
    #         level = 2
    #     ),
    #     "config",
    #     "deployment.yaml"
    # )
    deployment_config_path = os.getenv(
        "DEPLOYMENT_CONFIG_PATH",
        os.path.join(
            ".",
            "config",
            "deployment.yaml"
        )
    )
    if not os.path.exists(deployment_config_path):
        error_handler(message = f"Deployment configuration file not found: {deployment_config_path}")
        sys.exit(1)

    if not deployment_config_path.endswith(".yaml"):
        error_handler(message = f"Deployment configuration file must be a YAML file: {deployment_config_path}")
        sys.exit(1)

    try:
        with open(deployment_config_path, "r") as deployment_config_file:
            return DeploymentConfigSchema(**yaml.safe_load(deployment_config_file))
    except PermissionError:
        error_handler(
            message = f"Permission denied when reading deployment configuration file. Please check the file permissions. File: {deployment_config_path}"
        )
        sys.exit(1)
    except yaml.YAMLError as e:
        error_handler(
            message = f"Error when parsing deployment configuration file. Please check the file format. File: {deployment_config_path}, Error: {e}"
        )
        sys.exit(1)
    except Exception as e:
        error_handler(
            message = f"Error when loading deployment configuration file. Please check the file content. File: {deployment_config_path}, Error: {e}"
        )
        debug_handler(message = traceback.format_exc())
        sys.exit(1)


deployment_config = _deployment_config()


@lru_cache(maxsize = 1)
def load_env_command():
    """
    Load the environment variables command. | 加载环境变量命令
    :return:
    """
    if isinstance(deployment_config.system_environment_path, str):
        file_paths = deployment_config.system_environment_path.split(",")
        return " && ".join(
            [
                f"source {file_path.strip()}" for file_path in file_paths
            ]
        )
    elif isinstance(deployment_config.system_environment_path, list):
        return " && ".join(
            [
                f"source {file_path}" for file_path in deployment_config.system_environment_path
            ]
        )
    else:
        return "source /etc/profile"


def service_operation_command(service_name, operation_name):
    """
    get_service_operation_command | 获取服务操作命令
    :param service_name:
    :param operation_name:
    :return:
    """
    whaleTunnel_operation_commands = {
        "start": f"{load_env_command()} && /bin/bash {os.path.join(deployment_config.deployment_dir, 'current', 'whaletunnel', 'bin', 'seatunnel-cluster.sh')} -d",
        "status": f'pgrep -f "{deployment_config.deployment_dir}.*whaletunnel"',
        "logs": os.path.join(
            deployment_config.deployment_dir, 'current', 'whaletunnel', 'logs', 'seatunnel-engine-server.log'
        ),
    }
    if service_name == "whaletunnel":
        return whaleTunnel_operation_commands.get(operation_name, None)

    scheduler_startup_script = os.path.join(
        deployment_config.deployment_dir, "current", "whalestudio", "bin", "whalescheduler-daemon.sh"
    )

    service_operation_commands = {
        "start": f"{load_env_command()} && /bin/bash {scheduler_startup_script} start {service_name}-server",

        "status": f'pgrep -f "{deployment_config.deployment_dir}.*{service_name}-server"',
        "logs": os.path.join(
            deployment_config.service_log_dir, f'{service_name}-server', f'whalescheduler-{service_name}.log'
        ),
    }
    return service_operation_commands.get(operation_name, None)
