#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whale_studio_operation.py
# @Time    : 2025/06/04 17:17
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import subprocess
import sys

from app.common.utils import task_running_time
from app.config.deployment_config import deployment_config
from app.config.deployment_config import service_operation_command
from app.common.logging_utils import error_handler, info_handler, warning_handler, title_handler, debug_handler, console
from app.standaone.tools.command_execution_tools import execute_command
from app.standaone.tools.file_tool import FileTool
from app.standaone.tools.tail_logs_tools import tail_logs

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class WhaleStudioOperation:
    def __init__(self, operation, service_names):
        self.operation = operation
        self.default_services = [
            "api",
            "master",
            "worker",
            "alert"
        ]
        if deployment_config.deploy_whaletunnel:
            self.default_services.append("whaletunnel")
        if service_names == "all":
            self.service_names = self.default_services
        else:
            if isinstance(service_names, str):
                self.service_names = [service_name.strip() for service_name in service_names.split(",")]
            elif isinstance(service_names, list):
                self.service_names = service_names
            else:
                error_handler(message = "Invalid service names format. Must be a string or a list.")
                sys.exit(1)

    def run(self):
        # 不合法的服务名
        invalid_service_names = [service_name for service_name in self.service_names if
                                 service_name not in self.default_services]
        # 合法的服务名
        valid_service_names = [service_name for service_name in self.service_names if
                               service_name in self.default_services]
        if invalid_service_names:
            error_handler(message = "Invalid service names: ")
            for invalid_service_name in invalid_service_names:
                error_handler(message = f"    - {invalid_service_name}")
            error_handler(message = "The above service names are illegal and will be ignored.")
        if not valid_service_names and invalid_service_names:
            error_handler(
                message = "After ignoring all illegal service names, there are no valid service names to perform the operation Please check the entered service names."
            )
            sys.exit(1)
        if not valid_service_names and not invalid_service_names:
            error_handler(
                message = "There are no valid service names to perform the operation. Please check the entered service names."
            )
            sys.exit(1)
        title_handler(message = f"{self.operation} WhaleStudio services".center(100, "="))

        match self.operation:
            case "start":
                self._start_services(valid_service_names)
            case "stop":
                return self._stop_services(valid_service_names)
            case "restart":
                self._restart_services(valid_service_names)
            case "status":
                self._status_services(valid_service_names)
            case "logs":
                self._logs_service(valid_service_names[0])
            case _:
                error_handler(message = f"Invalid operation: {self.operation}")
                sys.exit(1)

    @task_running_time(task_name = "Start WhaleStudio services")
    def _start_services(self, service_names):
        with console.status(f"Starting WhaleStudio services..."):
            for service_name in service_names:
                if self._get_service_pids(service_name):
                    warning_handler(message = f"{service_name} is already running. No need to start it again.")
                    continue
                # 1. 获取服务的启动命令
                service_start_command = service_operation_command(
                    service_name = service_name,
                    operation_name = "start"
                )
                if not service_start_command:
                    error_handler(message = f"Failed to get the start command of {service_name}.")
                    continue

                # 2. 执行启动命令
                debug_handler(message = f"Executing command: {service_start_command}")
                execute_command_recode, execute_command_output, execute_command_error = execute_command(
                    command = service_start_command
                )
                if execute_command_recode != 0 or execute_command_error:
                    error_handler(message = f"Failed to start {service_name}: {execute_command_error}")
                    continue
                # 3. 打印启动成功信息
                info_handler(message = f"{service_name} has been started successfully.")
                # 4. 打印启动时间

    @task_running_time(task_name = "Stop WhaleStudio services")
    def _stop_services(self, service_names):

        stop_services_status = True

        with console.status(f"Stopping WhaleStudio services..."):
            for service_name in service_names:

                service_pids = self._get_service_pids(service_name)
                if not service_pids:
                    warning_handler(message = f"{service_name} is not running. No need to stop it.")
                    continue
                # 1. 获取服务的停止命令
                service_stop_command = f"kill -9 {' '.join(service_pids)}"
                debug_handler(message = f"Executing command: {service_stop_command}")
                execute_command_recode, execute_command_output, execute_command_error = execute_command(
                    command = service_stop_command
                )
                if execute_command_recode != 0 or execute_command_error:
                    error_handler(message = f"Failed to stop {service_name}: {execute_command_error}")
                    stop_services_status = False
                    continue
                # 2. 打印停止成功信息
                info_handler(message = f"{service_name} has been stopped successfully.")
        return stop_services_status

    @task_running_time(task_name = "Restart WhaleStudio services")
    def _restart_services(self, service_names):
        with console.status(f"Restarting WhaleStudio services..."):
            for service_name in service_names:
                # 1. 检查服务是否正在运行
                service_pids = self._get_service_pids(service_name)
                if service_pids:
                    # 2. 如果服务正在运行，先停止服务
                    service_stop_command = f"kill -9 {' '.join(service_pids)}"
                    debug_handler(message = f"Executing command: {service_stop_command}")
                    execute_command_recode, execute_command_output, execute_command_error = execute_command(
                        command = service_stop_command
                    )
                    if execute_command_recode != 0 or execute_command_error:
                        error_handler(message = f"Failed to stop {service_name}: {execute_command_error}")
                        continue

                # 3. 获取服务的重启命令
                service_restart_command = service_operation_command(
                    service_name = service_name,
                    operation_name = "start"
                )
                if not service_restart_command:
                    error_handler(message = f"Failed to get the restart command of {service_name}.")
                    continue
                # 4. 执行重启命令
                debug_handler(message = f"Executing command: {service_restart_command}")
                execute_command_recode, execute_command_output, execute_command_error = execute_command(
                    command = service_restart_command
                )
                if execute_command_recode != 0 or execute_command_error:
                    error_handler(message = f"Failed to restart {service_name}: {execute_command_error}")
                    continue
                # 5. 打印重启成功信息
                info_handler(message = f"{service_name} has been restarted successfully.")

    @task_running_time(task_name = "Get WhaleStudio services status")
    def _status_services(self, service_names):
        with console.status(f"Getting WhaleStudio services status..."):
            for service_name in service_names:
                service_pids = self._get_service_pids(service_name)
                if not service_pids:
                    warning_handler(message = f"{service_name} is not running.")
                    continue
                debug_handler(message = f"The status of {service_name} is running. PID: {service_pids}")
                info_handler(message = f"{service_name} is running.")

    def _logs_service(self, service_name):
        with console.status(f"[bold green]Getting WhaleStudio {service_name} logs...[/bold green]"):
            service_logs_file_path = service_operation_command(
                service_name = service_name,
                operation_name = "logs"
            )
            if not service_logs_file_path:
                error_handler(message = f"Failed to get the logs command of {service_name}.")
                return
            debug_handler(message = f"Executing command: {service_logs_file_path}")
            if not FileTool.is_file_exists(file_path = service_logs_file_path):
                error_handler(message = f"Failed to get the logs of {service_name}.")
                return
            tail_logs(
                log_file = service_logs_file_path,
            )

    def _get_service_pids(self, service_name):
        """
        Get the status of the service. | 获取服务的状态
        :param service_name: The name of the service. | 服务名称
        :return: The status of the service. | 服务状态
        """
        service_status_command = service_operation_command(
            service_name = service_name,
            operation_name = "status"
        )
        if not service_status_command:
            debug_handler(message = f"Failed to get the status command of {service_name}.")
            return False
        debug_handler(message = f"Executing command: {service_status_command}")
        execute_command_recode, execute_command_output, execute_command_error = execute_command(
            command = service_status_command
        )
        if execute_command_recode != 0 or execute_command_error:
            debug_handler(message = f"Failed to get the status of {service_name}: {execute_command_error}")
            return False

        if execute_command_output:
            return [
                pid for pid in execute_command_output.splitlines() if pid.strip().isdigit()
            ]
