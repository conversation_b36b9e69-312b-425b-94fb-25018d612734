#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_uninstall.py
# @Time    : 2025/06/05 09:09
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
from app.config.deployment_config import deployment_config
from app.common.logging_utils import title_handler, warning_handler, error_handler, info_handler, console
from app.common.utils import task_running_time
from app.standaone.functions.whaleStudio_operation import WhaleStudioOperation
from app.standaone.tools.path_tool import PathTool

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class WhaleStudioUninstaller:
    def __init__(self):
        pass

    @task_running_time(task_name = "Uninstall Whale Studio")
    def uninstall(self):
        """
        Uninstall Whale Studio. | 卸载 Whale Studio.
        :return:
        """
        title_handler("Uninstalling Whale Studio".center(100, "="))
        # 用户输入
        for _ in range(3):
            user_input = input("Are you sure to uninstall Whale Studio? (y/n) ")
            if user_input.lower() in ["y", "yes"]:
                break
            elif user_input.lower() in ["n", "no"]:
                warning_handler(message = "Uninstall cancelled by user.")
                sys.exit(0)
            else:
                warning_handler(message = "Invalid input. Please enter 'y' or 'n'.")
        # 1. 停止所有的服务
        if not WhaleStudioOperation(
                operation = "stop",
                service_names = "all"
        ).run():
            warning_handler(message = "Failed to stop all services. Please stop them manually.")
        # 2. 删除 Whale Studio 目录
        title_handler("Deleting Whale Studio directories".center(100, "="))
        with console.status("[bold green]Deleting...[/bold green]"):
            for catalog_to_be_deleted in [
                os.path.join(
                    deployment_config.deployment_dir, "current"
                ),
                os.path.join(
                    deployment_config.deployment_dir, "current_package"
                ),
                deployment_config.package_dir
            ]:
                if not PathTool.is_directory_exist(path = catalog_to_be_deleted):
                    warning_handler(message = f"Directory does not exist: {catalog_to_be_deleted}")
                    continue
                delete_status, delete_message = PathTool.delete_directory(
                    path = catalog_to_be_deleted,
                )
                if not delete_status:
                    error_handler(message = f"Failed to delete directory: {delete_message}")
                    continue
                info_handler(message = f"Successfully deleted directory: {catalog_to_be_deleted}")
