#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_db_operation.py
# @Time    : 2025/06/04 18:43
# <AUTHOR> chenyi<PERSON>i
# @Version : 1.0

import os
import sys
import time
import threading
from app.config.deployment_config import deployment_config, load_env_command
from app.config.scheduler_config import metabase_initialization_config, metabase_logs_save_path
from app.common.logging_utils import console, title_handler, error_handler, info_handler, debug_handler
from app.common.utils import task_running_time
from app.standaone.tools.file_tool import FileTool
from app.standaone.tools.command_execution_tools import execute_command
from app.standaone.tools.tail_logs_tools import tail_logs

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class DBOperation:
    def __init__(self, operation_type, show_log = False):
        """
        初始化数据库操作类
        :param operation_type:
        """
        self.operation_type = operation_type
        self.metabase_init_script = os.path.join(
            deployment_config.deployment_dir,
            "current",
            "whalestudio",
            "tools",
            "bin",
            "upgrade-schema.sh"
        )
        self.show_log = show_log
        self.metabase_logs_save_path = os.path.join(
            os.getenv(
                "LOGS_PATH", metabase_logs_save_path()
            ), f"{operation_type}.log"
        )

    def run(self):
        """
        执行数据库操作
        :return:
        """
        if not FileTool.is_file_exists(file_path = self.metabase_init_script):
            error_handler(message = f"Metabase initialization script does not exist! Path: {self.metabase_init_script}")
            error_handler(message = "Please check the deployment directory or deployment status and try again.")
            sys.exit(1)

        self._update_metabase_config()
        if self.show_log != "None":
            log_thread = threading.Thread(target = self._print_log)
            log_thread.daemon = True
            log_thread.start()

        match self.operation_type:
            case "db_init":
                self.db_init()
            case "db_upgrade":
                self.db_upgrade()
            case _:
                error_handler(message = "Database operation type error!")
                sys.exit(1)

    @task_running_time(task_name = "Metabase initialization")
    def db_init(self):
        """
        初始化数据库
        :return:
        """
        title_handler("Metabase database initialization".center(100, "="))
        with console.status(f"[bold green]Metabase database initialization...[/bold green]"):
            metabase_init_status, metabase_init_output, metabase_init_error = execute_command(
                command = f"{load_env_command()} && /bin/bash {self.metabase_init_script} > {self.metabase_logs_save_path} 2>&1",
            )
            debug_handler(
                message = f"Metabase initialization script output: {metabase_init_output}"
            )
            debug_handler(
                message = f"Metabase initialization script error: {metabase_init_error}"
            )
        time.sleep(3)
        if metabase_init_status == 0:
            info_handler(message = f"Metabase database initialization completed! Output: {metabase_init_output}")
            info_handler(message = f"Log file path:{self.metabase_logs_save_path}")
        else:
            error_handler(message = f"Metabase database initialization failed!")
            error_handler(message = "Please view detailed error information through the log file.")
            error_handler(message = f"Log file path:{self.metabase_logs_save_path}")
        sys.exit(0)

    @task_running_time(task_name = "Metabase upgrade")
    def db_upgrade(self):
        """
        数据库升级
        :return:
        """
        title_handler("Metabase database upgrade".center(100, "="))
        with console.status(f"[bold green]Metabase database upgrade...[/bold green]"):
            metabase_upgrade_status, metabase_upgrade_output, metabase_upgrade_error = execute_command(
                command = f"{load_env_command()} && /bin/bash {self.metabase_init_script} > {self.metabase_logs_save_path} 2>&1",
            )
            time.sleep(3)
        if metabase_upgrade_status == 0:
            info_handler(message = f"Metabase database upgrade completed! Output: {metabase_upgrade_output}")
            info_handler(message = f"Log file path:{self.metabase_logs_save_path}")
        else:
            error_handler(message = f"Metabase database upgrade failed!")
            error_handler(message = "Please view detailed error information through the log file.")
            error_handler(message = f"Log file path:{self.metabase_logs_save_path}")
        sys.exit(0)

    def _update_metabase_config(self):
        """
        更新 metabase 配置文件
        :return:
        """
        metabase_config_path = os.path.join(
            deployment_config.deployment_dir,
            "current",
            "whalestudio",
            "tools",
            "conf",
            "whalescheduler_env.sh"
        )
        write_status, write_message = FileTool.write_file(
            file_path = metabase_config_path,
            content = metabase_initialization_config(),
            file_type = "text"
        )
        if not write_status:
            error_handler(message = f"Failed to update metabase configuration file! Error message: {write_message}")
            sys.exit(1)

    def _print_log(self):
        """
        打印日志
        :return:
        """
        # 循环20秒，等待日志文件生成
        for i in range(20):
            if not FileTool.is_file_exists(file_path = self.metabase_logs_save_path):
                time.sleep(1)
                continue
            break
        tail_logs(
            log_file = self.metabase_logs_save_path,
            lines = 20
        )
