#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : main.py
# @Time    : 2025/06/03 17:04
# <AUTHOR> chen<PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def standalone_main(operation_type, *args, **kwargs):
    """
    Main function for standalone operations. | 独立操作的主函数
    :param operation_type: 操作类型
    :param args:
    :param kwargs:
    :return:
    """
    match operation_type.lower():
        case "install":
            from app.standaone.functions.whaleStudio_install import WhaleStudioInstaller
            WhaleStudioInstaller().install()
        case "pre_check":
            from app.standaone.functions.pre_check import PreDeploymentCheck
            PreDeploymentCheck().run()
        case "config_update":
            from app.standaone.functions.whaleStudio_configuration_update import ConfigurationFileUpdate
            ConfigurationFileUpdate().run()
        case "start" | "stop" | "restart" | "status" | "logs":
            from app.standaone.functions.whaleStudio_operation import WhaleStudioOperation
            WhaleStudioOperation(operation_type, kwargs.get("operation_value", "all")).run()
        case "db_init" | "db_upgrade":
            from app.standaone.functions.whaleStudio_db_operation import DBOperation
            DBOperation(operation_type, show_log = kwargs.get("operation_value", False)).run()
        case "uninstall":
            from app.standaone.functions.whaleStudio_uninstall import WhaleStudioUninstaller
            WhaleStudioUninstaller().uninstall()
        case "clean_packages":
            from app.standaone.functions.clean_up_useless_packages import CleanUpUselessPackages
            CleanUpUselessPackages().run()
        case "rollback":
            from app.standaone.functions.whaleStudio_rollback import rollback
            rollback()
