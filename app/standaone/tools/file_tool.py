#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : file_tool.py
# @Time    : 2025/06/03 18:05
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import json
import traceback
import shutil
from ruamel.yaml import YAM<PERSON>

from app.common.logging_utils import debug_handler

yaml = YAML()
"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class FileTool:
    """
    File tool class. | 文件工具类
    """

    @staticmethod
    def is_file_exists(file_path: str) -> bool:
        """
        Check if the file exists. | 检查文件是否存在
        :param file_path:
        :return:
        """
        return os.path.exists(file_path)

    @staticmethod
    def write_file(file_path: str, content: str, file_type: str = "txt"):
        """
        Write content to a file. | 将内容写入文件
        :param file_path:
        :param content:
        :param file_type:
        :return:
        """
        try:
            with open(file_path, "w", encoding = "utf-8") as file:
                match file_type:
                    case "yaml":
                        yaml.dump(content, file)
                    case "json":
                        json.dump(content, file, indent = 4)
                    case _:
                        if isinstance(content, list):
                            file.write("\n".join(content))
                        else:
                            file.write(content)
            return True, "Write file success."
        except PermissionError:
            return False, "Permission denied. Please check the file path and permissions."
        except Exception as e:
            debug_handler(
                message = f"Error writing file: {e}. the file path is {file_path}. traceback: {traceback.format_exc()}"
            )
            return False, str(e)

    @staticmethod
    def read_file(file_path: str, file_type: str = "txt"):
        """
        Read content from a file. | 从文件读取内容
        :param file_path:
        :param file_type:
        :return:
        """
        try:
            with open(file_path, "r", encoding = "utf-8") as file:
                match file_type:
                    case "yaml":
                        content = yaml.load(file)
                    case "json":
                        content = json.load(file)
                    case _:
                        content = file.readlines()
            return True, content
        except FileNotFoundError:
            return False, "File not found."
        except Exception as e:
            debug_handler(
                f"Error reading file: {e}. the file path is {file_path}. traceback: {traceback.format_exc()}"
            )
            return False, str(e)

    @staticmethod
    def delete_file(file_path: str):
        """
        Delete a file. | 删除文件
        :param file_path:
        :return:
        """
        try:
            os.remove(file_path)
            return True, "Delete file success."
        except FileNotFoundError:
            return True, "File not found."
        except Exception as e:
            debug_handler(
                f"Error deleting file: {e}. the file path is {file_path}. traceback: {traceback.format_exc()}"
            )
            return False, str(e)

    @staticmethod
    def sync_file(src_file_path: str, dst_file_path: str):
        """
        Synchronize the content of two files. | 同步两个文件的内容
        :param src_file_path:
        :param dst_file_path:
        :return:
        """
        try:
            shutil.copyfile(src_file_path, dst_file_path)
            return True, "Sync file success."
        except Exception as e:
            debug_handler(
                f"Error syncing file: {e}. the src file path is {src_file_path}. the dst file path is {dst_file_path}. traceback: {traceback.format_exc()}"
            )
            return False, str(e)
