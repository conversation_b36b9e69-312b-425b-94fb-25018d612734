#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : path_tool.py
# @Time    : 2025/06/03 18:01
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import shutil
import sys
import traceback
from app.common.logging_utils import debug_handler

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class PathTool:
    """
    A class to handle path-related operations. | 处理路径相关操作的类
    """

    @staticmethod
    def is_directory_exist(path):
        """
        Check if a path exists. | 检查路径是否存在
        :param path: The path to check. | 要检查的路径
        :return: True if the path exists, False otherwise. | 如果路径存在，返回True，否则返回False
        """
        return os.path.exists(path)

    @staticmethod
    def create_directory(path):
        """
        Create a directory if it does not exist. | 如果目录不存在，则创建目录
        :param path: The path of the directory to create. | 要创建的目录路径
        """
        try:
            os.makedirs(path)
            return True, "Directory created successfully."
        except PermissionError:
            return False, "Permission denied."
        except FileExistsError:
            return False, "Directory already exists."
        except Exception as e:
            debug_handler(message = f"Create directory: {path} failed, traceback: {traceback.format_exc()}")
            return False, str(e)

    @staticmethod
    def get_symlink_target(sub_paths):
        """
        Get the target of a symbolic link. | 获取符号链接的目标
        :param sub_paths: The sub-paths to check. | 要检查的子路径
        :return:
        """

        symlink_map = {}

        for sub_path in sub_paths:
            if os.path.islink(sub_path):
                target = os.readlink(sub_path)
                symlink_map[sub_path] = target
                debug_handler(message = f"Symbolic link found: {sub_path} -> {target}")
            else:
                debug_handler(message = f"Not a symbolic link: {sub_path}")

        return symlink_map

    @staticmethod
    def delete_symlink(symlink_path):
        """
        Delete a symbolic link. | 删除符号链接
        :param symlink_path: The path of the symbolic link to delete. | 要删除的符号链接路径
        :return:
        """
        if not os.path.islink(symlink_path):
            debug_handler(message = f"Not a symbolic link: {symlink_path}")
            return True, "Not a symbolic link."
        try:
            os.unlink(path = symlink_path)
            debug_handler(message = f"Symbolic link deleted: {symlink_path}")
            return True, "Symbolic link deleted successfully."
        except Exception as e:
            debug_handler(
                message = f"Failed to delete symbolic link: {symlink_path}, traceback: {traceback.format_exc()}"
            )
            return False, str(e)

    @staticmethod
    def create_symlink(symlink_path, target_path):
        """
        Create a symbolic link. | 创建符号链接
        :param symlink_path: The source path of the symbolic link. | 符号链接的源路径
        :param target_path: The target path of the symbolic link. | 符号链接的目标路径
        :return:
        """
        try:
            os.symlink(src = target_path, dst = symlink_path)
            debug_handler(message = f"Symbolic link created: {target_path} -> {symlink_path}")
            return True, "Symbolic link created successfully."
        except Exception as e:
            debug_handler(
                message = f"Failed to create symbolic link: {target_path} -> {symlink_path}, traceback: {traceback.format_exc()}"
            )
            return False, str(e)

    @staticmethod
    def delete_directory(path):
        """
        Delete a directory. | 删除目录
        :param path: The path of the directory to delete. | 要删除的目录路径
        :return:
        """
        try:
            shutil.rmtree(path)
            debug_handler(message = f"Directory deleted: {path}")
            return True, "Directory deleted successfully."
        except OSError:
            return True, "Directory is not empty."
        except Exception as e:
            debug_handler(message = f"Failed to delete directory: {path}, traceback: {traceback.format_exc()}")
            return False, str(e)
