# WhaleStudio 构建工具优化版

## 主要改进

### 1. 代码结构优化
- **模块化设计**：将功能拆分为独立的类，提高代码可维护性
- **配置管理**：统一的配置管理类，支持环境变量
- **错误处理**：改进的异常处理和日志记录
- **类型注解**：添加完整的类型注解，提高代码可读性

### 2. 安全性改进
- **SQL注入防护**：使用参数化查询替代字符串拼接
- **敏感信息保护**：支持环境变量配置，避免硬编码密码
- **输入验证**：增强参数验证和错误处理

### 3. 性能优化
- **数据库连接管理**：优化数据库连接的创建和释放
- **异步处理**：保持MD5计算的异步特性
- **资源管理**：改进文件和网络资源的管理

### 4. 功能增强
- **日志系统**：使用loguru提供更好的日志体验
- **参数解析**：改进的命令行参数处理
- **错误恢复**：更好的错误处理和恢复机制

## 使用方法

### 1. 环境配置

```bash
# 复制配置文件模板
cp config.env.example config.env

# 编辑配置文件，填入实际值
vim config.env

# 设置环境变量
source config.env
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 基本用法

```bash
# 获取版本号
python whaleStudio_build_optimized.py --operation get_version --branch main

# 上传文件到OSS
python whaleStudio_build_optimized.py --operation upload_to_oss \
    --file /path/to/package.tar.gz \
    --latest_version 1.0.1-main

# 发送构建完成消息
python whaleStudio_build_optimized.py --operation send_end_message \
    --build_status success \
    --latest_version 1.0.1-main \
    --jenkins_url "http://jenkins.example.com/job/build/123/" \
    --commit_id "abc1234,def5678,ghi9012,jkl3456" \
    --build_path "/path/to/build"

# 更新提交ID
python whaleStudio_build_optimized.py --operation update_commit_id \
    --latest_version 1.0.1-main \
    --commit_id "abc1234,def5678,ghi9012,jkl3456"

# 删除版本
python whaleStudio_build_optimized.py --operation delete_version \
    --latest_version 1.0.1-main

# 发送确认消息
python whaleStudio_build_optimized.py --operation send_confirmation_message \
    --branch main \
    --jenkins_url "http://jenkins.example.com/job/build/123/"
```

### 4. 客户特定构建

```bash
# 为特定客户构建
python whaleStudio_build_optimized.py --operation get_version \
    --branch main \
    --customer zhongxinjiantou
```

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| MYSQL_HOST | 数据库主机 | *********** |
| MYSQL_USER | 数据库用户 | root |
| MYSQL_PASSWORD | 数据库密码 | - |
| MYSQL_DATABASE | 数据库名 | jenkins_db |
| MYSQL_PORT | 数据库端口 | 3306 |
| OSS_ACCESS_KEY_ID | OSS访问密钥ID | - |
| OSS_ACCESS_KEY_SECRET | OSS访问密钥 | - |
| OSS_ENDPOINT | OSS端点 | - |
| OSS_BUCKET_NAME | OSS存储桶名 | whale-ops |
| WEBHOOK_URL | 飞书Webhook地址 | - |

### 支持的操作

- `get_version`: 获取并生成新版本号
- `update_commit_id`: 更新提交ID
- `upload_to_oss`: 上传文件到OSS
- `send_confirmation_message`: 发送确认消息
- `send_end_message`: 发送结束消息
- `delete_version`: 删除版本

### 支持的构建状态

- `success`: 构建成功
- `error`: 构建失败
- `timeout`: 构建超时
- `cancel`: 构建取消

## 日志

日志文件位置：`logs/whaleStudio_build.log`

日志特性：
- 自动轮转（20MB）
- 保留30天
- 同时输出到控制台和文件
- 彩色控制台输出

## 错误处理

优化版本提供了更好的错误处理：
- 详细的错误信息和堆栈跟踪
- 数据库事务回滚
- 资源自动清理
- 优雅的异常处理

## 迁移指南

从原版本迁移到优化版本：

1. **备份数据**：确保数据库和重要文件已备份
2. **更新配置**：使用环境变量替代硬编码配置
3. **测试功能**：在测试环境中验证所有功能
4. **逐步替换**：逐步将原脚本替换为优化版本

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 确认网络连接
   - 验证用户权限

2. **OSS上传失败**
   - 检查OSS配置
   - 验证访问权限
   - 确认网络连接

3. **飞书消息发送失败**
   - 检查Webhook URL
   - 验证消息格式
   - 确认网络连接

### 调试模式

设置环境变量启用调试模式：
```bash
export LOG_LEVEL=DEBUG
python whaleStudio_build_optimized.py --operation get_version --branch main
```
