#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书消息发送脚本
将 curl 命令转换为 Python 代码
"""

import requests
import json
from typing import Dict, Any, Optional


def send_feishu_message(
    token: str,
    receive_id: str,
    message: str,
    receive_id_type: str = "chat_id",
    msg_type: str = "text"
) -> Dict[str, Any]:
    """
    发送飞书消息
    
    Args:
        token: 访问令牌
        receive_id: 接收者ID
        message: 消息内容
        receive_id_type: 接收者ID类型，默认为 chat_id
        msg_type: 消息类型，默认为 text
        
    Returns:
        Dict[str, Any]: API响应结果
    """
    # API URL
    url = f"https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type={receive_id_type}"
    
    # 请求头
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    # 请求体
    data = {
        "content": json.dumps({"text": message}),
        "msg_type": msg_type,
        "receive_id": receive_id
    }
    
    try:
        # 发送POST请求
        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()
        
        # 返回响应结果
        result = response.json()
        print(f"消息发送成功: {result}")
        return result
        
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return {"error": str(e)}
    except Exception as e:
        print(f"发送消息失败: {e}")
        return {"error": str(e)}


def main():
    """主函数 - 演示如何使用"""
    # 从您的 curl 命令中提取的参数
    token = "t-g1047nh2WCRXAAFSUBTVNA4NJOTZRIDZ2HKQQW4C"
    receive_id = "oc_f5a3b6e372cfeae27f91638e142d7dbe"
    message = "<at user_id=\"all\"></at>当前是2025年7月第4周的周四请大家及时填写线上问题回顾"
    
    # 发送消息
    result = send_feishu_message(
        token=token,
        receive_id=receive_id,
        message=message
    )
    
    # 打印结果
    print("发送结果:", json.dumps(result, indent=2, ensure_ascii=False))


# 直接对应您的 curl 命令的版本
def send_message_curl_style():
    """
    直接对应 curl 命令的 Python 版本
    """
    url = "https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=chat_id"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer t-g1047nh2WCRXAAFSUBTVNA4NJOTZRIDZ2HKQQW4C"
    }
    
    data = {
        "content": "{\"text\":\"<at user_id=\\\"all\\\"></at>当前是2025年7月第4周的周四请大家及时填写线上问题回顾\"}",
        "msg_type": "text",
        "receive_id": "oc_f5a3b6e372cfeae27f91638e142d7dbe"
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应体: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("消息发送成功!")
            return result
        else:
            print(f"消息发送失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"发送失败: {e}")
        return None


if __name__ == "__main__":
    print("=== 使用封装函数发送 ===")
    main()
    
    print("\n=== 直接对应 curl 命令 ===")
    send_message_curl_style()
