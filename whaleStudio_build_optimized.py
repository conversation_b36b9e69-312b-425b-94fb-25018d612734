#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WhaleStudio 构建工具 - 优化版
@File    : whaleStudio_build_optimized.py
@Time    : 2025/07/24 09:56
<AUTHOR> chenyi<PERSON><PERSON>
@Version : 2.0
"""

import argparse
import hashlib
import json
import os
import sys
import threading
import time
import traceback
from datetime import datetime
from pathlib import Path
from queue import Queue
from typing import Optional, Tuple, Dict, Any, List

import git
import oss2
import requests
from loguru import logger
from pymysql import connect
from oss2 import Auth, Bucket


class Config:
    """配置管理类"""
    
    # 基础配置
    CURRENT_DIRECTORY = Path(__file__).parent.absolute()
    LOGS_DIR = CURRENT_DIRECTORY / "logs"
    
    # 数据库配置 - 建议使用环境变量
    MYSQL_HOST = os.getenv("MYSQL_HOST", "***********")
    MYSQL_USER = os.getenv("MYSQL_USER", "root")
    MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD", "QWer12#$")
    MYSQL_DATABASE = os.getenv("MYSQL_DATABASE", "jenkins_db")
    MYSQL_PORT = int(os.getenv("MYSQL_PORT", "3306"))
    
    # OSS配置 - 建议使用环境变量
    OSS_ACCESS_KEY_ID = os.getenv("OSS_ACCESS_KEY_ID", "LTAI5tQ5SWAFG55MSEuQLFsY")
    OSS_ACCESS_KEY_SECRET = os.getenv("OSS_ACCESS_KEY_SECRET", "******************************")
    OSS_ENDPOINT = os.getenv("OSS_ENDPOINT", "https://oss-cn-wulanchabu-internal.aliyuncs.com")
    OSS_BUCKET_NAME = os.getenv("OSS_BUCKET_NAME", "whale-ops")
    
    # 下载链接配置
    PUBLIC_DOWNLOAD_LINK = "https://whale-ops.oss-cn-wulanchabu.aliyuncs.com"
    INTRANET_DOWNLOAD_LINK = "https://whale-ops.oss-cn-wulanchabu-internal.aliyuncs.com"
    
    # 飞书配置
    WEBHOOK_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/053a9872-d18f-4d3d-a433-6c3fef797e4c"
    
    # 客户列表
    CUSTOMER_LIST = {
        "zhongxinjiantou": "中信建投",
        "guangdalicai": "光大理财", 
        "renshou": "人寿",
        "renbao": "人保"
    }


class LoggerSetup:
    """日志设置类"""
    
    @staticmethod
    def setup_logger():
        """设置日志配置"""
        # 确保日志目录存在
        Config.LOGS_DIR.mkdir(exist_ok=True)
        
        # 移除默认处理器
        logger.remove()
        
        # 添加文件日志
        logger.add(
            Config.LOGS_DIR / "whaleStudio_build.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
            mode='a',
            encoding='utf-8',
            level="INFO",
            rotation="20 MB",
            retention="30 days"
        )
        
        # 添加控制台日志
        logger.add(
            sys.stderr,
            format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | {message}",
            level="INFO"
        )


class DatabaseManager:
    """数据库管理类"""
    
    @staticmethod
    def get_connection():
        """获取数据库连接"""
        try:
            conn = connect(
                host=Config.MYSQL_HOST,
                user=Config.MYSQL_USER,
                password=Config.MYSQL_PASSWORD,
                port=Config.MYSQL_PORT,
                database=Config.MYSQL_DATABASE,
                charset='utf8mb4'
            )
            return conn
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    @staticmethod
    def execute_query(sql: str, params: tuple = None, fetch_one: bool = False, fetch_all: bool = False):
        """执行SQL查询"""
        conn = None
        cursor = None
        try:
            conn = DatabaseManager.get_connection()
            cursor = conn.cursor()
            
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            
            if fetch_one:
                return cursor.fetchone()
            elif fetch_all:
                return cursor.fetchall()
            else:
                conn.commit()
                return cursor.rowcount
                
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"SQL执行失败: {sql}, 参数: {params}, 错误: {e}")
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()


class FileUtils:
    """文件工具类"""
    
    @staticmethod
    def calculate_md5(file_path: str, chunk_size: int = 8192) -> str:
        """计算文件MD5值"""
        logger.info(f"开始计算文件MD5: {file_path}")
        start_time = time.time()
        
        try:
            md5_hash = hashlib.md5()
            with open(file_path, 'rb') as f:
                while chunk := f.read(chunk_size):
                    md5_hash.update(chunk)
            
            result = md5_hash.hexdigest()
            cost_time = time.time() - start_time
            logger.info(f"MD5计算完成: {result}, 耗时: {cost_time:.2f}秒")
            return result
            
        except Exception as e:
            logger.error(f"MD5计算失败: {e}")
            raise
    
    @staticmethod
    def calculate_md5_async(file_path: str, result_queue: Queue):
        """异步计算文件MD5"""
        try:
            md5_value = FileUtils.calculate_md5(file_path)
            result_queue.put(md5_value)
        except Exception as e:
            logger.error(f"异步MD5计算失败: {e}")
            result_queue.put(None)


class MessageTemplates:
    """消息模板类"""
    
    CONFIRMATION_MESSAGE = '''
    {{
      "config": {{
        "wide_screen_mode": true
      }},
      "elements": [
        {{
          "tag": "div",
          "text": {{
            "content": "**请点击[链接]({jenkins_url})进行{build_message}**",
            "tag": "lark_md"
          }}
        }}
      ],
      "header": {{
        "template": "blue",
        "title": {{
          "content": "📢  {branch}",
          "tag": "plain_text"
        }}
      }}
    }}
    '''
    
    END_MESSAGE = '''
    {{
      "config": {{
        "wide_screen_mode": true
      }},
      "elements": [
        {{
            "tag": "div",
            "text": {{
                "content": "{content}",
                "tag": "lark_md"
            }}
        }}
      ],
      "header": {{
        "template": "{color}",
        "title": {{
          "content": "📢  {branch}",
          "tag": "plain_text"
        }}
      }}
    }}
    '''


class FeishuNotifier:
    """飞书通知类"""
    
    @staticmethod
    def send_message(message_data: dict) -> bool:
        """发送飞书消息"""
        try:
            headers = {"Content-Type": "application/json"}
            data = json.dumps(message_data, ensure_ascii=False)
            
            logger.info(f"发送飞书消息: {data}")
            response = requests.post(
                url=Config.WEBHOOK_URL,
                data=data.encode('utf-8'),
                headers=headers,
                timeout=30
            )
            
            response.raise_for_status()
            logger.info(f"飞书消息发送成功: {response.text}")
            return True
            
        except Exception as e:
            logger.error(f"飞书消息发送失败: {e}")
            return False


class OSSUploader:
    """OSS上传类"""
    
    def __init__(self):
        try:
            auth = Auth(Config.OSS_ACCESS_KEY_ID, Config.OSS_ACCESS_KEY_SECRET)
            self.bucket = Bucket(auth, Config.OSS_ENDPOINT, Config.OSS_BUCKET_NAME)
        except Exception as e:
            logger.error(f"OSS初始化失败: {e}")
            raise
    
    def upload_file(self, local_path: str, oss_path: str) -> Tuple[str, str]:
        """
        上传文件到OSS
        
        Returns:
            Tuple[str, str]: (公网下载链接, 内网下载链接)
        """
        try:
            logger.info(f"开始上传文件到OSS: {local_path} -> {oss_path}")
            start_time = time.time()
            
            # 上传文件
            self.bucket.put_object_from_file(key=oss_path, filename=local_path)
            
            # 设置公共读权限
            self.bucket.put_object_acl(key=oss_path, permission=oss2.OBJECT_ACL_PUBLIC_READ)
            
            cost_time = (time.time() - start_time) / 60
            logger.info(f"文件上传成功，耗时: {cost_time:.2f}分钟")
            
            # 生成下载链接
            public_link = f"{Config.PUBLIC_DOWNLOAD_LINK}/{oss_path}"
            intranet_link = f"{Config.INTRANET_DOWNLOAD_LINK}/{oss_path}"
            
            return public_link, intranet_link
            
        except Exception as e:
            logger.error(f"文件上传失败: {e}")
            raise


class VersionManager:
    """版本管理类"""
    
    @staticmethod
    def record_version(branch: str, version: str, customer: Optional[str] = None):
        """记录版本号到数据库"""
        try:
            if customer:
                sql = "INSERT INTO minor_version_number (branch, customer, minor_version) VALUES (%s, %s, %s)"
                params = (branch, customer, version)
            else:
                sql = "INSERT INTO minor_version_number (branch, minor_version) VALUES (%s, %s)"
                params = (branch, version)
            
            DatabaseManager.execute_query(sql, params)
            logger.info(f"版本号记录成功: {version}")
            
        except Exception as e:
            logger.error(f"版本号记录失败: {e}")
            raise
    
    @staticmethod
    def get_latest_version(branch: str, customer: Optional[str] = None) -> Optional[str]:
        """获取最新版本号"""
        try:
            if customer:
                sql = "SELECT minor_version FROM minor_version_number WHERE branch = %s AND customer IS NULL ORDER BY id DESC LIMIT 1"
                params = (branch,)
            else:
                sql = "SELECT minor_version FROM minor_version_number WHERE branch = %s ORDER BY id DESC LIMIT 1"
                params = (branch,)
            
            result = DatabaseManager.execute_query(sql, params, fetch_one=True)
            return result[0] if result else None
            
        except Exception as e:
            logger.error(f"获取最新版本失败: {e}")
            raise
    
    @staticmethod
    def generate_new_version(current_version: Optional[str], branch: str) -> str:
        """生成新版本号"""
        if not current_version:
            return f"1.0.1-{branch}"
        
        try:
            # 解析版本号
            version_part, branch_part = current_version.split("-", 1)
            version_parts = version_part.split(".")
            
            if len(version_parts) == 2:
                major, minor = version_parts
                new_version = f"{major}.{minor}.1"
            elif len(version_parts) == 3:
                major, minor, patch = version_parts
                new_version = f"{major}.{minor}.{int(patch) + 1}"
            else:
                raise ValueError(f"版本号格式错误: {current_version}")
            
            return f"{new_version}-{branch_part}"
            
        except Exception as e:
            logger.error(f"版本号生成失败: {e}")
            raise


class GitManager:
    """Git管理类"""

    @staticmethod
    def get_commit_messages(repo_path: str, from_commit: str, to_commit: str = "HEAD") -> Dict[str, str]:
        """获取提交信息"""
        try:
            repo = git.Repo(repo_path)
            commits = list(repo.iter_commits(f"{from_commit}..{to_commit}"))

            if not commits:
                return {}

            commit_messages = {}
            for commit in commits:
                commit_id = commit.hexsha[:7]
                # 过滤和清理提交信息
                message_lines = [
                    line.strip().lstrip("* ")
                    for line in commit.message.split('\n')
                    if line.strip() and not line.startswith((
                        "Co-authored-by", "* improve", "* update", "* optimize", "--"
                    ))
                ]

                if message_lines:
                    commit_messages[commit_id] = "\n".join(set(message_lines))

            return commit_messages

        except Exception as e:
            logger.error(f"获取提交信息失败: {e}")
            return {}

    @staticmethod
    def get_latest_commit_message(repo_path: str) -> List[str]:
        """获取最新提交信息"""
        try:
            repo = git.Repo(repo_path)
            commit = repo.head.commit

            message_lines = [
                line.strip().lstrip("* ")
                for line in commit.message.split("\n")
                if line.strip() and not line.startswith((
                    "Co-authored-by", "* improve", "* update", "* optimize", "--"
                ))
            ]

            return message_lines

        except Exception as e:
            logger.error(f"获取最新提交信息失败: {e}")
            return []


class BuildWhaleStudioOptimized:
    """WhaleStudio构建类 - 优化版"""

    def __init__(self, **kwargs):
        self.operation = kwargs.get('operation')
        self.branch = kwargs.get('branch')
        self.customer = kwargs.get('customer')
        self.file_path = kwargs.get('file_path')
        self.jenkins_url = kwargs.get('jenkins_url')
        self.build_status = kwargs.get('build_status')
        self.latest_version = kwargs.get('latest_version')
        self.build_path = kwargs.get('build_path')

        # 解析commit_id
        commit_id = kwargs.get('commit_id')
        if commit_id:
            try:
                commit_parts = commit_id.split(",")
                if len(commit_parts) == 4:
                    self.scheduler_id, self.scheduler_ui_id, self.tunnel_id, self.tunnel_web_id = commit_parts
                else:
                    logger.warning(f"commit_id格式不正确: {commit_id}")
                    self.scheduler_id = self.scheduler_ui_id = self.tunnel_id = self.tunnel_web_id = "unknown"
            except Exception as e:
                logger.error(f"解析commit_id失败: {e}")
                self.scheduler_id = self.scheduler_ui_id = self.tunnel_id = self.tunnel_web_id = "unknown"
        else:
            self.scheduler_id = self.scheduler_ui_id = self.tunnel_id = self.tunnel_web_id = "unknown"

    def execute(self):
        """执行操作"""
        operations = {
            "get_version": self.get_version,
            "update_commit_id": self.update_commit_id,
            "upload_to_oss": self.upload_to_oss,
            "send_confirmation_message": self.send_confirmation_message,
            "send_end_message": self.send_end_message,
            "delete_version": self.delete_version,
        }

        if self.operation not in operations:
            logger.error(f"不支持的操作: {self.operation}")
            raise ValueError(f"不支持的操作: {self.operation}")

        return operations[self.operation]()

    def get_version(self):
        """获取并生成新版本号"""
        try:
            logger.info(f"获取 {self.branch} 分支版本号")

            # 获取最新版本
            current_version = VersionManager.get_latest_version(self.branch, self.customer)

            # 生成新版本号
            new_version = VersionManager.generate_new_version(current_version, self.branch)

            # 记录新版本
            VersionManager.record_version(self.branch, new_version, self.customer)

            logger.info(f"新版本号: {new_version}")
            print(new_version)
            return new_version

        except Exception as e:
            logger.error(f"获取版本号失败: {e}")
            raise

    def delete_version(self):
        """删除版本号"""
        try:
            if self.customer:
                sql = "DELETE FROM minor_version_number WHERE minor_version = %s AND customer = %s"
                params = (self.latest_version, self.customer)
            else:
                sql = "DELETE FROM minor_version_number WHERE minor_version = %s"
                params = (self.latest_version,)

            DatabaseManager.execute_query(sql, params)
            logger.info(f"版本号删除成功: {self.latest_version}")

        except Exception as e:
            logger.error(f"版本号删除失败: {e}")
            raise

    def update_commit_id(self):
        """更新提交ID"""
        try:
            if self.customer:
                sql = """UPDATE minor_version_number
                        SET whalescheduler_commit = %s, whalescheduler_ui_commit = %s,
                            whaletunnel_commit = %s, whaletunnel_web_commit = %s
                        WHERE minor_version = %s AND customer = %s"""
                params = (self.scheduler_id, self.scheduler_ui_id, self.tunnel_id,
                         self.tunnel_web_id, self.latest_version, self.customer)
            else:
                sql = """UPDATE minor_version_number
                        SET whalescheduler_commit = %s, whalescheduler_ui_commit = %s,
                            whaletunnel_commit = %s, whaletunnel_web_commit = %s
                        WHERE minor_version = %s AND customer IS NULL"""
                params = (self.scheduler_id, self.scheduler_ui_id, self.tunnel_id,
                         self.tunnel_web_id, self.latest_version)

            DatabaseManager.execute_query(sql, params)
            logger.info("提交ID更新成功")

        except Exception as e:
            logger.error(f"提交ID更新失败: {e}")
            raise

    def upload_to_oss(self):
        """上传文件到OSS"""
        try:
            if not os.path.isfile(self.file_path):
                raise FileNotFoundError(f"文件不存在: {self.file_path}")

            logger.info("开始上传文件到OSS")

            # 确定OSS路径
            file_name = os.path.basename(self.file_path)
            oss_path = self._get_oss_path(file_name)

            # 异步计算MD5
            md5_queue = Queue()
            md5_thread = threading.Thread(
                target=FileUtils.calculate_md5_async,
                args=(self.file_path, md5_queue)
            )
            md5_thread.start()

            # 上传文件
            uploader = OSSUploader()
            public_link, intranet_link = uploader.upload_file(self.file_path, oss_path)

            # 等待MD5计算完成
            md5_thread.join()
            package_md5 = md5_queue.get()

            if not package_md5:
                raise RuntimeError("MD5计算失败")

            # 更新数据库
            self._update_download_info(public_link, intranet_link, package_md5)

            logger.info(f"文件上传成功")
            logger.info(f"公网下载链接: {public_link}")
            logger.info(f"内网下载链接: {intranet_link}")
            logger.info(f"文件MD5: {package_md5}")

            print(f"\n内网下载链接: {intranet_link}")
            print(f"公网下载链接: {public_link}")
            print(f"安装包MD5: {package_md5}")

        except Exception as e:
            logger.error(f"文件上传失败: {e}")
            raise

    def _get_oss_path(self, file_name: str) -> str:
        """获取OSS路径"""
        try:
            if file_name.split("-")[-1].startswith("release"):
                oss_path = "three-in-one/release/"
            else:
                oss_path = "three-in-one/test/"
        except Exception:
            oss_path = "three-in-one/test/"

        if self.customer:
            oss_path += f"{self.customer}/"

        return oss_path + file_name

    def _update_download_info(self, public_link: str, intranet_link: str, package_md5: str):
        """更新下载信息到数据库"""
        try:
            if self.customer:
                sql = """UPDATE minor_version_number
                        SET public_download_link = %s, intranet_download_link = %s, package_md5sum = %s
                        WHERE minor_version = %s AND customer = %s"""
                params = (public_link, intranet_link, package_md5, self.latest_version, self.customer)
            else:
                sql = """UPDATE minor_version_number
                        SET public_download_link = %s, intranet_download_link = %s, package_md5sum = %s
                        WHERE minor_version = %s AND customer IS NULL"""
                params = (public_link, intranet_link, package_md5, self.latest_version)

            DatabaseManager.execute_query(sql, params)
            logger.info("下载信息更新成功")

        except Exception as e:
            logger.error(f"下载信息更新失败: {e}")
            raise

    def send_confirmation_message(self):
        """发送确认消息"""
        try:
            jenkins_parts = self.jenkins_url.split("/")
            build_number = jenkins_parts[-2] if len(jenkins_parts) >= 2 else "unknown"

            message_data = {
                "msg_type": "interactive",
                "card": MessageTemplates.CONFIRMATION_MESSAGE.format(
                    jenkins_url=self.jenkins_url,
                    branch=f"{self.branch}分支构建通知",
                    build_message=f"{self.branch}分支,第{build_number}次构建"
                )
            }

            success = FeishuNotifier.send_message(message_data)
            if not success:
                logger.warning("确认消息发送失败")

        except Exception as e:
            logger.error(f"发送确认消息失败: {e}")
            raise

    def send_end_message(self):
        """发送结束消息"""
        try:
            # 获取下载信息
            download_info = self._get_download_info()

            # 构建消息内容
            content = self._build_end_message_content(download_info)

            # 确定消息颜色和标题
            color, title = self._get_message_style()

            message_data = {
                "msg_type": "interactive",
                "card": MessageTemplates.END_MESSAGE.format(
                    branch=title,
                    content=content,
                    color=color
                )
            }

            # 保存消息到文件（用于调试）
            self._save_message_to_file(message_data)

            # 发送消息
            success = FeishuNotifier.send_message(message_data)
            if not success:
                logger.warning("结束消息发送失败")

        except Exception as e:
            logger.error(f"发送结束消息失败: {e}")
            raise

    def _get_download_info(self) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """获取下载信息"""
        try:
            if self.customer:
                sql = """SELECT public_download_link, intranet_download_link, package_md5sum
                        FROM minor_version_number
                        WHERE minor_version = %s AND customer = %s"""
                params = (self.latest_version, self.customer)
            else:
                sql = """SELECT public_download_link, intranet_download_link, package_md5sum
                        FROM minor_version_number
                        WHERE minor_version = %s"""
                params = (self.latest_version,)

            result = DatabaseManager.execute_query(sql, params, fetch_one=True)
            return result if result else (None, None, None)

        except Exception as e:
            logger.error(f"获取下载信息失败: {e}")
            return None, None, None

    def _build_end_message_content(self, download_info: Tuple) -> str:
        """构建结束消息内容"""
        public_link, intranet_link, package_md5 = download_info
        build_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        content = ""

        # 客户信息
        if self.customer:
            customer_name = Config.CUSTOMER_LIST.get(self.customer, self.customer)
            content += f"**😝 客户名称：**<font color='green'>{customer_name}</font>\\n"

        if self.build_status == "success":
            content += f"**😎 本次打包成功，版本为** <font color='green'>{self.latest_version}</font>\\n"
            content += f"**😎 本次打包完成时间** {build_time}\\n"
            content += f"😎 **WhaleScheduler Commit Id**: {self.scheduler_id}\\n"
            content += f"😎 WhaleScheduler UI Commit Id: {self.scheduler_ui_id}\\n"
            content += f"😎 WhaleTunnel Commit Id: {self.tunnel_id}\\n"
            content += f"😎 WhaleTunnel_Web Commit Id: {self.tunnel_web_id}\\n"
            content += f"😎 安装包MD5: {package_md5 or '暂无'}\\n"
            content += f"😎 内网下载链接: {intranet_link or '暂无'}\\n"
            content += f"😎 外网下载链接: {public_link or '暂无'}\\n"
            content += f"👉 **Jenkins 直达**： {self.jenkins_url}\\n"

            # 添加变更记录
            content += self._build_change_log()

        elif self.build_status == "error":
            content += f"**😎 本次打包失败，版本为** <font color='red'>{self.latest_version}</font>\\n"
            content += f"**😎 本次打包完成时间** {build_time}\\n"
            content += f"👉 **Jenkins 直达**： {self.jenkins_url}\\n"

        elif self.build_status == "timeout":
            content += f"**{self.branch} 分支打包,等待输入超时,请检查日志**\\n"
            content += f"👉 **Jenkins 直达**： {self.jenkins_url}"

        elif self.build_status == "cancel":
            content += f"**{self.branch} 分支打包取消**\\n"
            content += f"👉 **Jenkins 直达**： {self.jenkins_url}"

        else:
            content += f"**{self.branch} 分支打包失败,请检查日志**\\n"
            content += f"👉 **Jenkins 直达**： {self.jenkins_url}"

        return content

    def _build_change_log(self) -> str:
        """构建变更日志"""
        if not self.build_path:
            return ""

        content = "\\n---------- 版本变更记录 -----------"

        # 项目路径映射
        projects = {
            "whalescheduler": {
                "path": os.path.join(self.build_path, "whalescheduler"),
                "commit_id": self.scheduler_id,
                "name": "WhaleScheduler"
            },
            "whalescheduler-ui": {
                "path": os.path.join(self.build_path, "whalescheduler/whalescheduler-ui"),
                "commit_id": self.scheduler_ui_id,
                "name": "WhaleScheduler UI"
            },
            "whaletunnel": {
                "path": os.path.join(self.build_path, "whaletunnel"),
                "commit_id": self.tunnel_id,
                "name": "WhaleTunnel"
            },
            "whaletunnel-web": {
                "path": os.path.join(self.build_path, "whaletunnel-web"),
                "commit_id": self.tunnel_web_id,
                "name": "WhaleTunnel_Web"
            }
        }

        # 获取上一次的提交ID
        previous_commits = self._get_previous_commits()

        for project_key, project_info in projects.items():
            project_name = project_info["name"]
            project_path = project_info["path"]
            current_commit = project_info["commit_id"]

            previous_commit = previous_commits.get(project_key)

            if previous_commit and current_commit != previous_commit:
                # 有变更记录
                commit_messages = GitManager.get_commit_messages(project_path, previous_commit, current_commit)
                if commit_messages:
                    content += f"\\n**👉 {project_name} 基于上一次版本的变更记录:**"
                    for commit_id, message in commit_messages.items():
                        content += f"\\n {commit_id}:\\n {message}"
                else:
                    content += f"\\n**👉 {project_name} 无基于上一次版本的变更记录**"
            else:
                # 无变更记录，显示当前提交
                content += f"\\n**👉 {project_name} 无基于上一次版本的变更记录**"
                current_messages = GitManager.get_latest_commit_message(project_path)
                if current_messages:
                    content += f"\\n**👉 {project_name} 本次提交内容: **"
                    for message in current_messages:
                        content += f"\\n {message}"
                    content += f"\\n---------------------------"

        return content

    def _get_previous_commits(self) -> Dict[str, str]:
        """获取上一次的提交ID"""
        try:
            if self.customer:
                sql = """SELECT whalescheduler_commit, whalescheduler_ui_commit,
                               whaletunnel_commit, whaletunnel_web_commit
                        FROM minor_version_number
                        WHERE branch = %s AND customer = %s
                        ORDER BY id DESC LIMIT 2"""
                params = (self.branch, self.customer)
            else:
                sql = """SELECT whalescheduler_commit, whalescheduler_ui_commit,
                               whaletunnel_commit, whaletunnel_web_commit
                        FROM minor_version_number
                        WHERE branch = %s
                        ORDER BY id DESC LIMIT 2"""
                params = (self.branch,)

            results = DatabaseManager.execute_query(sql, params, fetch_all=True)

            if not results or len(results) < 2:
                return {}

            # 返回上一次的提交ID
            previous_commits = results[1]
            return {
                "whalescheduler": previous_commits[0],
                "whalescheduler-ui": previous_commits[1],
                "whaletunnel": previous_commits[2],
                "whaletunnel-web": previous_commits[3]
            }

        except Exception as e:
            logger.error(f"获取上一次提交ID失败: {e}")
            return {}

    def _get_message_style(self) -> Tuple[str, str]:
        """获取消息样式"""
        if self.build_status == "success":
            return "green", f"{self.branch} 分支打包完成"
        elif self.build_status == "error":
            return "red", f"{self.branch} 分支打包失败"
        elif self.build_status == "timeout":
            return "gray", f"{self.branch} 分支打包超时"
        elif self.build_status == "cancel":
            return "gray", f"{self.branch} 分支打包取消"
        else:
            return "red", f"{self.branch} 分支打包失败"

    def _save_message_to_file(self, message_data: dict):
        """保存消息到文件（用于调试）"""
        try:
            file_path = f"/data/{self.latest_version}.json"
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            with open(file_path, "w", encoding='utf-8') as f:
                json.dump(message_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.warning(f"保存消息文件失败: {e}")


def create_argument_parser() -> argparse.ArgumentParser:
    """创建参数解析器"""
    parser = argparse.ArgumentParser(
        description='WhaleStudio构建工具 - 优化版',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python whaleStudio_build_optimized.py --operation get_version --branch main
  python whaleStudio_build_optimized.py --operation upload_to_oss --file /path/to/package.tar.gz --latest_version 1.0.1-main
  python whaleStudio_build_optimized.py --operation send_end_message --build_status success --latest_version 1.0.1-main --jenkins_url http://jenkins.example.com/job/build/123/
        """
    )

    parser.add_argument('--operation', '-o', type=str, required=True,
                       choices=['get_version', 'update_commit_id', 'upload_to_oss',
                               'send_confirmation_message', 'send_end_message', 'delete_version'],
                       help='要执行的操作')

    parser.add_argument('--branch', '-b', type=str, help='分支名称')
    parser.add_argument('--customer', '-P', type=str, help='客户名称')
    parser.add_argument('--file', '-f', type=str, help='包文件路径')
    parser.add_argument('--jenkins_url', type=str, help='Jenkins URL')
    parser.add_argument('--build_status', type=str,
                       choices=['success', 'error', 'timeout', 'cancel'],
                       help='构建状态')
    parser.add_argument('--latest_version', type=str, help='最新版本号')
    parser.add_argument('--commit_id', type=str, help='提交ID（逗号分隔）')
    parser.add_argument('--build_path', type=str, help='构建路径')

    return parser


def main():
    """主函数"""
    # 设置日志
    LoggerSetup.setup_logger()

    try:
        # 解析参数
        parser = create_argument_parser()
        args = parser.parse_args()

        # 验证必要参数
        if not args.operation:
            parser.error("必须指定操作类型")

        # 创建构建实例
        builder = BuildWhaleStudioOptimized(
            operation=args.operation,
            branch=args.branch,
            customer=args.customer,
            file_path=args.file,
            jenkins_url=args.jenkins_url,
            build_status=args.build_status,
            latest_version=args.latest_version,
            commit_id=args.commit_id,
            build_path=args.build_path
        )

        # 执行操作
        result = builder.execute()

        logger.info(f"操作 {args.operation} 执行成功")
        return result

    except KeyboardInterrupt:
        logger.warning("用户中断执行")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == '__main__':
    main()
