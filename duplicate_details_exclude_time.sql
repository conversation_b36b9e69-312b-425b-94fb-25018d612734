-- 显示所有重复记录的详细信息（包含时间字段用于参考）
SELECT t1.*
FROM t_ds_task_definition t1
INNER JOIN (
    SELECT 
        code, name, version, description, project_code, user_id, task_type,
        task_execute_type, task_params, flag, task_priority, worker_group,
        environment_code, fail_retry_times, fail_retry_interval, timeout_flag,
        timeout_notify_strategy, timeout, delay_time, resource_ids, task_group_id,
        task_group_priority, warning_group_id
    FROM t_ds_task_definition
    GROUP BY 
        code, name, version, description, project_code, user_id, task_type,
        task_execute_type, task_params, flag, task_priority, worker_group,
        environment_code, fail_retry_times, fail_retry_interval, timeout_flag,
        timeout_notify_strategy, timeout, delay_time, resource_ids, task_group_id,
        task_group_priority, warning_group_id
    HAVING COUNT(*) > 1
) t2 ON (
    t1.code = t2.code AND IFNULL(t1.name,'') = IFNULL(t2.name,'') 
    AND t1.version = t2.version AND IFNULL(t1.description,'') = IFNULL(t2.description,'')
    AND t1.project_code = t2.project_code AND IFNULL(t1.user_id,0) = IFNULL(t2.user_id,0)
    AND t1.task_type = t2.task_type AND t1.task_execute_type = t2.task_execute_type
    AND IFNULL(t1.task_params,'') = IFNULL(t2.task_params,'')
    AND IFNULL(t1.flag,0) = IFNULL(t2.flag,0) AND t1.task_priority = t2.task_priority
    AND IFNULL(t1.worker_group,'') = IFNULL(t2.worker_group,'')
    AND t1.environment_code = t2.environment_code
    AND IFNULL(t1.fail_retry_times,0) = IFNULL(t2.fail_retry_times,0)
    AND IFNULL(t1.fail_retry_interval,0) = IFNULL(t2.fail_retry_interval,0)
    AND t1.timeout_flag = t2.timeout_flag
    AND IFNULL(t1.timeout_notify_strategy,0) = IFNULL(t2.timeout_notify_strategy,0)
    AND t1.timeout = t2.timeout AND t1.delay_time = t2.delay_time
    AND IFNULL(t1.resource_ids,'') = IFNULL(t2.resource_ids,'')
    AND IFNULL(t1.task_group_id,0) = IFNULL(t2.task_group_id,0)
    AND t1.task_group_priority = t2.task_group_priority
    AND IFNULL(t1.warning_group_id,0) = IFNULL(t2.warning_group_id,0)
)
ORDER BY t1.code, t1.id;