# 飞书API权限问题修复指南

## 问题描述

错误信息：
```
"code":131006,"msg":"permission denied: wiki space permission denied, tenant needs read permission."
```

这表示当前应用没有访问知识空间的权限。

## 解决方案

### 方案1：在飞书开放平台添加权限（推荐）

1. **登录飞书开放平台**
   - 访问：https://open.feishu.cn/
   - 使用管理员账号登录

2. **找到您的应用**
   - 应用ID：`cli_a80aebb677fcd013`
   - 进入应用管理页面

3. **添加权限范围**
   - 点击"权限管理"
   - 在"权限范围"中添加以下权限：
     ```
     wiki:wiki:readonly          # 知识库只读权限
     wiki:wiki                   # 知识库基础权限
     drive:drive:readonly        # 云文档只读权限（备用）
     ```

4. **重新发布应用**
   - 保存权限设置
   - 点击"版本管理与发布"
   - 创建新版本并发布

5. **等待审核通过**
   - 权限变更可能需要审核
   - 审核通过后权限才会生效

### 方案2：联系管理员授权

如果您不是应用管理员：

1. **联系飞书管理员**
   - 提供应用ID：`cli_a80aebb677fcd013`
   - 请求添加知识库访问权限

2. **提供知识空间ID**
   - 知识空间ID：`7530168808812937218`
   - 请求管理员为应用授权访问此空间

### 方案3：使用备用API（临时方案）

如果无法获得权限，可以考虑使用其他API：

1. **使用文档搜索API**
   - 通过文档标题搜索
   - 不需要知识空间权限

2. **使用云盘API**
   - 如果文档存储在云盘中
   - 需要相应的云盘权限

## 验证权限是否生效

运行测试脚本验证：

```bash
python3 test_feishu_api.py
```

如果看到以下输出表示权限已生效：
```
✅ 获取根节点成功，共 X 个节点
```

## 常见问题

### Q1: 权限添加后仍然报错
**A**: 权限变更需要时间生效，请等待5-10分钟后重试。

### Q2: 无法找到权限设置页面
**A**: 确保使用管理员账号登录，普通用户无法修改应用权限。

### Q3: 权限审核被拒绝
**A**: 联系飞书技术支持，说明业务需求和使用场景。

## 技术细节

### 权限说明

- `wiki:wiki:readonly`: 允许读取知识库内容
- `wiki:wiki`: 知识库基础操作权限
- `drive:drive:readonly`: 云文档只读权限

### API端点权限要求

| API端点 | 所需权限 |
|---------|----------|
| `/wiki/v2/spaces/{space_id}/nodes` | `wiki:wiki:readonly` |
| `/drive/v1/files` | `drive:drive:readonly` |
| `/docx/v1/documents` | `drive:drive:readonly` |

## 联系信息

如果问题仍未解决，请联系：
- 飞书技术支持
- 应用管理员
- 系统管理员
