global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'dolphinscheduler-prod'

scrape_configs:
  # DolphinScheduler API服务
  - job_name: 'dolphinscheduler-api'
    file_sd_configs:
      - files:
          - '/data/prometheus/targets/api_targets.json'
        refresh_interval: 30s
    metrics_path: '/dolphinscheduler/actuator/prometheus'
    scrape_interval: 30s
    scrape_timeout: 10s

  # DolphinScheduler Master服务
  - job_name: 'dolphinscheduler-master'
    file_sd_configs:
      - files:
          - '/data/prometheus/targets/master_targets.json'
        refresh_interval: 30s
    metrics_path: '/dolphinscheduler/master/actuator/prometheus'
    scrape_interval: 30s
    scrape_timeout: 10s

  # DolphinScheduler Worker服务
  - job_name: 'dolphinscheduler-worker'
    file_sd_configs:
      - files:
          - '/data/prometheus/targets/worker_targets.json'
        refresh_interval: 30s
    metrics_path: '/dolphinscheduler/worker/actuator/prometheus'
    scrape_interval: 30s
    scrape_timeout: 10s

  # DolphinScheduler Alert服务
  - job_name: 'dolphinscheduler-alert'
    file_sd_configs:
      - files:
          - '/data/prometheus/targets/alert_targets.json'
        refresh_interval: 30s
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s
    scrape_timeout: 10s