#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : 连接ZooKeeper获取主机并创建Prometheus客户端.py
# @Time    : 2025/08/01 15:19
# <AUTHOR> ch<PERSON>yi<PERSON><PERSON>
# @Version : 2.0
import argparse
import json
import os
import sys
import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from pathlib import Path
from contextlib import contextmanager

from kazoo.client import KazooClient
from kazoo.exceptions import KazooException, NoNodeError, ConnectionLoss

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))

# 配置日志
logging.basicConfig(
    level = logging.INFO,
    format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers = [
        logging.FileHandler(os.path.join(CURRENT_DIRECTORY, 'zk_prometheus.log'), encoding = 'utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class ZooKeeperConfig:
    """ZooKeeper配置类"""
    hosts: str = '**************:2181'
    timeout: int = 30
    auth_scheme: Optional[str] = None
    auth_credential: Optional[str] = None
    retry_times: int = 3
    retry_delay: int = 2

    @classmethod
    def from_env(cls) -> 'ZooKeeperConfig':
        """从环境变量创建配置"""
        return cls(
            hosts = os.getenv('ZK_HOSTS', '**************:2181'),
            timeout = int(os.getenv('ZK_TIMEOUT', '30')),
            auth_scheme = os.getenv('ZK_AUTH_SCHEME'),
            auth_credential = os.getenv('ZK_AUTH_CREDENTIAL'),
            retry_times = int(os.getenv('ZK_RETRY_TIMES', '3')),
            retry_delay = int(os.getenv('ZK_RETRY_DELAY', '2'))
        )


@dataclass
class ServiceConfig:
    """服务配置类"""

    service_zk_paths: Dict[str, str] = field(
        default_factory = lambda: {
            "api": f'/{os.getenv("WHALE_SCHEDULER_NAME", "whalescheduler")}/nodes/apiServer',
            "master": f'/{os.getenv("WHALE_SCHEDULER_NAME", "whalescheduler")}/nodes/master',
            "worker": f'/{os.getenv("WHALE_SCHEDULER_NAME", "whalescheduler")}/nodes/worker',
            "alert": f'/{os.getenv("WHALE_SCHEDULER_NAME", "whalescheduler")}/nodes/alertServer'
        }
    )

    service_metrics_ports: Dict[str, int] = field(
        default_factory = lambda: {
            "api": 12345,
            "master": 5679,
            "worker": 1235,
            "alert": 50053
        }
    )

    prometheus_config_path: str = os.getenv("PROMETHEUS_CONFIG_PATH", '/data/prometheus/targets')
    job_prefix: str = "dolphinscheduler"
    environment: str = "production"


class PrometheusZooKeeperClient:
    """
    通过ZooKeeper获取主机列表，并创建Prometheus监控节点的客户端类
    """

    def __init__(
            self, zk_config: Optional[ZooKeeperConfig] = None,
            service_config: Optional[ServiceConfig] = None
    ):
        """
        初始化客户端
        
        Args:
            zk_config: ZooKeeper配置
            service_config: 服务配置
        """
        self.zk_config = zk_config or ZooKeeperConfig.from_env()
        self.service_config = service_config or ServiceConfig()
        self.zk_client: Optional[KazooClient] = None

        # 确保Prometheus配置目录存在
        self._ensure_prometheus_dir()

        logger.info(f"初始化PrometheusZooKeeperClient，ZK地址: {self.zk_config.hosts}")

    def _ensure_prometheus_dir(self):
        """确保Prometheus配置目录存在"""
        config_path = Path(self.service_config.prometheus_config_path)

        if not config_path.exists():
            try:
                config_path.mkdir(parents = True, exist_ok = True)
                logger.info(f"创建Prometheus配置目录: {config_path}")
            except PermissionError:
                logger.error(f"权限不足，无法创建目录: {config_path}")
                raise
            except Exception as e:
                logger.error(f"创建Prometheus配置目录失败: {e}")
                raise
        else:
            logger.debug(f"Prometheus配置目录已存在: {config_path}")

    @contextmanager
    def _zk_connection(self):
        """ZooKeeper连接上下文管理器"""
        zk = None
        try:
            zk = KazooClient(
                hosts = self.zk_config.hosts,
                timeout = self.zk_config.timeout
            )

            # 启动连接
            zk.start(timeout = self.zk_config.timeout)
            logger.info(f"ZooKeeper连接成功: {self.zk_config.hosts}")

            # 添加认证（如果配置了）
            if self.zk_config.auth_scheme and self.zk_config.auth_credential:
                zk.add_auth(self.zk_config.auth_scheme, self.zk_config.auth_credential)
                logger.info(f"ZooKeeper认证成功: {self.zk_config.auth_scheme}")

            yield zk

        except Exception as e:
            logger.error(f"ZooKeeper连接失败: {e}")
            raise
        finally:
            if zk:
                try:
                    zk.stop()
                    zk.close()
                    logger.info("ZooKeeper连接已关闭")
                except Exception as e:
                    logger.warning(f"关闭ZooKeeper连接时出现警告: {e}")

    def _get_service_hosts_with_retry(self, zk: KazooClient, service: str, zk_path: str) -> List[str]:
        """
        带重试机制获取服务主机列表
        
        Args:
            zk: ZooKeeper客户端
            service: 服务名称
            zk_path: ZooKeeper路径
            
        Returns:
            主机列表
        """
        for attempt in range(self.zk_config.retry_times):
            try:
                if service == "worker":
                    # Worker服务需要遍历命名空间
                    hosts = []
                    try:
                        namespaces = zk.get_children(zk_path)
                        logger.info(f"发现worker命名空间: {namespaces}")

                        for namespace in namespaces:
                            namespace_path = f"{zk_path}/{namespace}"
                            try:
                                zk_hosts = zk.get_children(namespace_path)
                                namespace_hosts = [
                                    f"{host.split(':')[0]}:{self.service_config.service_metrics_ports[service]}"
                                    for host in zk_hosts
                                ]
                                hosts.extend(namespace_hosts)
                                logger.debug(f"命名空间 {namespace} 的主机: {namespace_hosts}")
                            except NoNodeError:
                                logger.warning(f"命名空间节点不存在: {namespace_path}")
                                continue

                    except NoNodeError:
                        logger.warning(f"Worker根节点不存在: {zk_path}")
                        return []

                    # 去重
                    hosts = list(set(hosts))
                else:
                    # 其他服务直接获取子节点
                    try:
                        zk_hosts = zk.get_children(zk_path)
                        hosts = [
                            f"{host.split(':')[0]}:{self.service_config.service_metrics_ports[service]}"
                            for host in zk_hosts
                        ]
                        # 去重
                        hosts = list(set(hosts))
                    except NoNodeError:
                        logger.warning(f"服务节点不存在: {zk_path}")
                        return []

                logger.info(f"服务 {service} 获取到 {len(hosts)} 个主机")
                return hosts

            except (ConnectionLoss, KazooException) as e:
                logger.warning(
                    f"获取服务 {service} 主机列表失败 (尝试 {attempt + 1}/{self.zk_config.retry_times}): {e}"
                )
                if attempt < self.zk_config.retry_times - 1:
                    time.sleep(self.zk_config.retry_delay)
                else:
                    logger.error(f"获取服务 {service} 主机列表最终失败")
                    raise
            except Exception as e:
                logger.error(f"获取服务 {service} 主机列表时发生未知错误: {e}")
                raise

        return []

    def get_zookeeper_hosts(self) -> Dict[str, List[str]]:
        """
        获取ZooKeeper主机列表
        
        Returns:
            服务主机字典，包含 API, Master, Worker, Alert 服务的主机列表
        """
        host_list = {service: [] for service in self.service_config.service_zk_paths.keys()}

        try:
            with self._zk_connection() as zk:
                for service, zk_path in self.service_config.service_zk_paths.items():
                    try:
                        hosts = self._get_service_hosts_with_retry(zk, service, zk_path)
                        host_list[service] = hosts
                        logger.info(f"服务 {service}: {len(hosts)} 个主机")

                    except Exception as e:
                        logger.error(f"获取服务 {service} 主机失败: {e}")
                        # 继续处理其他服务
                        continue

        except Exception as e:
            logger.error(f"获取ZooKeeper主机列表失败: {e}")
            raise

        # 记录总结果
        total_hosts = sum(len(hosts) for hosts in host_list.values())
        logger.info(f"总共获取到 {total_hosts} 个主机，分布在 {len(host_list)} 个服务中")

        return host_list

    def create_prometheus_targets(self, host_list: Dict[str, List[str]]) -> Dict[str, bool]:
        """
        创建Prometheus监控目标配置文件
        
        Args:
            host_list: 服务主机字典
            
        Returns:
            创建结果字典，键为服务名，值为是否成功
        """
        results = {}

        for service, hosts in host_list.items():
            if not hosts:
                logger.warning(f"服务 {service} 没有可用主机，跳过创建配置文件")
                results[service] = False
                continue

            target_file = os.path.join(
                self.service_config.prometheus_config_path,
                f"{service}_targets.json"
            )

            target_config = [{
                "targets": hosts,
                "labels": {
                    "job": f"{self.service_config.job_prefix}-{service}",
                    "service": service,
                    "env": self.service_config.environment
                }
            }]

            try:
                with open(target_file, "w", encoding = 'utf-8') as f:
                    json.dump(target_config, f, indent = 2, ensure_ascii = False)

                logger.info(f"成功创建 {service} 配置文件: {target_file} ({len(hosts)} 个目标)")
                results[service] = True

            except Exception as e:
                logger.error(f"创建 {service} 配置文件失败: {e}")
                results[service] = False

        return results

    def sync_prometheus_targets(self) -> Dict[str, Any]:
        """
        同步Prometheus监控目标
        
        Returns:
            同步结果摘要
        """
        logger.info("开始同步Prometheus监控目标")
        start_time = time.time()

        try:
            # 获取主机列表
            host_list = self.get_zookeeper_hosts()

            # 创建配置文件
            create_results = self.create_prometheus_targets(host_list)

            # 统计结果
            total_services = len(create_results)
            successful_services = sum(1 for success in create_results.values() if success)
            total_hosts = sum(len(hosts) for hosts in host_list.values())

            elapsed_time = time.time() - start_time

            summary = {
                "success": successful_services == total_services,
                "total_services": total_services,
                "successful_services": successful_services,
                "failed_services": total_services - successful_services,
                "total_hosts": total_hosts,
                "host_list": host_list,
                "create_results": create_results,
                "elapsed_time": round(elapsed_time, 2)
            }

            if summary["success"]:
                logger.info(f"同步完成！处理了 {total_services} 个服务，{total_hosts} 个主机，耗时 {elapsed_time:.2f}s")
            else:
                logger.warning(f"同步部分成功：{successful_services}/{total_services} 个服务成功")

            return summary

        except Exception as e:
            logger.error(f"同步Prometheus监控目标失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "elapsed_time": round(time.time() - start_time, 2)
            }


def main():
    """主函数"""

    # 设置一个命令行参数,用于指定配置文件路径，zk地址，zk namespace等
    parser = argparse.ArgumentParser(description = "Prometheus ZooKeeper Client")
    parser.add_argument(
        "--config", "-c", type = str, default = "/data/prometheus/targets",
        help = "配置文件路径"
    )
    parser.add_argument(
        "--zk-hosts", "-z", type = str, default = "**************:2181",
        help = "ZooKeeper地址"
    )
    parser.add_argument(
        "--zk-namespace", "-n", type = str, default = "whalescheduler",
        help = "ZooKeeper命名空间"
    )
    args = parser.parse_args()

    # 设置配置文件路径
    os.environ['PROMETHEUS_CONFIG_PATH'] = args.config

    # 设置ZooKeeper地址
    os.environ['ZK_HOSTS'] = args.zk_hosts

    # 设置ZooKeeper命名空间
    os.environ['ZK_NAMESPACE'] = args.zk_namespace

    try:
        # 创建客户端
        client = PrometheusZooKeeperClient()

        # 同步监控目标
        result = client.sync_prometheus_targets()

        # 输出结果
        if result["success"]:
            print(f"✅ 同步成功！")
            print(f"📊 处理服务: {result['successful_services']}/{result['total_services']}")
            print(f"🖥️  总主机数: {result['total_hosts']}")
            print(f"⏱️  耗时: {result['elapsed_time']}s")
        else:
            print(f"❌ 同步失败: {result.get('error', '未知错误')}")
            if 'successful_services' in result:
                print(f"📊 部分成功: {result['successful_services']}/{result['total_services']}")

        return 0 if result["success"] else 1

    except KeyboardInterrupt:
        logger.info("用户中断操作")
        print("\n⚠️ 操作被用户中断")
        return 130
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"❌ 程序执行失败: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
