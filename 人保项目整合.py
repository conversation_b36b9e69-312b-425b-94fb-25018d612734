#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : 人保项目整合.py
# @Time    : 2025/07/23 15:57
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys

import xlwt

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))

# 创建一个新的Excel文件 | Create a new Excel file
workbook = xlwt.Workbook()
# 创建一个新的sheet | Create a new sheet
sheet = workbook.add_sheet('Sheet1')

# 写入数据 | Write data
sheet.write(0, 0, '序号')
sheet.write(0, 1, '项目名称')
sheet.write(0, 2, '描述')

with open(
        os.path.join(
            CURRENT_DIRECTORY,
            "renbao"
        ), "r"
) as f:
    index = 1
    for line in f:
        line = line.split(" ")
        line = [i for i in line if i]
        if len(line) < 3:
            print(f"Invalid line: {line}")
            continue
        sheet.write(index + 1, 0, line[0])
        sheet.write(index + 1, 1, line[1])
        sheet.write(index + 1, 2, " ".join(line[2:]).strip())
        index += 1

workbook.save('renbao.xls')
