apiVersion: apps/v1
kind: StatefulSet
metadata:
  namespace: whalestudio-a-cluster
  name: whalestudio-worker-test
  labels:
    app: whalestudio-worker-statefulset
spec:
  selector:
    matchLabels:
      app: whalestudio-worker
  serviceName: whalestudio-worker-service
  replicas: 1  # 副本数
  template:
    metadata:
      labels:
        app: whalestudio-worker
    spec:
      containers:
        - name: whalestudio-worker
          image: 10.21.42.159/bdmp/whalestudio-release-cluster-b:v2.6.6
          imagePullPolicy: Always
          ports:
            - containerPort: 1234
              name: http
          env:
            - name: service_type
              value: "worker"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: KUBERNETES_SERVICE_HOST
              value: "$(POD_NAME).whalestudio-worker-service.$(POD_NAMESPACE).svc.cluster.local"
            - name: DOCKER
              value: "true"
            - name: WORKFLOW_LOG_DIR
              value: "/data/whalestudio/logs/${POD_NAME}"
            - name: TASK_LOG_DIR
              value: "/data/whalestudio/logs/${POD_NAME}"
            - name: SERVER_LOG_DIR
              value: "/data/whalestudio/logs/${POD_NAME}"
            - name: RENSHOU_DATAX
              value: "true"
            - name: RENSHOU_DATAX_DES
              value: "true"
            - name: SERVICE_TYPE
              value: api-server

---
apiVersion: v1
kind: Service
metadata:
  name: whalestudio-worker-service
  namespace: whalestudio
  labels:
    app: whalestudio-worker-service
spec:
  clusterIP: None
  selector:
    app: whalestudio-worker
  ports:
    - protocol: TCP
      port: 5678
      targetPort: 5678