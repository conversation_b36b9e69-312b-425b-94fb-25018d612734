#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试飞书消息发送功能
"""

import requests
import json


def test_message_send():
    """测试消息发送 - 直接对应您的 curl 命令"""
    
    # API 配置
    url = "https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=chat_id"
    token = "t-g1047nh2WCRXAAFSUBTVNA4NJOTZRIDZ2HKQQW4C"
    receive_id = "oc_f5a3b6e372cfeae27f91638e142d7dbe"
    
    # 请求头
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    # 消息内容
    message_text = "<at user_id=\"all\"></at>当前是2025年7月第4周的周四请大家及时填写线上问题回顾"
    
    # 请求体 - 完全对应您的 curl 命令
    data = {
        "content": "{\"text\":\"" + message_text.replace('"', '\\"') + "\"}",
        "msg_type": "text",
        "receive_id": receive_id
    }
    
    print("发送请求...")
    print(f"URL: {url}")
    print(f"Headers: {json.dumps(headers, indent=2)}")
    print(f"Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
    print("-" * 50)
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应体: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("\n✅ 消息发送成功!")
            print(f"消息ID: {result.get('data', {}).get('message_id', 'N/A')}")
            return True
        else:
            print(f"\n❌ 消息发送失败，状态码: {response.status_code}")
            try:
                error_info = response.json()
                print(f"错误信息: {error_info}")
            except:
                print(f"错误响应: {response.text}")
            return False
            
    except requests.RequestException as e:
        print(f"\n❌ 网络请求失败: {e}")
        return False
    except Exception as e:
        print(f"\n❌ 发送失败: {e}")
        return False


def test_different_formats():
    """测试不同的消息格式"""
    
    # 基本配置
    url = "https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=chat_id"
    token = "t-g1047nh2WCRXAAFSUBTVNA4NJOTZRIDZ2HKQQW4C"
    receive_id = "oc_f5a3b6e372cfeae27f91638e142d7dbe"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    # 测试不同格式
    test_cases = [
        {
            "name": "原始格式（转义）",
            "content": "{\"text\":\"<at user_id=\\\"all\\\"></at>测试消息1\"}"
        },
        {
            "name": "JSON对象格式",
            "content": json.dumps({"text": "<at user_id=\"all\"></at>测试消息2"})
        },
        {
            "name": "简单文本",
            "content": json.dumps({"text": "简单测试消息3"})
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n=== 测试 {i}: {test_case['name']} ===")
        
        data = {
            "content": test_case["content"],
            "msg_type": "text",
            "receive_id": receive_id
        }
        
        print(f"Content: {test_case['content']}")
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 发送成功")
            else:
                print(f"❌ 发送失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")


if __name__ == "__main__":
    print("=== 飞书消息发送测试 ===\n")
    
    # 测试原始 curl 命令对应的格式
    print("1. 测试原始 curl 命令格式:")
    test_message_send()
    
    # 测试不同格式
    print("\n" + "="*60)
    print("2. 测试不同消息格式:")
    test_different_formats()
