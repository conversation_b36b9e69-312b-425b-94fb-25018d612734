#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
节假日检查模块 - 支持多种方案
"""

import datetime
from typing import Optional, List


class HolidayChecker:
    """节假日检查器 - 支持多种实现方案"""
    
    def __init__(self, method: str = "auto"):
        """
        初始化节假日检查器
        
        Args:
            method: 检查方法 ("workalendar", "holidays", "manual", "auto")
        """
        self.method = method
        self.checker = None
        self._init_checker()
    
    def _init_checker(self):
        """初始化具体的检查器"""
        if self.method == "auto":
            # 自动选择可用的方法
            for method in ["workalendar", "holidays", "manual"]:
                try:
                    self.method = method
                    self._init_specific_checker(method)
                    print(f"✅ 使用 {method} 方法进行节假日检查")
                    return
                except ImportError:
                    continue
            
            # 如果都不可用，使用手动方法
            self.method = "manual"
            self._init_specific_checker("manual")
            print("⚠️ 使用手动方法进行节假日检查（功能有限）")
        else:
            self._init_specific_checker(self.method)
    
    def _init_specific_checker(self, method: str):
        """初始化特定的检查器"""
        if method == "workalendar":
            from workalendar.asia import China
            self.checker = China()
        elif method == "holidays":
            import holidays
            self.checker = holidays.China()
        elif method == "manual":
            self.checker = ManualHolidayChecker()
        else:
            raise ValueError(f"不支持的方法: {method}")
    
    def is_holiday(self, date: datetime.date) -> bool:
        """
        检查指定日期是否为节假日
        
        Args:
            date: 要检查的日期
            
        Returns:
            bool: True表示是节假日，False表示不是
        """
        try:
            if self.method == "workalendar":
                return not self.checker.is_working_day(date)
            elif self.method == "holidays":
                return date in self.checker
            elif self.method == "manual":
                return self.checker.is_holiday(date)
            else:
                return False
        except Exception as e:
            print(f"⚠️ 节假日检查失败: {e}，默认为工作日")
            return False
    
    def get_method_info(self) -> str:
        """获取当前使用的方法信息"""
        method_info = {
            "workalendar": "workalendar库 - 支持完整的中国节假日",
            "holidays": "holidays库 - 支持基本节假日",
            "manual": "手动方法 - 仅支持固定节假日（功能有限）"
        }
        return method_info.get(self.method, "未知方法")


class ManualHolidayChecker:
    """手动节假日检查器 - 作为备选方案"""
    
    def __init__(self):
        # 2024-2025年的主要节假日（需要手动维护）
        self.holidays_2024 = [
            # 元旦
            (1, 1),
            # 春节
            (2, 10), (2, 11), (2, 12), (2, 13), (2, 14), (2, 15), (2, 16), (2, 17),
            # 清明节
            (4, 4), (4, 5), (4, 6),
            # 劳动节
            (5, 1), (5, 2), (5, 3), (5, 4), (5, 5),
            # 端午节
            (6, 10),
            # 中秋节
            (9, 15), (9, 16), (9, 17),
            # 国庆节
            (10, 1), (10, 2), (10, 3), (10, 4), (10, 5), (10, 6), (10, 7)
        ]
        
        self.holidays_2025 = [
            # 元旦
            (1, 1),
            # 春节（预估）
            (1, 28), (1, 29), (1, 30), (1, 31), (2, 1), (2, 2), (2, 3),
            # 清明节（预估）
            (4, 5), (4, 6), (4, 7),
            # 劳动节
            (5, 1), (5, 2), (5, 3), (5, 4), (5, 5),
            # 端午节（预估）
            (5, 31),
            # 中秋节（预估）
            (10, 6),
            # 国庆节
            (10, 1), (10, 2), (10, 3), (10, 4), (10, 5), (10, 6), (10, 7)
        ]
    
    def is_holiday(self, date: datetime.date) -> bool:
        """检查是否为节假日"""
        month_day = (date.month, date.day)
        
        if date.year == 2024:
            return month_day in self.holidays_2024
        elif date.year == 2025:
            return month_day in self.holidays_2025
        else:
            # 对于其他年份，只检查固定节假日
            fixed_holidays = [(1, 1), (5, 1), (10, 1)]  # 元旦、劳动节、国庆节
            return month_day in fixed_holidays


def test_holiday_checker():
    """测试节假日检查器"""
    checker = HolidayChecker()
    
    print(f"当前使用方法: {checker.get_method_info()}")
    print()
    
    # 测试一些日期
    test_dates = [
        datetime.date(2024, 1, 1),   # 元旦
        datetime.date(2024, 2, 12),  # 春节
        datetime.date(2024, 5, 1),   # 劳动节
        datetime.date(2024, 10, 1),  # 国庆节
        datetime.date(2024, 12, 25), # 圣诞节（不是中国节假日）
        datetime.date.today(),       # 今天
    ]
    
    for test_date in test_dates:
        is_holiday = checker.is_holiday(test_date)
        status = "节假日" if is_holiday else "工作日"
        print(f"{test_date.strftime('%Y年%m月%d日')}: {status}")


if __name__ == "__main__":
    test_holiday_checker()
