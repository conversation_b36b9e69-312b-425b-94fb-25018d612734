#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书API测试脚本
用于调试和测试飞书API调用
"""

import requests
import json
from typing import Dict, Any


class FeishuAPITester:
    """飞书API测试类"""
    
    def __init__(self):
        # 配置信息
        self.app_id = "cli_a80aebb677fcd013"
        self.app_secret = "n2KbftnWf5kiCAKJXklfLhBZuy01l4b5"
        self.wiki_space_id = "7530168808812937218"
        
        # API URLs
        self.token_url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
        self.wiki_nodes_url = f"https://open.feishu.cn/open-apis/wiki/v2/spaces/{self.wiki_space_id}/nodes"
        
        self.access_token = None
    
    def get_access_token(self) -> str:
        """获取访问令牌"""
        try:
            print("🔑 获取访问令牌...")
            
            response = requests.post(
                url=self.token_url,
                headers={"Content-Type": "application/json"},
                json={
                    "app_id": self.app_id,
                    "app_secret": self.app_secret
                },
                timeout=30
            )
            
            print(f"Token API 状态码: {response.status_code}")
            print(f"Token API 响应: {response.text}")
            
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") == 0:
                self.access_token = result.get("tenant_access_token")
                print(f"✅ 访问令牌获取成功: {self.access_token[:20]}...")
                return self.access_token
            else:
                print(f"❌ 获取访问令牌失败: {result.get('msg')}")
                return None
                
        except Exception as e:
            print(f"❌ 获取访问令牌异常: {e}")
            return None
    
    def test_get_root_nodes(self):
        """测试获取根节点"""
        if not self.access_token:
            print("❌ 没有访问令牌，无法测试")
            return
        
        try:
            print("\n📁 测试获取根节点...")
            
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(
                url=self.wiki_nodes_url,
                headers=headers,
                timeout=30
            )
            
            print(f"根节点API URL: {response.url}")
            print(f"根节点API 状态码: {response.status_code}")
            print(f"根节点API 响应头: {dict(response.headers)}")
            print(f"根节点API 响应: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    items = result.get("data", {}).get("items", [])
                    print(f"✅ 获取根节点成功，共 {len(items)} 个节点")
                    
                    # 显示前几个节点的信息
                    for i, item in enumerate(items[:3]):
                        print(f"  节点 {i+1}: {item.get('title', 'N/A')} (token: {item.get('node_token', 'N/A')})")
                    
                    return items
                else:
                    print(f"❌ API返回错误: code={result.get('code')}, msg={result.get('msg')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 测试根节点异常: {e}")
        
        return None
    
    def test_get_child_nodes(self, parent_node_token: str):
        """测试获取子节点"""
        if not self.access_token:
            print("❌ 没有访问令牌，无法测试")
            return
        
        try:
            print(f"\n📂 测试获取子节点 (父节点: {parent_node_token})...")
            
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            params = {
                "parent_node_token": parent_node_token
            }
            
            response = requests.get(
                url=self.wiki_nodes_url,
                params=params,
                headers=headers,
                timeout=30
            )
            
            print(f"子节点API URL: {response.url}")
            print(f"子节点API 状态码: {response.status_code}")
            print(f"子节点API 响应头: {dict(response.headers)}")
            print(f"子节点API 响应: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    items = result.get("data", {}).get("items", [])
                    print(f"✅ 获取子节点成功，共 {len(items)} 个节点")
                    
                    # 显示子节点信息
                    for i, item in enumerate(items):
                        print(f"  子节点 {i+1}: {item.get('title', 'N/A')} (token: {item.get('node_token', 'N/A')})")
                    
                    return items
                else:
                    print(f"❌ API返回错误: code={result.get('code')}, msg={result.get('msg')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 测试子节点异常: {e}")
        
        return None
    
    def test_find_document(self, title: str):
        """测试查找文档"""
        print(f"\n🔍 查找文档: {title}")
        
        # 先获取根节点
        root_nodes = self.test_get_root_nodes()
        if not root_nodes:
            return None
        
        # 在根节点中查找
        for node in root_nodes:
            if node.get("title") == title:
                print(f"✅ 在根节点中找到文档: {title}")
                return node
        
        print(f"❌ 在根节点中未找到文档: {title}")
        return None
    
    def run_full_test(self):
        """运行完整测试"""
        print("🚀 开始飞书API测试")
        print("=" * 50)
        
        # 1. 获取访问令牌
        if not self.get_access_token():
            print("❌ 无法获取访问令牌，测试终止")
            return
        
        # 2. 测试获取根节点
        root_nodes = self.test_get_root_nodes()
        if not root_nodes:
            print("❌ 无法获取根节点，测试终止")
            return
        
        # 3. 测试查找特定文档
        test_titles = [
            "2025年7月-线上问题回顾",
            "2024年12月-线上问题回顾"
        ]
        
        for title in test_titles:
            found_doc = self.test_find_document(title)
            if found_doc:
                # 4. 测试获取该文档的子节点
                node_token = found_doc.get("node_token")
                if node_token:
                    self.test_get_child_nodes(node_token)
        
        print("\n" + "=" * 50)
        print("🏁 测试完成")


def main():
    """主函数"""
    tester = FeishuAPITester()
    tester.run_full_test()


if __name__ == "__main__":
    main()
