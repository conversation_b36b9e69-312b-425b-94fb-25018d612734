#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : 获取工作流实例.py
# @Time    : 2025/08/01 14:35
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0
import json
import os
import sys

import requests

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))

URL = "http://ws3:12345"

TOKEN = "da5becf1a167835681d2922615e8c2e8"


# 1. 启动工作流
def start_workflow():
    """
    Start a workflow. | 启动工作流
    Returns:
    """
    headers = {
        "TOKEN": TOKEN,
    }
    data = {
        "processDefinitionCode": 147699075263648,
        "failureStrategy": "CONTINUE",
        "warningType": "NONE",
        "execType": "START_PROCESS",
        "taskDependType": "TASK_POST",
        "dependentMode": "OFF_MODE",
        "runMode": "RUN_MODE_SERIAL",
        "processInstancePriority": "MEDIUM",
        "workerGroup": "default",
        "expectedParallelismNumber": 10,
        "scheduleTime": '{"complementStartDate":"2025-08-01 00:00:00","complementEndDate":"2025-08-01 23:59:59","complementCalendarType":"SCHEDULE","complementCalendarCode":""}',
        "projectCode": 147196759664800,
        "complementCalendarType": "SCHEDULE",
        "operationId": 20250801
    }
    print(
        requests.post(
            url = f"{URL}/dolphinscheduler/projects/executors/start-process-instance",
            headers = headers,
            params = data
        ).json()
    )
    # result = {'code': 0, 'msg': 'success', 'exceptionStack': None, 'data': 148070506445664, 'failed': False,
    #           'success': True}


def get_workflow_instance():
    """
    Get the workflow instance. | 获取工作流实例
    Returns:
    """
    headers = {
        "TOKEN": TOKEN,
    }
    data = {
        "operationId": 20250801,
        "processDefinitionCode": 147699075263648,
        "searchVal": "mail",
        "pageNo": 1,
        "pageSize": 1
    }
    print(
        json.dumps(
            requests.get(
                url = f"{URL}/dolphinscheduler/ws/process-instances",
                headers = headers,
                params = data
            ).json(), indent = 4
        )
    )


if __name__ == '__main__':
    # start_workflow()
    get_workflow_instance()
