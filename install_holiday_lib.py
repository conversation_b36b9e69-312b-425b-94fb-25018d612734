#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
节假日库安装脚本
自动检测并安装适合的节假日库
"""

import subprocess
import sys
import importlib


def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)


def check_library(lib_name, import_path=None):
    """检查库是否已安装"""
    try:
        if import_path:
            importlib.import_module(import_path)
        else:
            importlib.import_module(lib_name)
        return True
    except ImportError:
        return False


def install_library(lib_name):
    """安装库"""
    print(f"正在安装 {lib_name}...")
    success, stdout, stderr = run_command(f"{sys.executable} -m pip install {lib_name}")
    
    if success:
        print(f"✅ {lib_name} 安装成功")
        return True
    else:
        print(f"❌ {lib_name} 安装失败")
        print(f"错误信息: {stderr}")
        return False


def main():
    """主函数"""
    print("=== 节假日库安装检查 ===\n")
    
    # 检查 Python 版本
    python_version = sys.version_info
    print(f"Python 版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 要尝试的库列表（按优先级排序）
    libraries = [
        {
            "name": "workalendar",
            "import_path": "workalendar.asia",
            "description": "功能最全面的日历库，支持全球节假日",
            "test_code": "from workalendar.asia import China; print('workalendar 可用')"
        },
        {
            "name": "holidays",
            "import_path": "holidays",
            "description": "轻量级节假日库，支持多国节假日",
            "test_code": "import holidays; print('holidays 可用')"
        }
    ]
    
    installed_lib = None
    
    # 检查已安装的库
    print("\n1. 检查已安装的库:")
    for lib in libraries:
        if check_library(lib["name"], lib["import_path"]):
            print(f"✅ {lib['name']} 已安装")
            if not installed_lib:
                installed_lib = lib
        else:
            print(f"❌ {lib['name']} 未安装")
    
    # 如果没有安装任何库，尝试安装
    if not installed_lib:
        print("\n2. 尝试安装节假日库:")
        
        for lib in libraries:
            print(f"\n尝试安装 {lib['name']} ({lib['description']})")
            
            if install_library(lib["name"]):
                # 验证安装
                if check_library(lib["name"], lib["import_path"]):
                    print(f"✅ {lib['name']} 安装并验证成功")
                    installed_lib = lib
                    break
                else:
                    print(f"⚠️ {lib['name']} 安装成功但验证失败")
            else:
                print(f"❌ {lib['name']} 安装失败，尝试下一个...")
    
    # 测试最终结果
    print("\n3. 最终测试:")
    if installed_lib:
        print(f"✅ 将使用 {installed_lib['name']} 进行节假日判断")
        try:
            exec(installed_lib["test_code"])
            
            # 进行实际测试
            print("\n4. 功能测试:")
            test_holiday_functionality(installed_lib["name"])
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    else:
        print("❌ 没有可用的节假日库")
        print("建议手动安装:")
        print("  pip install workalendar")
        print("  或")
        print("  pip install holidays")


def test_holiday_functionality(lib_name):
    """测试节假日功能"""
    import datetime
    
    try:
        if lib_name == "workalendar":
            from workalendar.asia import China
            cal = China()
            
            # 测试几个日期
            test_dates = [
                datetime.date(2024, 1, 1),   # 元旦
                datetime.date(2024, 5, 1),   # 劳动节
                datetime.date(2024, 10, 1),  # 国庆节
                datetime.date.today()        # 今天
            ]
            
            print("使用 workalendar 测试:")
            for test_date in test_dates:
                is_working = cal.is_working_day(test_date)
                status = "工作日" if is_working else "节假日"
                print(f"  {test_date}: {status}")
                
        elif lib_name == "holidays":
            import holidays
            china_holidays = holidays.China()
            
            test_dates = [
                datetime.date(2024, 1, 1),   # 元旦
                datetime.date(2024, 5, 1),   # 劳动节
                datetime.date(2024, 10, 1),  # 国庆节
                datetime.date.today()        # 今天
            ]
            
            print("使用 holidays 测试:")
            for test_date in test_dates:
                is_holiday = test_date in china_holidays
                status = "节假日" if is_holiday else "工作日"
                print(f"  {test_date}: {status}")
                
    except Exception as e:
        print(f"功能测试失败: {e}")


if __name__ == "__main__":
    main()
