#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
每周四提醒脚本
功能：检查今天是否为周四，如果是则显示提醒信息和本月周数
"""

import datetime
import sys
import traceback
from functools import lru_cache

import requests
from rich.console import Console

console = Console()


def info_handler(message):
    console.print(f"[bold green]{message}[/bold green]")


def warning_handler(message):
    console.print(f"[bold yellow]{message}[/bold yellow]")


def error_handler(message):
    console.print(f"[bold red]{message}[/bold red]")


def get_week_of_month(date):
    """
    计算指定日期是本月的第几周
    Args:
        date: datetime对象
    Returns:
        int: 本月第几周
    """
    # 获取本月第一天
    first_day = date.replace(day = 1)
    # 计算第一天是周几（0=周一，6=周日）
    first_weekday = first_day.weekday()
    # 计算当前日期是本月第几周
    week_number = (date.day + first_weekday - 1) // 7 + 1
    return week_number


class FeishuTools:
    @classmethod
    @lru_cache(maxsize = 1)
    def get_tenant_access_token(cls):
        """
        Get headers for Feishu API.
        Returns:
        """
        try:
            result = requests.post(
                headers = {
                    "Content-Type": "application/json"
                },
                json = {
                    "app_id": "cli_a80aebb677fcd013",
                    "app_secret": "n2KbftnWf5kiCAKJXklfLhBZuy01l4b5"
                },
                url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
            ).json()
            if result.get("code") == 0:
                info_handler("获取飞书租户 access_token 成功")
                tenant_access_token = result.get("tenant_access_token", None)
                if not tenant_access_token:
                    error_handler("获取飞书租户 access_token 失败，未获取到 access_token")
                    sys.exit(1)
                return tenant_access_token
            else:
                error_handler("获取飞书租户 access_token 失败")
                error_handler(f"错误信息：{result.get('msg')}")
                error_handler(message = traceback.format_exc())
                sys.exit(1)
        except Exception as e:
            error_handler(f"获取飞书租户 access_token 失败，错误信息：{e}")
            sys.exit(1)

    @classmethod
    def obtain_a_list_of_knowledge_bases(cls):
        """
        获取知识库列表
        Args:

        Returns:
            list: 知识库列表
        """
        try:
            result = requests.get(
                headers = {
                    "Authorization": f"Bearer {cls.get_tenant_access_token()}"
                },
                url = "https://open.feishu.cn/open-apis/wiki/v2/spaces/7530168808812937218/nodes"
            ).json()
            # print(f"知识库列表: {result.get('data', {}).get('items', [])}")
            if result.get("code") == 0:
                info_handler("获取知识库列表成功")
                return result.get("data", {}).get("items", [])
            else:
                error_handler("获取知识库列表失败")
                error_handler(f"错误信息：{result.get('msg')}")
                error_handler(message = traceback.format_exc())
                sys.exit(1)
        except Exception as e:
            error_handler(f"获取知识库列表失败，错误信息：{e}")
            sys.exit(1)

    @classmethod
    def create_month_doc(cls, month_name):
        """
        月度文档创建
        Args:
            month_name: 月份名称

        Returns:
            bool: 创建成功返回 True，失败返回 False
        """
        try:
            result = requests.post(
                headers = {
                    "Authorization": f"Bearer {cls.get_tenant_access_token()}",
                    "Content-Type": "application/json"
                },
                url = "https://open.feishu.cn/open-apis/docx/v1/documents",
                json = {
                    "title": f"{month_name}",
                }
            ).json()
            if result.get("code") == 0:
                info_handler(f"创建文档 {month_name} 成功")
                return True, result.get("data", {}).get("document", {}).get("document_id")
            else:
                error_handler(f"创建文档 {month_name} 失败")
                error_handler(f"错误信息：{result.get('msg')}")
                error_handler(message = traceback.format_exc())
                return False, None
        except Exception as e:
            error_handler(f"创建文档 {month_name} 失败，错误信息：{e}")
            return False, None

    @classmethod
    def copy_template_to_sheet(cls, table_name):
        """
        复制模板到指定文档
        Args:
            document_id: 文档Id
            table_name: 表格名称
        Returns:
            bool: 复制成功返回 True，失败返回 False
        """
        try:
            result = requests.post(
                headers = {
                    "Authorization": f"Bearer {cls.get_tenant_access_token()}",
                    "Content-Type": "application/json"
                },
                url = "https://open.feishu.cn/open-apis/drive/v1/files/P2SFs0PDXhVQ90tULR0c4CpFnfd/copy",
                json = {
                    "folder_token": "nodcnb914MHUAIiARtAzzgqYZCf",
                    "name": table_name,
                    "type": "sheet"
                }
            ).json()
            if result.get("code") == 0:
                info_handler(f"复制模板到文档 {table_name} 成功")
                return True, result.get("data", {}).get("file", {}).get("token")
            else:
                error_handler(f"复制模板到文档 {table_name} 失败")
                error_handler(f"错误信息：{result.get('msg')}")
                error_handler(message = traceback.format_exc())
                return False, None
        except Exception as e:
            error_handler(f"复制模板到文档 {table_name} 失败，错误信息：{e}")
            return False, None

    @classmethod
    def move_doc_to_knowledge_base(cls, document_id, file_type = "docx", parent_wiki_token = None):
        """
        移动文档到知识库
        Args:
            document_id: 文档Id
            file_type: 文件类型

        Returns:
            bool: 移动成功返回 True，失败返回 False
        """
        try:
            result = requests.post(
                headers = {
                    "Authorization": f"Bearer {cls.get_tenant_access_token()}",
                    "Content-Type": "application/json"
                },
                url = "https://open.feishu.cn/open-apis/wiki/v2/spaces/7530168808812937218/nodes/move_docs_to_wiki",
                json = {
                    "obj_type": file_type,
                    "obj_token": document_id,
                } if not parent_wiki_token else {
                    "obj_type": file_type,
                    "obj_token": document_id,
                    "parent_wiki_token": parent_wiki_token
                }
            ).json()
            if result.get("code") == 0:
                info_handler(f"移动文档 {document_id} 到知识库成功")
                return True
            else:
                error_handler(f"移动文档 {document_id} 到知识库失败")
                error_handler(f"错误信息：{result.get('msg')}")
                error_handler(message = traceback.format_exc())
                return False
        except Exception as e:
            error_handler(f"移动文档 {document_id} 到知识库失败，错误信息：{e}")
            return False

    @classmethod
    def get_child_nodes(cls, parent_node_token, page_token = None):
        """
        获取子节点列表
        Args:
            parent_node_token: 父节点token
        Returns:
            list: 子节点列表
        """
        try:

            url = f"https://open.feishu.cn/open-apis/wiki/v2/spaces/7530168808812937218/nodes?parent_node_token={parent_node_token}"
            if page_token:
                url = f"{url}&page_token={page_token}"
            result = requests.get(
                headers = {
                    "Authorization": f"Bearer {cls.get_tenant_access_token()}"
                },
                url = url
            ).json()
            if result.get("code") == 0:
                info_handler(f"获取子节点列表成功")
                items = result.get("data", {}).get("items", [])
                if not items:
                    return []
                if page_token:
                    items.extend(cls.get_child_nodes(parent_node_token = parent_node_token, page_token = page_token))
                    return items
                else:
                    return items
            else:
                error_handler(f"获取子节点列表失败")
                error_handler(f"错误信息：{result.get('msg')}")
                error_handler(message = traceback.format_exc())
                sys.exit(1)
        except Exception as e:
            error_handler(f"获取子节点列表失败，错误信息：{e}")
            sys.exit(1)


def main():
    # 1. 判断今天是不是周四
    today = datetime.date.today()
    if today.weekday() != 2:
        warning_handler("今天不是周四，不提醒")
        return
    # 2. 显示今天是哪年哪月
    today_year = today.year
    today_month = today.month
    # 今天是本月的第几周
    today_week = get_week_of_month(date = today)
    info_handler(f"今天是{today_year}年{today_month}月，是本月的第{today_week}周")

    # 3. 判断飞书知识库中是否存在 today_year-today_month 这个 文档
    knowledge_bases = FeishuTools.obtain_a_list_of_knowledge_bases()

    # 文档是否存在
    is_exist = False

    # 创建子节点
    is_create_child_node = False

    # print(knowledge_bases)
    for knowledge_base in knowledge_bases:
        if knowledge_base.get("title") == f"{today_year}年{today_month}月-线上问题回顾":
            info_handler("知识库中已经存在这个文档，无需创建")
            is_exist = True
            parent_node_token = knowledge_base.get("node_token")
            # 获取子节点列表
            # print(parent_node_token)
            child_nodes = FeishuTools.get_child_nodes(parent_node_token = parent_node_token)
            for child_node in child_nodes:
                if child_node.get("title") == f"{today_year}年{today_month}月第{today_week}周-线上问题回顾":
                    info_handler("子节点中已经存在这个文档，无需创建")
                    break
            else:
                warning_handler(
                    "知识库中存在这个文档，但子节点中没有这个文档，需要创建子节点"
                )
                is_create_child_node = True
            # 创建子节点
            # create_result, child_node_token = FeishuTools.create_month_doc(
            #     month_name = f"{today_year}年{today_month}月第{today_week}周-线上问题回顾"
            # )

    if not is_exist:
        warning_handler(message = "知识库中不存在这个文档，需要创建")
        create_result, document_id = FeishuTools.create_month_doc(
            month_name = f"{today_year}年{today_month}月-线上问题回顾"
        )
        if not create_result:
            error_handler(message = "创建文档失败")
            return

        info_handler(message = f"创建文档成功，文档ID为{document_id}")
        # 移动文档到知识库
        if not FeishuTools.move_doc_to_knowledge_base(document_id = document_id):
            error_handler(message = "移动文档到知识库失败")
            return
        # {'code': 0, 'data': {'document': {'document_id': 'YXE6dEYUJo58lexB63LcItS0nSf', 'revision_id': 1,
        #                                   'title': '2025年7月-线上问题回顾'}}, 'msg': 'success'}
        info_handler(message = "移动文档到知识库成功")
        is_create_child_node = True
        for knowledge_base in FeishuTools.obtain_a_list_of_knowledge_bases():
            if knowledge_base.get("title") == f"{today_year}年{today_month}月-线上问题回顾":
                parent_node_token = knowledge_base.get("node_token")
                break

    if is_create_child_node:
        create_result, child_node_token = FeishuTools.copy_template_to_sheet(
            table_name = f"{today_year}年{today_month}月第{today_week}周-线上问题回顾"
        )
        if not create_result:
            error_handler(message = "创建子节点失败")
            return

        info_handler(message = f"创建子节点成功，子节点token为{child_node_token}")
        # 移动子节点到 知识库-线上问题回顾-2025年7月-线上问题回顾
        if not FeishuTools.move_doc_to_knowledge_base(
                document_id = child_node_token, file_type = "sheet", parent_wiki_token = parent_node_token
        ):
            error_handler(message = "移动子节点到 知识库-线上问题回顾-2025年7月-线上问题回顾 失败")
            return
        info_handler(message = "移动子节点到 知识库-线上问题回顾-2025年7月-线上问题回顾 成功")

    # 获取最终的周度文档token（无论是新创建还是已存在）
    for knowledge_base in FeishuTools.obtain_a_list_of_knowledge_bases():
        if knowledge_base.get("title") == f"{today_year}年{today_month}月-线上问题回顾":
            parent_node_token = knowledge_base.get("node_token")
            break
    
    child_nodes = FeishuTools.get_child_nodes(parent_node_token = parent_node_token)
    final_child_node_token = None
    for child_node in child_nodes:
        if child_node.get("title") == f"{today_year}年{today_month}月第{today_week}周-线上问题回顾":
            final_child_node_token = child_node.get("obj_token")
            info_handler(message = f"找到周度文档，token为: {final_child_node_token}")
            break
    
    if final_child_node_token:
        print(f"最终周度文档token: {final_child_node_token}")
    else:
        error_handler(message = "未找到周度文档")


if __name__ == '__main__':
    main()
    #
    # print(
    #     requests.get(
    #         "https://open.feishu.cn/open-apis/drive/explorer/v2/root_folder/meta",
    #         headers = {
    #             f"Authorization": f"Bearer {FeishuTools.get_tenant_access_token()}",
    #             "Content-Type": "application/json"
    #         }
    #     ).json()
    # )
