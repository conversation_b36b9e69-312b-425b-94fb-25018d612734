#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
每周四提醒脚本 - 优化版
功能：检查今天是否为周四，如果是则显示提醒信息和本月周数，并管理飞书知识库文档
"""

import datetime
import os
import sys
import traceback
from functools import lru_cache
from typing import Optional, Tuple, List, Dict, Any

import requests
from rich.console import Console

console = Console()


def info_handler(message: str) -> None:
    """信息日志处理器"""
    console.print(f"[bold green]✅ {message}[/bold green]")


def warning_handler(message: str) -> None:
    """警告日志处理器"""
    console.print(f"[bold yellow]⚠️  {message}[/bold yellow]")


def error_handler(message: str) -> None:
    """错误日志处理器"""
    console.print(f"[bold red]❌ {message}[/bold red]")


# 节假日判断库 - 支持多种方案
HOLIDAY_SUPPORT = False
HOLIDAY_CHECKER = None

# 尝试导入可用的节假日库
try:
    from workalendar.asia import China
    HOLIDAY_CHECKER = China()
    HOLIDAY_SUPPORT = True
    HOLIDAY_METHOD = "workalendar"
    info_handler("使用 workalendar 库进行节假日判断")
except ImportError:
    try:
        import holidays
        HOLIDAY_CHECKER = holidays.China()
        HOLIDAY_SUPPORT = True
        HOLIDAY_METHOD = "holidays"
        info_handler("使用 holidays 库进行节假日判断")
    except ImportError:
        HOLIDAY_SUPPORT = False
        HOLIDAY_METHOD = "none"
        warning_handler("未安装节假日库，建议安装: pip install workalendar 或 pip install holidays")


class Config:
    """配置管理类"""
    # 飞书API配置 - 建议使用环境变量
    FEISHU_APP_ID = os.getenv("FEISHU_APP_ID", "cli_a80aebb677fcd013")
    FEISHU_APP_SECRET = os.getenv("FEISHU_APP_SECRET", "n2KbftnWf5kiCAKJXklfLhBZuy01l4b5")
    FEISHU_WIKI_SPACE_ID = "7530168808812937218"
    FEISHU_TEMPLATE_FILE_TOKEN = "P2SFs0PDXhVQ90tULR0c4CpFnfd"
    FEISHU_FOLDER_TOKEN = "nodcnb914MHUAIiARtAzzgqYZCf"

    # API URLs
    FEISHU_TOKEN_URL = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    FEISHU_WIKI_NODES_URL = f"https://open.feishu.cn/open-apis/wiki/v2/spaces/{FEISHU_WIKI_SPACE_ID}/nodes"
    FEISHU_CREATE_DOC_URL = "https://open.feishu.cn/open-apis/docx/v1/documents"
    FEISHU_COPY_FILE_URL = f"https://open.feishu.cn/open-apis/drive/v1/files/{FEISHU_TEMPLATE_FILE_TOKEN}/copy"
    FEISHU_MOVE_TO_WIKI_URL = f"https://open.feishu.cn/open-apis/wiki/v2/spaces/{FEISHU_WIKI_SPACE_ID}/nodes/move_docs_to_wiki"

    # 备用API - 如果权限不足，可以尝试使用文档API
    FEISHU_DOCS_SEARCH_URL = "https://open.feishu.cn/open-apis/drive/v1/files"

    FEISHU_MESSAGE_URL = "https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=chat_id"
    FEISHU_MESSAGE_TYPE = "text"
    FEISHU_MESSAGE_RECEIVE_ID = "oc_de4bc24e5525d48d60cae684c3329b59"
    #
    # msg_type = "text",
    # receive_id = "oc_f5a3b6e372cfeae27f91638e142d7dbe"


def get_week_of_month(date: datetime.date) -> int:
    """
    计算指定日期是本月的第几周
    
    Args:
        date: datetime.date对象
        
    Returns:
        int: 本月第几周
    """
    # 获取本月第一天
    first_day = date.replace(day = 1)
    # 计算第一天是周几（0=周一，6=周日）
    first_weekday = first_day.weekday()
    # 计算当前日期是本月第几周
    week_number = (date.day + first_weekday - 1) // 7 + 1
    return week_number


def is_holiday(date: datetime.date) -> bool:
    """
    判断指定日期是否为节假日

    Args:
        date: datetime.date对象

    Returns:
        bool: True表示是节假日，False表示不是节假日
    """
    if not HOLIDAY_SUPPORT:
        warning_handler("节假日判断功能不可用，默认为工作日")
        return False

    try:
        if HOLIDAY_METHOD == "workalendar":
            # workalendar: is_working_day 返回 True 表示工作日
            return not HOLIDAY_CHECKER.is_working_day(date)
        elif HOLIDAY_METHOD == "holidays":
            # holidays: 直接检查日期是否在节假日中
            return date in HOLIDAY_CHECKER
        else:
            return False
    except Exception as e:
        warning_handler(f"节假日判断失败: {e}，默认为工作日")
        return False


class FeishuAPIException(Exception):
    """飞书API异常类"""
    pass


class FeishuTools:
    """飞书API工具类 - 优化版"""

    def __init__(self):
        self._knowledge_bases_cache: Optional[List[Dict[str, Any]]] = None

    @classmethod
    @lru_cache(maxsize = 1)
    def get_tenant_access_token(cls) -> str:
        """
        获取飞书租户访问令牌
        
        Returns:
            str: 访问令牌
            
        Raises:
            FeishuAPIException: API调用失败时抛出
        """
        try:
            response = requests.post(
                url = Config.FEISHU_TOKEN_URL,
                headers = {"Content-Type": "application/json"},
                json = {
                    "app_id": Config.FEISHU_APP_ID,
                    "app_secret": Config.FEISHU_APP_SECRET
                },
                timeout = 30
            )
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                tenant_access_token = result.get("tenant_access_token")
                if not tenant_access_token:
                    raise FeishuAPIException("未获取到 access_token")
                info_handler("获取飞书租户 access_token 成功")
                return tenant_access_token
            else:
                raise FeishuAPIException(f"API返回错误: {result.get('msg')}")

        except requests.RequestException as e:
            raise FeishuAPIException(f"网络请求失败: {e}")
        except Exception as e:
            raise FeishuAPIException(f"获取访问令牌失败: {e}")

    def get_knowledge_bases(self, force_refresh: bool = False) -> List[Dict[str, Any]]:
        """
        获取知识库列表（带缓存）
        
        Args:
            force_refresh: 是否强制刷新缓存
            
        Returns:
            List[Dict[str, Any]]: 知识库列表
        """
        if self._knowledge_bases_cache is None or force_refresh:
            self._knowledge_bases_cache = self._fetch_knowledge_bases()
        return self._knowledge_bases_cache

    def _fetch_knowledge_bases(self) -> List[Dict[str, Any]]:
        """从API获取知识库列表"""
        try:
            headers = {
                "Authorization": f"Bearer {self.get_tenant_access_token()}",
                "Content-Type": "application/json"
            }

            info_handler("正在获取知识库列表...")

            response = requests.get(
                url=Config.FEISHU_WIKI_NODES_URL,
                headers=headers,
                timeout=30
            )

            # 记录详细的请求和响应信息
            info_handler(f"请求URL: {response.url}")
            info_handler(f"响应状态码: {response.status_code}")

            # 不直接抛出HTTP异常，先检查响应内容
            result = response.json()
            info_handler(f"API响应: {result}")

            if result.get("code") == 0:
                items = result.get("data", {}).get("items", [])
                info_handler(f"获取知识库列表成功，共 {len(items)} 个节点")
                return items
            elif result.get("code") == 131006:
                # 权限不足错误
                error_msg = "知识空间权限不足，请检查应用权限配置"
                warning_handler(error_msg)
                warning_handler("解决方案：")
                warning_handler("1. 在飞书开放平台为应用添加 wiki:wiki:readonly 权限")
                warning_handler("2. 重新发布应用")
                warning_handler("3. 或联系管理员为应用授权访问知识空间")
                raise FeishuAPIException(error_msg)
            else:
                error_msg = f"获取知识库列表失败: code={result.get('code')}, msg={result.get('msg')}"
                error_handler(error_msg)
                raise FeishuAPIException(error_msg)

        except requests.RequestException as e:
            error_msg = f"网络请求失败: {e}"
            error_handler(error_msg)
            raise FeishuAPIException(error_msg)
        except FeishuAPIException:
            # 重新抛出已知的API异常
            raise
        except Exception as e:
            error_msg = f"获取知识库列表失败: {e}"
            error_handler(error_msg)
            raise FeishuAPIException(error_msg)

    def find_document_by_title(self, title: str) -> Optional[Dict[str, Any]]:
        """
        根据标题查找文档
        
        Args:
            title: 文档标题
            
        Returns:
            Optional[Dict[str, Any]]: 找到的文档信息，未找到返回None
        """
        knowledge_bases = self.get_knowledge_bases()
        for doc in knowledge_bases:
            if doc.get("title") == title:
                return doc
        return None

    def create_month_doc(self, month_name: str) -> Tuple[bool, Optional[str]]:
        """
        创建月度文档
        
        Args:
            month_name: 月份名称
            
        Returns:
            Tuple[bool, Optional[str]]: (创建是否成功, 文档ID)
        """
        try:
            response = requests.post(
                url = Config.FEISHU_CREATE_DOC_URL,
                headers = {
                    "Authorization": f"Bearer {self.get_tenant_access_token()}",
                    "Content-Type": "application/json"
                },
                json = {"title": month_name},
                timeout = 30
            )
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                document_id = result.get("data", {}).get("document", {}).get("document_id")
                info_handler(f"创建文档 {month_name} 成功")
                return True, document_id
            else:
                error_handler(f"创建文档 {month_name} 失败: {result.get('msg')}")
                return False, None

        except Exception as e:
            error_handler(f"创建文档 {month_name} 失败: {e}")
            return False, None

    def copy_template_to_sheet(self, table_name: str) -> Tuple[bool, Optional[str]]:
        """
        复制模板到新表格
        
        Args:
            table_name: 表格名称
            
        Returns:
            Tuple[bool, Optional[str]]: (复制是否成功, 文件token)
        """
        try:
            response = requests.post(
                url = Config.FEISHU_COPY_FILE_URL,
                headers = {
                    "Authorization": f"Bearer {self.get_tenant_access_token()}",
                    "Content-Type": "application/json"
                },
                json = {
                    "folder_token": Config.FEISHU_FOLDER_TOKEN,
                    "name": table_name,
                    "type": "sheet"
                },
                timeout = 30
            )
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                file_token = result.get("data", {}).get("file", {}).get("token")
                info_handler(f"复制模板到文档 {table_name} 成功")
                return True, file_token
            else:
                error_handler(f"复制模板失败: {result.get('msg')}")
                return False, None

        except Exception as e:
            error_handler(f"复制模板失败: {e}")
            return False, None

    def move_doc_to_knowledge_base(
            self, document_id: str, file_type: str = "docx",
            parent_wiki_token: Optional[str] = None
    ) -> bool:
        """
        移动文档到知识库
        
        Args:
            document_id: 文档ID
            file_type: 文件类型
            parent_wiki_token: 父节点token
            
        Returns:
            bool: 移动是否成功
        """
        try:
            payload = {
                "obj_type": file_type,
                "obj_token": document_id,
            }
            if parent_wiki_token:
                payload["parent_wiki_token"] = parent_wiki_token

            response = requests.post(
                url = Config.FEISHU_MOVE_TO_WIKI_URL,
                headers = {
                    "Authorization": f"Bearer {self.get_tenant_access_token()}",
                    "Content-Type": "application/json"
                },
                json = payload,
                timeout = 30
            )
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                info_handler(f"移动文档 {document_id} 到知识库成功")
                return True
            else:
                error_handler(f"移动文档失败: {result.get('msg')}")
                return False

        except Exception as e:
            error_handler(f"移动文档失败: {e}")
            return False

    def get_child_nodes(self, parent_node_token: str, page_token: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取子节点列表

        Args:
            parent_node_token: 父节点token
            page_token: 分页token

        Returns:
            List[Dict[str, Any]]: 子节点列表
        """
        try:
            # 构建请求参数
            params = {
                "parent_node_token": parent_node_token
            }
            if page_token:
                params["page_token"] = page_token

            # 构建请求头
            headers = {
                "Authorization": f"Bearer {self.get_tenant_access_token()}",
                "Content-Type": "application/json"
            }

            info_handler(f"正在获取子节点列表，父节点token: {parent_node_token}")

            response = requests.get(
                url=Config.FEISHU_WIKI_NODES_URL,
                params=params,
                headers=headers,
                timeout=30
            )

            # 记录详细的请求和响应信息
            info_handler(f"请求URL: {response.url}")
            info_handler(f"响应状态码: {response.status_code}")

            # 不直接抛出HTTP异常，先检查响应内容
            result = response.json()
            info_handler(f"API响应: {result}")

            if result.get("code") == 0:
                items = result.get("data", {}).get("items", [])
                info_handler(f"获取子节点列表成功，共 {len(items)} 个节点")

                # 处理分页
                next_page_token = result.get("data", {}).get("page_token")
                if next_page_token:
                    info_handler(f"存在下一页，继续获取...")
                    items.extend(self.get_child_nodes(parent_node_token, next_page_token))

                return items
            elif result.get("code") == 131006:
                # 权限不足错误
                error_msg = "知识空间权限不足，无法获取子节点"
                warning_handler(error_msg)
                raise FeishuAPIException(error_msg)
            else:
                error_msg = f"获取子节点列表失败: code={result.get('code')}, msg={result.get('msg')}"
                error_handler(error_msg)
                raise FeishuAPIException(error_msg)

        except requests.RequestException as e:
            error_msg = f"网络请求失败: {e}"
            error_handler(error_msg)
            raise FeishuAPIException(error_msg)
        except Exception as e:
            error_msg = f"获取子节点列表失败: {e}"
            error_handler(error_msg)
            raise FeishuAPIException(error_msg)


class WeeklyReminderManager:
    """周提醒管理器"""

    def __init__(self):
        self.feishu_tools = FeishuTools()

    def check_and_process_thursday(self) -> Tuple[bool, Optional[str]]:
        """
        检查是否为周四并处理相关逻辑

        Returns:
            Tuple[bool, Optional[str]]: (是否执行了处理逻辑, 最终文档token)
        """
        today = datetime.date.today()

        # 检查是否为周四 (weekday() 返回 3 表示周四)
        if today.weekday() != 3:
            warning_handler(
                f"今天是{today.strftime('%Y年%m月%d日')} "
                f"{['周一', '周二', '周三', '周四', '周五', '周六', '周日'][today.weekday()]}，不是周四，不提醒"
            )
            return False, "不是周四，不提醒"

        # 检查是否为节假日
        if is_holiday(today):
            warning_handler(f"今天是{today.strftime('%Y年%m月%d日')}，是节假日，跳过提醒")
            return False, "是节假日，跳过提醒"

        # 获取日期信息
        today_year = today.year
        today_month = today.month
        today_week = get_week_of_month(today)

        info_handler(f"今天是{today_year}年{today_month}月，是本月的第{today_week}周")

        try:
            # 处理月度文档和周度文档
            self._process_monthly_and_weekly_docs(today_year, today_month, today_week)

            # 获取最终的周度文档token
            final_token = self._get_final_weekly_doc_token(today_year, today_month, today_week)
            if final_token:
                info_handler(f"最终周度文档token: {final_token}")
                print(f"最终周度文档token: {final_token}")

                message = f"今天是{today_year}年{today_month}月第{today_week}周\\n本周线上问题回顾文档已创建.请尽快填写\\n在线文档: https://sample.feishu.cn/sheets/{final_token}"
                return True, message
            else:
                error_handler("未找到周度文档")
                return False, None

        except FeishuAPIException as e:
            if "权限不足" in str(e) or "permission denied" in str(e):
                error_handler("飞书API权限不足，请参考 feishu_permission_fix.md 文件解决权限问题")
                error_handler("临时解决方案：手动创建文档或联系管理员")
                # 返回成功但提示权限问题，避免程序崩溃
                return True, "权限不足，请手动创建文档"
            else:
                error_handler(f"处理文档时发生错误: {e}")
                return False, None
        except Exception as e:
            error_handler(f"未知错误: {e}")
            error_handler(traceback.format_exc())
            return False, None

    def _process_monthly_and_weekly_docs(self, year: int, month: int, week: int) -> None:
        """处理月度和周度文档"""
        month_doc_title = f"{year}年{month}月-线上问题回顾"
        week_doc_title = f"{year}年{month}月第{week}周-线上问题回顾"

        # 查找月度文档
        month_doc = self.feishu_tools.find_document_by_title(month_doc_title)
        parent_node_token = None

        if month_doc:
            info_handler("月度文档已存在")
            parent_node_token = month_doc.get("node_token")

            # 检查周度文档是否存在
            if self._check_weekly_doc_exists(parent_node_token, week_doc_title):
                info_handler("周度文档已存在，无需创建")
                return
            else:
                warning_handler("月度文档存在，但周度文档不存在，需要创建周度文档")
        else:
            # 创建月度文档
            warning_handler("月度文档不存在，需要创建")
            success, document_id = self.feishu_tools.create_month_doc(month_doc_title)
            if not success:
                raise FeishuAPIException("创建月度文档失败")

            # 移动月度文档到知识库
            if not self.feishu_tools.move_doc_to_knowledge_base(document_id):
                raise FeishuAPIException("移动月度文档到知识库失败")

            # 刷新缓存并获取新创建的文档信息
            self.feishu_tools.get_knowledge_bases(force_refresh = True)
            month_doc = self.feishu_tools.find_document_by_title(month_doc_title)
            if month_doc:
                parent_node_token = month_doc.get("node_token")

        # 创建周度文档
        self._create_weekly_doc(week_doc_title, parent_node_token)

    def _check_weekly_doc_exists(self, parent_node_token: str, week_doc_title: str) -> bool:
        """检查周度文档是否存在"""
        try:
            child_nodes = self.feishu_tools.get_child_nodes(parent_node_token)
            for child_node in child_nodes:
                if child_node.get("title") == week_doc_title:
                    return True
            return False
        except FeishuAPIException:
            return False

    def _create_weekly_doc(self, week_doc_title: str, parent_node_token: Optional[str]) -> None:
        """创建周度文档"""
        success, child_node_token = self.feishu_tools.copy_template_to_sheet(week_doc_title)
        if not success:
            raise FeishuAPIException("创建周度文档失败")

        info_handler(f"创建周度文档成功，token: {child_node_token}")

        # 移动周度文档到月度文档下
        if not self.feishu_tools.move_doc_to_knowledge_base(
                child_node_token, file_type = "sheet", parent_wiki_token = parent_node_token
        ):
            raise FeishuAPIException("移动周度文档到月度文档失败")

        info_handler("周度文档创建并移动成功")

    def _get_final_weekly_doc_token(self, year: int, month: int, week: int) -> Optional[str]:
        """
        获取最终的周度文档token（无论是新创建还是已存在）
        
        Args:
            year: 年份
            month: 月份  
            week: 周数
            
        Returns:
            Optional[str]: 周度文档token，未找到返回None
        """
        try:
            month_doc_title = f"{year}年{month}月-线上问题回顾"
            week_doc_title = f"{year}年{month}月第{week}周-线上问题回顾"

            # 刷新缓存并查找月度文档
            month_doc = self.feishu_tools.find_document_by_title(month_doc_title)
            if not month_doc:
                error_handler("未找到月度文档")
                return None

            parent_node_token = month_doc.get("node_token")
            child_nodes = self.feishu_tools.get_child_nodes(parent_node_token)

            # 查找周度文档
            for child_node in child_nodes:
                if child_node.get("title") == week_doc_title:
                    token = child_node.get("obj_token")
                    info_handler(f"找到周度文档，token为: {token}")
                    return token

            error_handler("未找到周度文档")
            return None

        except Exception as e:
            error_handler(f"获取周度文档token失败: {e}")
            return None


# 消息通知
class MessageHandler:
    """
    消息通知器
    """
    @staticmethod
    def error(message: str) -> None:
        """
        发送错误消息
        Args:
            message: 消息内容
        """

        headers = {
            "Authorization": f"Bearer {FeishuTools.get_tenant_access_token()}",
            "Content-Type": "application/json"
        }
        content = "{\"text\":\"<at user_id=\\\"4fee8g3c\\\"></at> " + message + "\"}"

        data = {
            "content": content,
            "msg_type": Config.FEISHU_MESSAGE_TYPE,
            "receive_id": Config.FEISHU_MESSAGE_RECEIVE_ID
        }

        try:
            response = requests.post(url = Config.FEISHU_MESSAGE_URL, headers = headers, json = data, timeout = 30)
            if response.status_code == 200:
                info_handler("错误消息发送成功")
                print("Response:", response.json())
            else:
                warning_handler(f"错误消息发送失败: {response.status_code}")
        except Exception as e:
            warning_handler(f"发送错误消息失败: {e}")

    def warning(self, message: str) -> None:
        """
        发送警告消息
        Args:
            message: 消息内容
        """
        print(f"WARNING: {message}")

    @staticmethod
    def info(message: str) -> None:
        """
        发送信息消息
        Args:
            message: 消息内容
        """
        headers = {
            "Authorization": f"Bearer {FeishuTools.get_tenant_access_token()}",
            "Content-Type": "application/json"
        }
        content = "{\"text\":\"<at user_id=\\\"all\\\"></at> " + message + "\"}"

        data = {
            "content": content,
            "msg_type": Config.FEISHU_MESSAGE_TYPE,
            "receive_id": Config.FEISHU_MESSAGE_RECEIVE_ID
        }

        try:
            response = requests.post(url = Config.FEISHU_MESSAGE_URL, headers = headers, json = data, timeout = 30)
            if response.status_code == 200:
                info_handler("信息消息发送成功")
                print("Response:", response.json())
            else:
                warning_handler(f"信息消息发送失败: {response.status_code}")
                print("Response:", response.json())
        except Exception as e:
            warning_handler(f"发送信息消息失败: {e}")


def main() -> None:
    """主函数"""
    try:
        manager = WeeklyReminderManager()
        success, message = manager.check_and_process_thursday()
        if not success and message in [
            "不是周四，不提醒",
            "是节假日，跳过提醒"
        ]:
            return

        if not success:
            MessageHandler.error("创建今日文档失败，请检查日志")
            sys.exit(1)
        else:
            MessageHandler.info(message = message)
    except KeyboardInterrupt:
        warning_handler("用户中断执行")
        sys.exit(1)
    except Exception as e:
        error_handler(f"程序执行失败: {e}")
        error_handler(traceback.format_exc())
        sys.exit(1)


if __name__ == '__main__':
    main()
