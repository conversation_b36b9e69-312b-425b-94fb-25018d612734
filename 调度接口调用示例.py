#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : 调度接口调用示例.py
# @Time    : 2025/08/01 15:09
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import requests
import json

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))

"""
below is the example of calling the scheduling interface
下面是调度接口调用示例
"""


# 各种接口请求参数


class SchedulingInterface:
    """
    SchedulingInterface is a class for calling the scheduling interface. | 调度接口调用类
    """
    SCHEDULE_API_URL = "http://127.0.0.1:12345"

    def home_page_info(self):
        """
        项目统计信息
        描述:
            请求地址	/dolphinscheduler/home/<USER>
            接口描述	统计指定日期的项目以及工作流和任务数据
            请求类型	GET
            返回值类型	JSON
            请求体格式	form-data
        请求参数:
            结束时间	  endDate	string	FALSE	Eg: 2024-10-31 00:00:00
            页码号	pageNo	integer	TRUE	Eg: 1
            页大小	pageSize	integer	TRUE	Eg: 10
            开始时间	startDate	string	FALSE	Eg: 2024-10-31 23:59:59
            排序字段	sortBy	string	FALSE	FINISH_PROCESS_INSTANCE_COUNT 完成工作流实例数
            RUNNING_PROCESS_INSTANCE_COUNT 运行中工作流实例数
            FAILURE_PROCESS_INSTANCE_COUN 失败工作流实例数 FAILURE_TASK_INSTANCE_COUNT 失败任务实例数
            排序方式	orderBy	string	FALSE	DESC 倒序
            ASC 顺序
        响应参数:
        返回状态码	code	integer(int32)	0表示成功 非0表示不成功
返回数据	data	Object(json)
|- 数据列表	totalList	List
|-|-项目id	projectId	int
|-|-项目名称	projectName	String	调度项目名称
|-|-项目code	projectCode	Long
|-|-项目所属人id	ownerId	int
|-|-项目所属人名称	owerName	string
|-|-有效流程定义数量	validProcessDefinitionCount	int
	统计时间区间内，指定项目下是否有定时将要执行时间的工作流定义数量。
|-|-应调度的工作流数量	processDefinitionCount	int	统计在时间区间内，依据定时和日切，统计应该执行工作流的数量。
|-|-已完成的工作流个数	finishProcessInstanceCount	int	统计工作流开始时间在指定时间区间内，工作流状态是终态的工作流个数
|-|-正在执行的工作流个数	runningProcessInstanceCount	int	统计工作流开始时间在指定时间区间内，工作流状态不是终态的工作流个数
|-|-失败的工作流实例个数	failureProcessInstanceCount	int	统计工作流开始时间在指定时间区间内，工作流状态是失败状态的工作流个数
|-|-失败的任务实例个数	failureTaskInstanceCount	int	统计任务开始时间在指定时间区间内，任务状态是失败状态的任务个数
|-总条数	total	int
|-总页数	totalPage	int
|-每页数据条数	pageSize	int
|-当前页数	currentPage	int
是否失败	failed	boolean
响应结果	msg	string
是否成功	success	boolean
        Returns:
        """
        request_address = "/dolphinscheduler/home/<USER>"
