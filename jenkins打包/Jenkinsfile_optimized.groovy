pipeline {
    agent any
    options {
        timestamps()
    }
    environment {
        whale_scheduler_version = '2.6-release'
        currentPath = pwd()
        jenkins_url = "http://***********:8080/blue/organizations/jenkins/WhaleStudio_2.6-release/detail/WhaleStudio_2.6-release/${env.BUILD_ID}/pipeline"
        PYTHON_PATH = '/data/miniconda3/bin/python'
        BUILD_SCRIPT = '/data/jenkins/whaleStudio_build_test.py'
        GIT_TOKEN = '****************************************'
        GITHUB_BASE_URL = "https://${GIT_TOKEN}@github.com/WhaleOps"
        
        // 项目目录定义
        WHALESCHEDULER_DIR = 'whalescheduler'
        WHALETUNNEL_DIR = 'whaletunnel'
        WHALETUNNEL_WEB_DIR = 'whaletunnel-web'
        WHALESCHEDULER_UI_DIR = 'whalescheduler/whalescheduler-ui'
    }
    
    stages {
        stage('Clean workspace') {
            steps {
                echo "Cleaning workspace"
                cleanWs()
                echo "Workspace cleaned"
            }
        }
        
        stage("Clone WhaleStudio Code") {
            parallel {
                stage("Clone WhaleScheduler") {
                    steps {
                        script {
                            retry(3) {
                                echo "Cloning WhaleScheduler..."
                                git branch: "${whale_scheduler_version}", 
                                    url: "${GITHUB_BASE_URL}/whalescheduler.git"
                                
                                // 克隆UI子项目
                                dir("${WHALESCHEDULER_DIR}") {
                                    git branch: "${whale_scheduler_version}", 
                                        url: "${GITHUB_BASE_URL}/whalescheduler-ui.git",
                                        dir: 'whalescheduler-ui'
                                }
                            }
                        }
                    }
                }
                
                stage("Clone WhaleTunnel") {
                    steps {
                        script {
                            retry(3) {
                                echo "Cloning WhaleTunnel..."
                                dir("${WHALETUNNEL_DIR}") {
                                    git branch: "${whale_scheduler_version}", 
                                        url: "${GITHUB_BASE_URL}/whaletunnel.git"
                                }
                            }
                        }
                    }
                }
                
                stage("Clone WhaleTunnel-Web") {
                    steps {
                        script {
                            retry(3) {
                                echo "Cloning WhaleTunnel-Web..."
                                dir("${WHALETUNNEL_WEB_DIR}") {
                                    git branch: "${whale_scheduler_version}", 
                                        url: "${GITHUB_BASE_URL}/whaletunnel-web.git"
                                }
                            }
                        }
                    }
                }
            }
        }
        
        stage("Get Current Commit ID") {
            steps {
                script {
                    echo "Getting commit IDs..."
                    
                    dir("${WHALESCHEDULER_DIR}") {
                        whalescheduler_commit_id = sh(
                            script: "git rev-parse --short=7 HEAD", 
                            returnStdout: true
                        ).trim()
                        echo "WhaleScheduler commit: ${whalescheduler_commit_id}"
                    }
                    
                    dir("${WHALESCHEDULER_UI_DIR}") {
                        whalescheduler_ui_commit_id = sh(
                            script: "git rev-parse --short=7 HEAD", 
                            returnStdout: true
                        ).trim()
                        echo "WhaleScheduler UI commit: ${whalescheduler_ui_commit_id}"
                    }
                    
                    dir("${WHALETUNNEL_DIR}") {
                        whaletunnel_commit_id = sh(
                            script: "git rev-parse --short=7 HEAD", 
                            returnStdout: true
                        ).trim()
                        echo "WhaleTunnel commit: ${whaletunnel_commit_id}"
                    }
                    
                    dir("${WHALETUNNEL_WEB_DIR}") {
                        whaletunnel_web_commit_id = sh(
                            script: "git rev-parse --short=7 HEAD", 
                            returnStdout: true
                        ).trim()
                        echo "WhaleTunnel Web commit: ${whaletunnel_web_commit_id}"
                    }
                }
            }
        }
        
        stage("Get Version") {
            steps {
                script {
                    echo "Getting version information..."
                    dir("${WHALESCHEDULER_DIR}") {
                        def pomContent = readFile('pom.xml')
                        def versionMatcher = pomContent =~ /<version>([^<]+)<\/version>/
                        if (versionMatcher) {
                            minor_version = versionMatcher[0][1]
                            echo "Detected version: ${minor_version}"
                        } else {
                            error "Could not extract version from pom.xml"
                        }
                    }
                }
            }
        }
        
        stage("Build WhaleStudio") {
            steps {
                script {
                    retry(3) {
                        echo "Building WhaleStudio..."
                        
                        dir("${WHALESCHEDULER_DIR}") {
                            def baseCommand = "mvnd clean package -T2 -Dmaven.test.skip -Dcheckstyle.skip=true -Dspotless.skip=true -Prelease -Dseatunnel-framework.version=${minor_version} -Dwhaletunnel-web.version=${minor_version}"
                            def buildCommand = ("${customer}" == "None") ? baseCommand : "${baseCommand} ${customer}"
                            
                            echo "Build command: ${buildCommand}"
                            sh buildCommand
                        }
                    }
                }
            }
        }
        
        stage("Make three-in-one directory") {
            steps {
                script {
                    echo "Creating three-in-one directory structure..."
                    sh "mkdir -p whalestudio_${minor_version}/{whaletunnel,datasource,whalescheduler}"
                }
            }
        }
        
        stage("Copy WhaleTunnel") {
            steps {
                script {
                    echo "Copying WhaleTunnel files..."
                    dir("${WHALETUNNEL_DIR}") {
                        sh """
                            find . -name "whaletunnel-dist-*.tar.gz" -exec cp {} ../whalestudio_${minor_version}/whaletunnel/ \\;
                            ls -la ../whalestudio_${minor_version}/whaletunnel/
                        """
                    }
                }
            }
        }
        
        stage("Copy WhaleScheduler") {
            steps {
                script {
                    echo "Copying WhaleScheduler files..."
                    dir("${WHALESCHEDULER_DIR}") {
                        sh """
                            find . -name "whalescheduler-dist-*.tar.gz" -exec cp {} ../whalestudio_${minor_version}/whalescheduler/ \\;
                            ls -la ../whalestudio_${minor_version}/whalescheduler/
                        """
                    }
                }
            }
        }
        
        stage("Copy DataSource") {
            steps {
                script {
                    echo "Copying DataSource files..."
                    dir("${WHALETUNNEL_WEB_DIR}") {
                        sh """
                            find . -name "whaletunnel-web-dist-*.tar.gz" -exec cp {} ../whalestudio_${minor_version}/datasource/ \\;
                            ls -la ../whalestudio_${minor_version}/datasource/
                        """
                    }
                }
            }
        }
        
        stage("Package WhaleStudio") {
            steps {
                script {
                    echo "Creating final package..."
                    sh """
                        tar -czf whalestudio_${minor_version}.tar.gz whalestudio_${minor_version}/
                        ls -la whalestudio_${minor_version}.tar.gz
                    """
                }
            }
        }
        
        stage("Archive Artifacts") {
            steps {
                script {
                    echo "Archiving artifacts..."
                    archiveArtifacts artifacts: "whalestudio_${minor_version}.tar.gz", 
                                   fingerprint: true
                }
            }
        }
    }
    
    post {
        success {
            script {
                echo "Build completed successfully!"
                def commonCommand = "${PYTHON_PATH} ${BUILD_SCRIPT} --operation send_end_message --jenkins_url ${jenkins_url} --branch ${whale_scheduler_version} --latest_version ${minor_version} --build_status success --build_path ${currentPath} --commit_id ${whalescheduler_commit_id},${whalescheduler_ui_commit_id},${whaletunnel_commit_id},${whaletunnel_web_commit_id}"
                def command = ("${customer}" == "None") ? commonCommand : "${commonCommand} ${customer}"
                sh "${command}"
            }
        }
        
        failure {
            script {
                echo "Build failed!"
                def commonCommand = "${PYTHON_PATH} ${BUILD_SCRIPT} --operation send_end_message --jenkins_url ${jenkins_url} --branch ${whale_scheduler_version} --latest_version ${minor_version} --build_status failure --build_path ${currentPath}"
                def command = ("${customer}" == "None") ? commonCommand : "${commonCommand} ${customer}"
                sh "${command}"
            }
        }
        
        always {
            script {
                echo "Cleaning up workspace..."
                cleanWs()
            }
        }
    }
}
