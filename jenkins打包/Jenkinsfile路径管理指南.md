# Jenkinsfile 路径管理指南

## 概述

在Jenkins Pipeline中，可以通过多种方式设置和管理每个阶段命令所在的相对路径。

## 🎯 方法对比

| 方法 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| `dir` 步骤 | 清晰、安全、推荐 | 需要嵌套结构 | 大多数情况 |
| `cd` 命令 | 简单直接 | 容易出错、不安全 | 简单脚本 |
| 环境变量 | 灵活配置 | 需要额外管理 | 复杂项目 |

## 📁 方法一：使用 `dir` 步骤（推荐）

### 基本用法

```groovy
pipeline {
    agent any
    stages {
        stage('Build in specific directory') {
            steps {
                dir('whalescheduler') {
                    sh 'mvn clean package'
                    sh 'ls -la'
                }
            }
        }
    }
}
```

### 嵌套目录

```groovy
stage('Nested directories') {
    steps {
        dir('whalescheduler') {
            dir('whalescheduler-ui') {
                sh 'npm install'
                sh 'npm run build'
            }
        }
    }
}
```

### 多个目录操作

```groovy
stage('Multiple directories') {
    steps {
        dir('whaletunnel') {
            sh 'mvn clean install'
        }
        dir('whaletunnel-web') {
            sh 'npm install'
            sh 'npm run build'
        }
    }
}
```

## 🔧 方法二：在shell命令中使用 `cd`

### 单个命令

```groovy
stage('Build with cd') {
    steps {
        sh 'cd whalescheduler && mvn clean package'
    }
}
```

### 多行脚本

```groovy
stage('Multi-line script') {
    steps {
        sh '''
            cd whalescheduler
            mvn clean package
            cd ../whaletunnel
            mvn clean install
        '''
    }
}
```

### 使用子shell

```groovy
stage('Subshell approach') {
    steps {
        sh '(cd whalescheduler && mvn clean package)'
        sh '(cd whaletunnel && mvn clean install)'
    }
}
```

## 🌍 方法三：环境变量管理

### 定义项目目录

```groovy
pipeline {
    agent any
    environment {
        WHALESCHEDULER_DIR = 'whalescheduler'
        WHALETUNNEL_DIR = 'whaletunnel'
        WHALETUNNEL_WEB_DIR = 'whaletunnel-web'
    }
    stages {
        stage('Build') {
            steps {
                dir("${WHALESCHEDULER_DIR}") {
                    sh 'mvn clean package'
                }
            }
        }
    }
}
```

### 动态路径

```groovy
environment {
    PROJECT_ROOT = "${WORKSPACE}/projects"
    BUILD_DIR = "${PROJECT_ROOT}/build"
}

stages {
    stage('Setup') {
        steps {
            sh "mkdir -p ${BUILD_DIR}"
            dir("${BUILD_DIR}") {
                sh 'echo "Working in build directory"'
            }
        }
    }
}
```

## 🚀 方法四：并行构建不同目录

```groovy
stage("Parallel Build") {
    parallel {
        stage("Build WhaleScheduler") {
            steps {
                dir('whalescheduler') {
                    sh 'mvn clean package -T4'
                }
            }
        }
        stage("Build WhaleTunnel") {
            steps {
                dir('whaletunnel') {
                    sh 'mvn clean package -T4'
                }
            }
        }
        stage("Build WhaleTunnel-Web") {
            steps {
                dir('whaletunnel-web') {
                    sh 'npm install && npm run build'
                }
            }
        }
    }
}
```

## 📋 实际应用示例

### 您当前项目的优化版本

```groovy
pipeline {
    agent any
    environment {
        // 项目目录定义
        WHALESCHEDULER_DIR = 'whalescheduler'
        WHALETUNNEL_DIR = 'whaletunnel'
        WHALETUNNEL_WEB_DIR = 'whaletunnel-web'
    }
    
    stages {
        stage("Clone Projects") {
            parallel {
                stage("Clone WhaleScheduler") {
                    steps {
                        dir("${WHALESCHEDULER_DIR}") {
                            git branch: "${whale_scheduler_version}", 
                                url: "${GITHUB_BASE_URL}/whalescheduler.git"
                        }
                    }
                }
                stage("Clone WhaleTunnel") {
                    steps {
                        dir("${WHALETUNNEL_DIR}") {
                            git branch: "${whale_scheduler_version}", 
                                url: "${GITHUB_BASE_URL}/whaletunnel.git"
                        }
                    }
                }
            }
        }
        
        stage("Build Projects") {
            steps {
                // WhaleScheduler构建
                dir("${WHALESCHEDULER_DIR}") {
                    sh 'mvnd clean package -T2 -Dmaven.test.skip'
                }
                
                // WhaleTunnel构建
                dir("${WHALETUNNEL_DIR}") {
                    sh 'mvn clean install'
                }
                
                // WhaleTunnel-Web构建
                dir("${WHALETUNNEL_WEB_DIR}") {
                    sh 'npm install && npm run build'
                }
            }
        }
        
        stage("Get Commit IDs") {
            steps {
                script {
                    dir("${WHALESCHEDULER_DIR}") {
                        whalescheduler_commit_id = sh(
                            script: "git rev-parse --short=7 HEAD", 
                            returnStdout: true
                        ).trim()
                    }
                    
                    dir("${WHALETUNNEL_DIR}") {
                        whaletunnel_commit_id = sh(
                            script: "git rev-parse --short=7 HEAD", 
                            returnStdout: true
                        ).trim()
                    }
                }
            }
        }
    }
}
```

## ⚠️ 注意事项和最佳实践

### 1. 路径安全

```groovy
// ✅ 推荐：使用dir步骤
dir('project') {
    sh 'make build'
}

// ❌ 不推荐：直接cd可能有安全风险
sh 'cd ../../../etc && cat passwd'
```

### 2. 错误处理

```groovy
stage('Build with error handling') {
    steps {
        script {
            try {
                dir('whalescheduler') {
                    sh 'mvn clean package'
                }
            } catch (Exception e) {
                echo "Build failed in whalescheduler: ${e.getMessage()}"
                currentBuild.result = 'FAILURE'
            }
        }
    }
}
```

### 3. 路径验证

```groovy
stage('Validate paths') {
    steps {
        script {
            def projectDirs = ['whalescheduler', 'whaletunnel', 'whaletunnel-web']
            
            projectDirs.each { dirName ->
                if (!fileExists(dirName)) {
                    error "Directory ${dirName} does not exist"
                }
            }
        }
    }
}
```

### 4. 动态路径生成

```groovy
stage('Dynamic paths') {
    steps {
        script {
            def version = "2.6-release"
            def buildDir = "build-${version}-${env.BUILD_NUMBER}"
            
            dir(buildDir) {
                sh 'echo "Building in dynamic directory"'
            }
        }
    }
}
```

## 🔍 调试技巧

### 1. 显示当前工作目录

```groovy
stage('Debug paths') {
    steps {
        sh 'pwd'
        dir('whalescheduler') {
            sh 'pwd'
            sh 'ls -la'
        }
        sh 'pwd'  // 回到原目录
    }
}
```

### 2. 环境变量调试

```groovy
stage('Debug environment') {
    steps {
        sh 'env | grep -E "(WORKSPACE|BUILD|PATH)"'
        echo "Current workspace: ${WORKSPACE}"
        echo "Current path: ${pwd()}"
    }
}
```

## 📝 迁移指南

### 从cd命令迁移到dir步骤

**原来的写法：**
```groovy
sh "cd whalescheduler && mvn clean package"
```

**优化后的写法：**
```groovy
dir('whalescheduler') {
    sh 'mvn clean package'
}
```

### 复杂脚本的迁移

**原来的写法：**
```groovy
sh '''
    cd whalescheduler
    mvn clean package
    cd ../whaletunnel
    mvn clean install
    cd ..
    tar -czf release.tar.gz whalescheduler/ whaletunnel/
'''
```

**优化后的写法：**
```groovy
dir('whalescheduler') {
    sh 'mvn clean package'
}
dir('whaletunnel') {
    sh 'mvn clean install'
}
sh 'tar -czf release.tar.gz whalescheduler/ whaletunnel/'
```

## 🎯 总结

1. **优先使用 `dir` 步骤** - 更安全、更清晰
2. **合理使用环境变量** - 便于维护和配置
3. **利用并行构建** - 提高构建效率
4. **添加错误处理** - 提高Pipeline稳定性
5. **做好路径验证** - 避免运行时错误

使用这些方法，您可以更好地管理Jenkins Pipeline中的工作目录，提高构建脚本的可读性和维护性。
