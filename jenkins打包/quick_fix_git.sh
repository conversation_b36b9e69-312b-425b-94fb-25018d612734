#!/bin/bash

# Jenkins Git问题快速修复脚本
# 解决SSH连接问题，改用HTTPS认证

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
JENKINS_HOME="${JENKINS_HOME:-/data/jenkins/jenkins_home}"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# GitHub Token (从Jenkinsfile_中提取)
GIT_TOKEN="****************************************"

# 1. 安装必要工具
install_tools() {
    log_info "安装必要工具..."
    
    # 安装Git
    if ! command -v git &> /dev/null; then
        log_info "安装Git..."
        if command -v yum &> /dev/null; then
            sudo yum install -y git
        elif command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y git
        fi
    fi
    
    # 安装netcat
    if ! command -v nc &> /dev/null; then
        log_info "安装netcat..."
        if command -v yum &> /dev/null; then
            sudo yum install -y nc nmap-ncat
        elif command -v apt-get &> /dev/null; then
            sudo apt-get install -y netcat-openbsd
        fi
    fi
    
    log_info "✓ 工具安装完成"
}

# 2. 配置Git
configure_git() {
    log_info "配置Git..."
    
    git config --global user.name "Jenkins"
    git config --global user.email "jenkins@localhost"
    git config --global init.defaultBranch main
    git config --global pull.rebase false
    
    # 清除可能的SSH配置
    git config --global --unset core.sshCommand 2>/dev/null || true
    
    log_info "✓ Git配置完成"
}

# 3. 更新所有Jenkinsfile使用HTTPS
update_jenkinsfiles() {
    log_info "更新Jenkinsfile使用HTTPS..."
    
    local files=(
        "$SCRIPT_DIR/Jenkinsfile_"
        "$SCRIPT_DIR/jenkinsfile" 
        "$SCRIPT_DIR/deploy_jenkinsfile"
    )
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            log_info "更新文件: $(basename "$file")"
            
            # 备份原文件
            cp "$file" "${file}.backup.$(date +%Y%m%d_%H%M%S)"
            
            # 替换SSH URL为HTTPS URL
            sed -i "s|**************:WhaleOps|https://${GIT_TOKEN}@github.com/WhaleOps|g" "$file"
            
            # 检查是否还有SSH URL
            if grep -q "**************" "$file"; then
                log_warn "文件 $(basename "$file") 中仍有SSH URL，请手动检查"
            else
                log_info "✓ $(basename "$file") 已更新为HTTPS"
            fi
        fi
    done
}

# 4. 测试GitHub连接
test_github_connection() {
    log_info "测试GitHub连接..."
    
    # 测试Token有效性
    local response=$(curl -s -H "Authorization: token $GIT_TOKEN" https://api.github.com/user)
    
    if echo "$response" | grep -q '"login"'; then
        local username=$(echo "$response" | grep '"login"' | cut -d'"' -f4)
        log_info "✓ GitHub Token有效，用户: $username"
        return 0
    else
        log_error "✗ GitHub Token无效或已过期"
        echo "响应: $response"
        return 1
    fi
}

# 5. 测试Git克隆
test_git_clone() {
    log_info "测试Git克隆..."
    
    local test_dir="/tmp/git_test_$$"
    mkdir -p "$test_dir"
    cd "$test_dir"
    
    # 测试克隆
    if git clone "https://${GIT_TOKEN}@github.com/WhaleOps/whalescheduler.git" --depth 1 2>/dev/null; then
        log_info "✓ Git克隆测试成功"
        cd /
        rm -rf "$test_dir"
        return 0
    else
        log_error "✗ Git克隆测试失败"
        cd /
        rm -rf "$test_dir"
        return 1
    fi
}

# 6. 创建Jenkins环境配置
create_jenkins_env() {
    log_info "创建Jenkins环境配置..."
    
    # 创建环境变量文件
    cat > "$JENKINS_HOME/git_env.sh" << EOF
#!/bin/bash
# Jenkins Git环境配置

# 清除SSH相关环境变量
unset SSH_AUTH_SOCK
unset GIT_SSH_COMMAND

# 设置Git使用HTTPS
export GIT_TOKEN="$GIT_TOKEN"
export GITHUB_BASE_URL="https://\${GIT_TOKEN}@github.com/WhaleOps"

# Git配置
git config --global user.name "Jenkins"
git config --global user.email "jenkins@localhost"
git config --global --unset core.sshCommand 2>/dev/null || true
EOF
    
    chmod +x "$JENKINS_HOME/git_env.sh"
    log_info "✓ 环境配置已创建: $JENKINS_HOME/git_env.sh"
}

# 7. 清理SSH配置
cleanup_ssh_config() {
    log_info "清理SSH配置..."
    
    # 清理可能导致问题的SSH配置
    if [ -f "$JENKINS_HOME/.ssh/config" ]; then
        mv "$JENKINS_HOME/.ssh/config" "$JENKINS_HOME/.ssh/config.backup.$(date +%Y%m%d_%H%M%S)"
        log_info "已备份SSH配置文件"
    fi
    
    # 清理Git SSH配置
    git config --global --unset core.sshCommand 2>/dev/null || true
    
    log_info "✓ SSH配置已清理"
}

# 8. 验证修复结果
verify_fix() {
    log_info "验证修复结果..."
    
    echo ""
    echo "=== 验证清单 ==="
    
    # 检查Git
    if command -v git &> /dev/null; then
        echo "✓ Git已安装: $(git --version)"
    else
        echo "✗ Git未安装"
        return 1
    fi
    
    # 检查netcat
    if command -v nc &> /dev/null; then
        echo "✓ netcat已安装"
    else
        echo "✗ netcat未安装"
    fi
    
    # 检查GitHub连接
    if test_github_connection; then
        echo "✓ GitHub Token有效"
    else
        echo "✗ GitHub Token无效"
        return 1
    fi
    
    # 检查Git克隆
    if test_git_clone; then
        echo "✓ Git克隆测试成功"
    else
        echo "✗ Git克隆测试失败"
        return 1
    fi
    
    # 检查Jenkinsfile
    local https_count=0
    local ssh_count=0
    
    for file in "$SCRIPT_DIR/Jenkinsfile_" "$SCRIPT_DIR/jenkinsfile" "$SCRIPT_DIR/deploy_jenkinsfile"; do
        if [ -f "$file" ]; then
            if grep -q "https://.*@github.com" "$file"; then
                ((https_count++))
            fi
            if grep -q "**************" "$file"; then
                ((ssh_count++))
            fi
        fi
    done
    
    echo "✓ Jenkinsfile HTTPS配置: $https_count 个文件"
    if [ $ssh_count -gt 0 ]; then
        echo "⚠ 仍有 $ssh_count 个文件使用SSH"
    fi
    
    echo ""
    return 0
}

# 主函数
main() {
    echo "=== Jenkins Git问题快速修复 ==="
    echo ""
    
    log_info "开始修复Git问题..."
    
    # 检查权限
    if [ "$EUID" -ne 0 ] && ! sudo -n true 2>/dev/null; then
        log_error "需要sudo权限来安装软件包"
        log_error "请运行: sudo $0"
        exit 1
    fi
    
    # 执行修复步骤
    install_tools
    configure_git
    cleanup_ssh_config
    update_jenkinsfiles
    create_jenkins_env
    
    echo ""
    log_info "修复完成，正在验证..."
    
    if verify_fix; then
        echo ""
        log_info "🎉 所有问题已修复！"
        echo ""
        echo "=== 下一步操作 ==="
        echo "1. 重启Jenkins服务: ./jenkins_manager.sh restart"
        echo "2. 重新运行失败的Pipeline"
        echo "3. 如果仍有问题，检查Jenkins日志"
        echo ""
        echo "=== 备份文件 ==="
        echo "原始Jenkinsfile已备份为 *.backup.* 文件"
        
    else
        echo ""
        log_error "修复过程中发现问题，请检查上述错误信息"
        exit 1
    fi
}

# 显示帮助
show_help() {
    echo "Jenkins Git问题快速修复脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help    显示帮助信息"
    echo ""
    echo "功能:"
    echo "  1. 安装Git和netcat"
    echo "  2. 配置Git使用HTTPS认证"
    echo "  3. 更新所有Jenkinsfile使用HTTPS URL"
    echo "  4. 清理可能导致问题的SSH配置"
    echo "  5. 测试GitHub连接和Git克隆"
}

# 处理命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    "")
        main
        ;;
    *)
        echo "未知选项: $1"
        echo "使用 -h 查看帮助信息"
        exit 1
        ;;
esac
