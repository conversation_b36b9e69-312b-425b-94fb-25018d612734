#!/bin/bash

# Jenkins Git问题修复脚本
# 解决Jenkins无法找到Git命令的问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S'): $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S'): $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S'): $1"
}

# 检查操作系统
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    elif type lsb_release >/dev/null 2>&1; then
        OS=$(lsb_release -si)
        VER=$(lsb_release -sr)
    elif [ -f /etc/redhat-release ]; then
        OS="Red Hat Enterprise Linux"
        VER=$(cat /etc/redhat-release | sed 's/.*release //' | sed 's/ .*//')
    else
        OS=$(uname -s)
        VER=$(uname -r)
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 检查Git安装状态
check_git_status() {
    log_info "检查Git安装状态..."
    
    # 检查git命令是否存在
    if command -v git &> /dev/null; then
        local git_version=$(git --version)
        log_info "Git已安装: $git_version"
        log_info "Git路径: $(which git)"
        return 0
    else
        log_warn "Git未安装或不在PATH中"
        return 1
    fi
}

# 安装Git
install_git() {
    log_info "开始安装Git..."
    
    # 检查权限
    if [ "$EUID" -ne 0 ] && ! sudo -n true 2>/dev/null; then
        log_error "需要root权限或sudo权限来安装Git"
        log_error "请使用以下命令之一运行此脚本:"
        log_error "  sudo $0"
        log_error "  或者以root用户运行: $0"
        exit 1
    fi
    
    # 根据包管理器安装Git
    if command -v yum &> /dev/null; then
        # CentOS/RHEL/Fedora
        log_info "使用yum安装Git..."
        if [ "$EUID" -eq 0 ]; then
            yum install -y git
        else
            sudo yum install -y git
        fi
        
    elif command -v dnf &> /dev/null; then
        # Fedora (newer versions)
        log_info "使用dnf安装Git..."
        if [ "$EUID" -eq 0 ]; then
            dnf install -y git
        else
            sudo dnf install -y git
        fi
        
    elif command -v apt-get &> /dev/null; then
        # Ubuntu/Debian
        log_info "使用apt-get安装Git..."
        if [ "$EUID" -eq 0 ]; then
            apt-get update
            apt-get install -y git
        else
            sudo apt-get update
            sudo apt-get install -y git
        fi
        
    elif command -v apk &> /dev/null; then
        # Alpine Linux
        log_info "使用apk安装Git..."
        if [ "$EUID" -eq 0 ]; then
            apk add --no-cache git
        else
            sudo apk add --no-cache git
        fi
        
    elif command -v zypper &> /dev/null; then
        # openSUSE
        log_info "使用zypper安装Git..."
        if [ "$EUID" -eq 0 ]; then
            zypper install -y git
        else
            sudo zypper install -y git
        fi
        
    else
        log_error "未识别的包管理器"
        log_error "请手动安装Git:"
        log_error "  CentOS/RHEL: yum install git"
        log_error "  Ubuntu/Debian: apt-get install git"
        log_error "  Alpine: apk add git"
        exit 1
    fi
    
    log_info "Git安装完成"
}

# 配置Git全局设置
configure_git() {
    log_info "配置Git全局设置..."
    
    # 检查是否已有全局配置
    local git_user=$(git config --global user.name 2>/dev/null || echo "")
    local git_email=$(git config --global user.email 2>/dev/null || echo "")
    
    if [ -z "$git_user" ]; then
        log_info "设置Git用户名..."
        git config --global user.name "Jenkins"
    else
        log_info "Git用户名已设置: $git_user"
    fi
    
    if [ -z "$git_email" ]; then
        log_info "设置Git邮箱..."
        git config --global user.email "jenkins@localhost"
    else
        log_info "Git邮箱已设置: $git_email"
    fi
    
    # 设置其他有用的配置
    git config --global init.defaultBranch main
    git config --global pull.rebase false
    git config --global core.autocrlf input
    
    log_info "Git配置完成"
}

# 配置Jenkins Git工具
configure_jenkins_git() {
    log_info "配置Jenkins Git工具..."
    
    local jenkins_home="${JENKINS_HOME:-/data/jenkins/jenkins_home}"
    local git_path=$(which git)
    
    if [ ! -d "$jenkins_home" ]; then
        log_warn "Jenkins目录不存在: $jenkins_home"
        return 1
    fi
    
    # 创建Jenkins Git工具配置
    local tools_dir="$jenkins_home/tools"
    mkdir -p "$tools_dir"
    
    # 创建Git工具配置文件
    cat > "$jenkins_home/hudson.plugins.git.GitTool.xml" << EOF
<?xml version='1.1' encoding='UTF-8'?>
<hudson.plugins.git.GitTool_-DescriptorImpl plugin="git@4.8.3">
  <installations>
    <hudson.plugins.git.GitTool>
      <name>Default</name>
      <home>$git_path</home>
      <properties/>
    </hudson.plugins.git.GitTool>
  </installations>
</hudson.plugins.git.GitTool_-DescriptorImpl>
EOF
    
    log_info "Jenkins Git工具配置已创建"
    log_info "Git路径: $git_path"
}

# 测试Git功能
test_git_functionality() {
    log_info "测试Git功能..."
    
    # 创建临时目录进行测试
    local test_dir="/tmp/git_test_$$"
    mkdir -p "$test_dir"
    cd "$test_dir"
    
    # 测试git init
    if git init . &>/dev/null; then
        log_info "✓ git init 测试成功"
    else
        log_error "✗ git init 测试失败"
        return 1
    fi
    
    # 测试git config
    if git config user.name "Test" && git config user.email "<EMAIL>"; then
        log_info "✓ git config 测试成功"
    else
        log_error "✗ git config 测试失败"
        return 1
    fi
    
    # 测试git add和commit
    echo "test" > test.txt
    if git add test.txt && git commit -m "test commit" &>/dev/null; then
        log_info "✓ git add/commit 测试成功"
    else
        log_error "✗ git add/commit 测试失败"
        return 1
    fi
    
    # 清理测试目录
    cd /
    rm -rf "$test_dir"
    
    log_info "Git功能测试完成"
}

# 检查SSH密钥配置
check_ssh_keys() {
    log_info "检查SSH密钥配置..."
    
    local jenkins_home="${JENKINS_HOME:-/data/jenkins/jenkins_home}"
    local ssh_dir="$jenkins_home/.ssh"
    
    if [ -f "$ssh_dir/id_rsa" ] || [ -f "$ssh_dir/id_ed25519" ]; then
        log_info "✓ 找到SSH私钥"
        
        # 检查SSH密钥权限
        if [ -f "$ssh_dir/id_rsa" ]; then
            chmod 600 "$ssh_dir/id_rsa"
            log_info "已设置id_rsa权限为600"
        fi
        
        if [ -f "$ssh_dir/id_ed25519" ]; then
            chmod 600 "$ssh_dir/id_ed25519"
            log_info "已设置id_ed25519权限为600"
        fi
        
        # 检查known_hosts
        if [ ! -f "$ssh_dir/known_hosts" ]; then
            log_warn "known_hosts文件不存在，创建并添加GitHub主机密钥"
            mkdir -p "$ssh_dir"
            ssh-keyscan -H github.com >> "$ssh_dir/known_hosts" 2>/dev/null
            chmod 644 "$ssh_dir/known_hosts"
        fi
        
    else
        log_warn "未找到SSH私钥"
        log_warn "如需克隆私有仓库，请配置SSH密钥或使用HTTPS认证"
    fi
}

# 重启Jenkins服务
restart_jenkins() {
    log_info "重启Jenkins服务以应用配置..."
    
    # 查找Jenkins进程
    local jenkins_pid=$(pgrep -f "jenkins.war" || true)
    
    if [ -n "$jenkins_pid" ]; then
        log_info "停止Jenkins进程 (PID: $jenkins_pid)"
        kill -TERM "$jenkins_pid"
        
        # 等待进程停止
        local count=0
        while [ $count -lt 30 ] && kill -0 "$jenkins_pid" 2>/dev/null; do
            sleep 1
            ((count++))
        done
        
        if kill -0 "$jenkins_pid" 2>/dev/null; then
            log_warn "强制停止Jenkins进程"
            kill -KILL "$jenkins_pid"
        fi
    fi
    
    log_info "请手动重启Jenkins服务"
    log_info "运行命令: ./jenkins_start.sh"
}

# 显示解决方案总结
show_solution_summary() {
    log_info "Git问题修复完成！"
    echo ""
    echo "=== 修复总结 ==="
    echo "1. ✓ Git已安装并配置"
    echo "2. ✓ Jenkins Git工具已配置"
    echo "3. ✓ Git功能测试通过"
    echo "4. ✓ SSH密钥权限已检查"
    echo ""
    echo "=== 下一步操作 ==="
    echo "1. 重启Jenkins服务: ./jenkins_start.sh"
    echo "2. 在Jenkins中配置Git凭据（如需要）"
    echo "3. 重新运行失败的Pipeline"
    echo ""
    echo "=== Git信息 ==="
    echo "Git版本: $(git --version)"
    echo "Git路径: $(which git)"
    echo "用户配置: $(git config --global user.name) <$(git config --global user.email)>"
}

# 主函数
main() {
    log_info "开始修复Jenkins Git问题..."
    
    detect_os
    
    if ! check_git_status; then
        install_git
    fi
    
    configure_git
    configure_jenkins_git
    test_git_functionality
    check_ssh_keys
    
    show_solution_summary
    
    # 询问是否重启Jenkins
    read -p "是否现在重启Jenkins服务? (y/N): " restart_confirm
    if [[ "$restart_confirm" =~ ^[Yy]$ ]]; then
        restart_jenkins
    fi
}

# 显示帮助信息
show_help() {
    echo "Jenkins Git问题修复脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  --no-restart        不询问重启Jenkins"
    echo ""
    echo "功能:"
    echo "  1. 检查并安装Git"
    echo "  2. 配置Git全局设置"
    echo "  3. 配置Jenkins Git工具"
    echo "  4. 测试Git功能"
    echo "  5. 检查SSH密钥配置"
}

# 处理命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    --no-restart)
        main
        ;;
    "")
        main
        ;;
    *)
        echo "未知选项: $1"
        echo "使用 -h 或 --help 查看帮助信息"
        exit 1
        ;;
esac
