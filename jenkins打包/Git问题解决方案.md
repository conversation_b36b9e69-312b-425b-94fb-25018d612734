# Jenkins Git问题解决方案

## 问题描述

Jenkins Pipeline执行时出现以下错误：
```
Cannot run program "git": error=2, No such file or directory
```

这表明Jenkins系统中没有安装Git或Git不在PATH中。

## 🚀 快速解决方案

### 方法一：使用一键修复脚本（推荐）

```bash
# 1. 修复Git安装问题
sudo ./fix_git_issue.sh

# 2. 配置Git凭据
./setup_git_credentials.sh -i

# 3. 重启Jenkins
./jenkins_manager.sh restart
```

### 方法二：使用管理脚本

```bash
# 修复Git问题
./jenkins_manager.sh git

# 配置Git凭据
./jenkins_manager.sh credentials -i

# 重启Jenkins
./jenkins_manager.sh restart
```

## 📋 详细解决步骤

### 1. 安装Git

#### CentOS/RHEL/Fedora:
```bash
sudo yum install -y git
# 或者
sudo dnf install -y git
```

#### Ubuntu/Debian:
```bash
sudo apt-get update
sudo apt-get install -y git
```

#### Alpine Linux:
```bash
sudo apk add --no-cache git
```

### 2. 验证Git安装

```bash
# 检查Git版本
git --version

# 检查Git路径
which git

# 配置Git用户信息
git config --global user.name "Jenkins"
git config --global user.email "jenkins@localhost"
```

### 3. 配置Jenkins Git工具

创建Jenkins Git工具配置：
```bash
# 创建Git工具配置文件
cat > /data/jenkins/jenkins_home/hudson.plugins.git.GitTool.xml << EOF
<?xml version='1.1' encoding='UTF-8'?>
<hudson.plugins.git.GitTool_-DescriptorImpl plugin="git@4.8.3">
  <installations>
    <hudson.plugins.git.GitTool>
      <name>Default</name>
      <home>/usr/bin/git</home>
      <properties/>
    </hudson.plugins.git.GitTool>
  </installations>
</hudson.plugins.git.GitTool_-DescriptorImpl>
EOF
```

### 4. 配置GitHub访问凭据

#### 选项A：使用Personal Access Token（推荐）

1. 访问 https://github.com/settings/tokens
2. 创建新的Token，权限选择：`repo`, `workflow`, `read:org`
3. 更新Jenkinsfile使用HTTPS：

```bash
# 备份原文件
cp Jenkinsfile_ Jenkinsfile_.backup

# 替换SSH为HTTPS（将YOUR_TOKEN替换为实际Token）
sed -i 's|**************:WhaleOps|https://<EMAIL>/WhaleOps|g' Jenkinsfile_
```

#### 选项B：使用SSH密钥

1. 生成SSH密钥：
```bash
ssh-keygen -t rsa -b 4096 -C "jenkins@localhost" -f /data/jenkins/jenkins_home/.ssh/id_rsa -N ""
```

2. 添加公钥到GitHub：
```bash
cat /data/jenkins/jenkins_home/.ssh/id_rsa.pub
```

3. 添加GitHub到known_hosts：
```bash
ssh-keyscan -H github.com >> /data/jenkins/jenkins_home/.ssh/known_hosts
```

### 5. 测试Git连接

#### 测试HTTPS连接：
```bash
curl -H "Authorization: token YOUR_TOKEN" https://api.github.com/user
```

#### 测试SSH连接：
```bash
ssh -T **************
```

### 6. 重启Jenkins服务

```bash
./jenkins_manager.sh restart
```

## 🔧 故障排除

### 问题1：Git命令找不到

**解决方案：**
```bash
# 检查Git是否安装
which git

# 如果未安装，安装Git
sudo yum install -y git  # CentOS/RHEL
sudo apt-get install -y git  # Ubuntu/Debian

# 检查PATH环境变量
echo $PATH

# 如果需要，添加Git到PATH
export PATH="/usr/bin:$PATH"
```

### 问题2：权限被拒绝

**解决方案：**
```bash
# 检查SSH密钥权限
chmod 600 /data/jenkins/jenkins_home/.ssh/id_rsa
chmod 644 /data/jenkins/jenkins_home/.ssh/id_rsa.pub

# 检查Jenkins用户权限
chown -R jenkins:jenkins /data/jenkins/jenkins_home/.ssh/
```

### 问题3：GitHub认证失败

**解决方案：**
```bash
# 检查Token权限
curl -H "Authorization: token YOUR_TOKEN" https://api.github.com/user

# 检查SSH密钥是否添加到GitHub
ssh -T **************

# 检查known_hosts
ssh-keyscan -H github.com >> ~/.ssh/known_hosts
```

### 问题4：Jenkinsfile语法错误

**解决方案：**
检查Jenkinsfile中的Git URL格式：

```groovy
// SSH格式
git branch: "main", url: "**************:WhaleOps/repo.git"

// HTTPS格式
git branch: "main", url: "https://<EMAIL>/WhaleOps/repo.git"

// 或使用sh命令
sh "git clone -b main https://<EMAIL>/WhaleOps/repo.git"
```

## 📝 验证清单

完成以下检查确保问题已解决：

- [ ] Git已安装并可执行：`git --version`
- [ ] Git路径正确：`which git`
- [ ] Git全局配置已设置：`git config --global --list`
- [ ] Jenkins Git工具已配置
- [ ] GitHub凭据已配置（Token或SSH密钥）
- [ ] GitHub连接测试成功
- [ ] Jenkinsfile已更新使用正确的认证方式
- [ ] Jenkins服务已重启
- [ ] Pipeline可以成功克隆代码

## 🎯 最佳实践

1. **使用HTTPS + Token**：比SSH更简单，更容易管理
2. **定期更新Token**：设置Token过期时间，定期更新
3. **最小权限原则**：只给Token必要的权限
4. **备份配置**：修改前备份Jenkinsfile和配置文件
5. **监控日志**：定期检查Jenkins日志，及时发现问题

## 📞 获取帮助

如果问题仍未解决，请提供以下信息：

1. 操作系统版本：`cat /etc/os-release`
2. Git版本：`git --version`
3. Jenkins版本和日志
4. 完整的错误信息
5. Jenkinsfile内容（去除敏感信息）

## 🔗 相关脚本

- `fix_git_issue.sh` - 一键修复Git安装问题
- `setup_git_credentials.sh` - 配置Git凭据
- `jenkins_manager.sh` - Jenkins管理工具
- `jenkins_start.sh` - Jenkins启动脚本
