#!/bin/bash

set -e
trap 'echo "Error occurred at line $LINENO. Exiting."; exit 1' ERR

# 检查是否启用设置向导 (true=启用向导和登录, false=跳过向导)
ENABLE_SETUP_WIZARD="${ENABLE_SETUP_WIZARD:-true}"

export JAVA_HOME="/usr/local/jdk-21.0.8"
export PATH="$JAVA_HOME/bin:$PATH"

# 代理配置
export http_proxy="http://172.16.3.74:7890"
export https_proxy="http://172.16.3.74:7890"

# Jenkins 配置
export JENKINS_HOME="/data/jenkins/jenkins_home"

# 增强的JVM参数 - 解决AWT字体问题
export JAVA_OPTS="-server \
-Xms2g -Xmx4g \
-XX:+UseG1GC \
-XX:+DisableExplicitGC \
-XX:+UseCompressedOops \
-XX:+UseCompressedClassPointers \
-Dfile.encoding=UTF-8 \
-Dsun.jnu.encoding=UTF-8 \
-Djava.awt.headless=true \
-Djava.awt.GraphicsEnvironment=sun.awt.HeadlessGraphicsEnvironment \
-Dprism.order=sw \
-Dprism.text=t2k \
-Djava.awt.fonts.useSystemFonts=false \
-Dsun.java2d.fontpath=/usr/share/fonts \
-Dhudson.DNSMultiCast.disabled=true \
-Dhudson.udp=-1 \
-Djenkins.install.runSetupWizard=${ENABLE_SETUP_WIZARD} \
-Dpermissive-script-security.enabled=true \
-Dhudson.security.csrf.GlobalCrumbIssuerConfiguration.DISABLE_CSRF_PROTECTION=true \
-Dorg.apache.commons.jelly.tags.fmt.timeZone=Asia/Shanghai \
-Dhudson.model.UpdateCenter.never=true \
-Djava.io.tmpdir=${JENKINS_HOME}/tmp"

# Jenkins 系统优化参数 - 修正端口配置
export JENKINS_OPTS="--httpPort=80 \
--httpListenAddress=0.0.0.0"

# 配置文件路径
LOG_FILE="/var/log/jenkins_start.log"
PID_FILE="/var/run/jenkins.pid"
JENKINS_WAR="/data/jenkins/jenkins.war"
JENKINS_LOG_DIR="$JENKINS_HOME/logs"

# 函数：记录日志
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S'): $1" | tee -a "$LOG_FILE"
}

# 函数：检查端口占用（增强版）
check_port_usage() {
    log_message "检查端口80占用情况..."
    
    # 检查端口占用
    if netstat -tln 2>/dev/null | grep -q ":80 " || ss -tln 2>/dev/null | grep -q ":80 "; then
        log_message "错误: 端口80已被占用"
        local pid=$(lsof -ti:80 2>/dev/null || true)
        if [ -n "$pid" ]; then
            log_message "占用端口80的进程PID: $pid"
            ps -p $pid -o pid,ppid,cmd 2>/dev/null || true
            log_message "请先停止占用端口的进程，然后重新启动Jenkins"
        fi
        
        # 显示所有监听80的进程
        log_message "所有监听80端口的进程:"
        netstat -tlnp 2>/dev/null | grep ":80 " || true
        ss -tlnp 2>/dev/null | grep ":80 " || true
        
        exit 1
    fi
    
    log_message "端口80可用"
    return 0
}

# 函数：安装字体包
install_fonts() {
    log_message "检查并安装字体包..."

    # 检查是否为root用户或有sudo权限
    if [ "$EUID" -eq 0 ] || sudo -n true 2>/dev/null; then
        # 检测操作系统类型
        if command -v yum &> /dev/null; then
            # CentOS/RHEL/Fedora
            log_message "检测到yum包管理器，安装字体包..."
            if [ "$EUID" -eq 0 ]; then
                yum install -y fontconfig dejavu-sans-fonts dejavu-serif-fonts dejavu-sans-mono-fonts 2>/dev/null || {
                    log_message "警告: 字体包安装失败，但将继续启动"
                }
            else
                sudo yum install -y fontconfig dejavu-sans-fonts dejavu-serif-fonts dejavu-sans-mono-fonts 2>/dev/null || {
                    log_message "警告: 字体包安装失败，但将继续启动"
                }
            fi
        elif command -v apt-get &> /dev/null; then
            # Ubuntu/Debian
            log_message "检测到apt包管理器，安装字体包..."
            if [ "$EUID" -eq 0 ]; then
                apt-get update && apt-get install -y fontconfig fonts-dejavu-core fonts-dejavu-extra 2>/dev/null || {
                    log_message "警告: 字体包安装失败，但将继续启动"
                }
            else
                sudo apt-get update && sudo apt-get install -y fontconfig fonts-dejavu-core fonts-dejavu-extra 2>/dev/null || {
                    log_message "警告: 字体包安装失败，但将继续启动"
                }
            fi
        elif command -v apk &> /dev/null; then
            # Alpine Linux
            log_message "检测到apk包管理器，安装字体包..."
            if [ "$EUID" -eq 0 ]; then
                apk add --no-cache fontconfig ttf-dejavu 2>/dev/null || {
                    log_message "警告: 字体包安装失败，但将继续启动"
                }
            else
                sudo apk add --no-cache fontconfig ttf-dejavu 2>/dev/null || {
                    log_message "警告: 字体包安装失败，但将继续启动"
                }
            fi
        else
            log_message "警告: 未识别的包管理器，跳过字体包安装"
        fi

        # 刷新字体缓存
        if command -v fc-cache &> /dev/null; then
            log_message "刷新字体缓存..."
            fc-cache -fv 2>/dev/null || true
        fi
    else
        log_message "警告: 没有管理员权限，跳过字体包安装"
    fi

    # 检查字体配置
    if command -v fc-list &> /dev/null; then
        FONT_COUNT=$(fc-list | wc -l)
        log_message "系统字体数量: $FONT_COUNT"
        if [ "$FONT_COUNT" -eq 0 ]; then
            log_message "警告: 系统中没有检测到字体，可能会出现AWT问题"
        fi
    fi
}

# 函数：检查必要文件
check_prerequisites() {
    log_message "检查必要文件和目录..."

    # 安装字体包
    install_fonts

    # 检查Jenkins WAR文件
    if [ ! -f "$JENKINS_WAR" ]; then
        log_message "错误: Jenkins WAR文件不存在: $JENKINS_WAR"
        exit 1
    fi

    # 检查Java环境
    if ! command -v java &> /dev/null; then
        log_message "错误: Java未安装或不在PATH中"
        exit 1
    fi

    # 显示Java版本
    JAVA_VERSION=$(java -version 2>&1 | head -n 1)
    log_message "Java版本: $JAVA_VERSION"

    # 检查端口
    check_port_usage
}

# 函数：检查Jenkins是否已运行
check_jenkins_running() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            log_message "Jenkins 已在运行 (PID: $PID)"
            exit 1
        else
            log_message "清理过期的PID文件"
            rm -f "$PID_FILE"
        fi
    fi
}

# 函数：创建必要目录
create_directories() {
    log_message "创建必要目录..."
    
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    mkdir -p "$JENKINS_LOG_DIR"
    mkdir -p "$JENKINS_HOME"
    mkdir -p "$JENKINS_HOME/tmp"
    
    # 设置权限
    chmod 755 "$JENKINS_HOME"
    chmod 755 "$JENKINS_HOME/tmp"
    
    if id jenkins &>/dev/null; then
        chown -R jenkins:jenkins "$JENKINS_HOME" 2>/dev/null || {
            log_message "警告: 无法设置jenkins用户权限，将以当前用户运行"
        }
    fi
}

# 函数：创建Jenkins日志配置
create_logging_config() {
    log_message "创建Jenkins日志配置..."
    
    cat > "$JENKINS_HOME/logging.properties" << EOF
handlers= java.util.logging.ConsoleHandler, java.util.logging.FileHandler
.level= INFO

# Console Handler
java.util.logging.ConsoleHandler.level=INFO
java.util.logging.ConsoleHandler.formatter=java.util.logging.SimpleFormatter

# File Handler
java.util.logging.FileHandler.pattern=$JENKINS_HOME/logs/jenkins-%g.log
java.util.logging.FileHandler.limit=10485760
java.util.logging.FileHandler.count=10
java.util.logging.FileHandler.formatter=java.util.logging.SimpleFormatter

# Formatter
java.util.logging.SimpleFormatter.format=%1\$tY-%1\$tm-%1\$td %1\$tH:%1\$tM:%1\$tS %4\$s %2\$s %5\$s%6\$s%n

# 减少日志输出
hudson.level=WARNING
jenkins.level=WARNING
org.eclipse.jetty.level=WARNING
winstone.level=WARNING
hudson.ExpressionFactory2.level=SEVERE
EOF
}

# 函数：优化系统参数
optimize_system() {
    log_message "优化系统参数..."
    
    # 设置文件描述符限制
    ulimit -n 65536
    ulimit -u 32768
    
    # 显示当前限制
    log_message "文件描述符限制: $(ulimit -n)"
    log_message "进程数限制: $(ulimit -u)"
}

# 函数：清理Jenkins环境
cleanup_jenkins_environment() {
    log_message "清理Jenkins环境..."
    
    # 清理可能损坏的war目录
    if [ -d "$JENKINS_HOME/war" ]; then
        log_message "清理war目录"
        rm -rf "$JENKINS_HOME/war"
    fi
    
    # 清理锁文件
    find "$JENKINS_HOME" -name "*.lock" -type f -delete 2>/dev/null || true
    
    # 清理临时文件
    if [ -d "$JENKINS_HOME/tmp" ]; then
        rm -rf "$JENKINS_HOME/tmp"/*
    fi
    
    log_message "环境清理完成"
}

# 函数：启动Jenkins（增强诊断版）
start_jenkins() {
    log_message "启动 Jenkins..."
    log_message "JENKINS_HOME: $JENKINS_HOME"
    log_message "Jenkins WAR: $JENKINS_WAR"
    log_message "JVM 内存: 2G-4G"
    log_message "当前用户: $(whoami)"
    log_message "当前用户ID: $(id)"
    
    # 确保临时目录存在且权限正确
    mkdir -p "$JENKINS_HOME/tmp"
    chmod 755 "$JENKINS_HOME/tmp"
    
    # 测试端口绑定权限
    log_message "测试端口绑定权限..."
    if command -v nc &> /dev/null; then
        timeout 2 nc -l 80 2>/dev/null &
        local test_pid=$!
        sleep 1
        if kill -0 $test_pid 2>/dev/null; then
            kill $test_pid 2>/dev/null || true
            log_message "端口绑定测试成功"
        else
            log_message "警告: 端口绑定测试失败，可能存在权限问题"
        fi
    fi
    
    # 显示完整的启动命令
    log_message "完整启动命令:"
    log_message "java $JAVA_OPTS -jar $JENKINS_WAR $JENKINS_OPTS"
    
    # 启动Jenkins
    log_message "执行启动命令..."
    nohup java $JAVA_OPTS -jar "$JENKINS_WAR" $JENKINS_OPTS \
        > "$JENKINS_HOME/jenkins.log" 2>&1 &
    JENKINS_PID=$!
    
    log_message "Jenkins进程已启动 (PID: $JENKINS_PID)"
    
    # 等待进程启动
    sleep 8
    
    # 检查进程状态
    if ! ps -p $JENKINS_PID > /dev/null 2>&1; then
        log_message "错误: Jenkins进程启动失败"
        log_message "完整错误日志:"
        if [ -f "$JENKINS_HOME/jenkins.log" ]; then
            tail -50 "$JENKINS_HOME/jenkins.log" | while read line; do
                log_message "Jenkins日志: $line"
            done
        fi
        
        # 检查是否有其他Jenkins进程
        log_message "检查是否有其他Jenkins进程:"
        ps aux | grep jenkins | grep -v grep || true
        
        exit 1
    fi
    
    # 保存PID
    echo $JENKINS_PID > "$PID_FILE"
    log_message "Jenkins 已启动 (PID: $JENKINS_PID)"
}

# 函数：健康检查
health_check() {
    log_message "等待Jenkins启动..."
    
    local max_attempts=60
    local attempt=0
    local jenkins_url="http://127.0.0.1:80"
    
    while [ $attempt -lt $max_attempts ]; do
        # 检查进程状态
        if ! ps -p $JENKINS_PID > /dev/null 2>&1; then
            log_message "错误: Jenkins进程已退出"
            tail -20 "$JENKINS_HOME/jenkins.log" 2>/dev/null || true
            rm -f "$PID_FILE"
            exit 1
        fi
        
        # 检查端口监听
        if netstat -tln 2>/dev/null | grep -q ":80 " || ss -tln 2>/dev/null | grep -q ":80 "; then
            log_message "Jenkins 启动成功！"
            log_message "访问地址: http://172.16.3.74:80"
            return 0
        fi
        
        sleep 5
        ((attempt++))
        log_message "等待Jenkins启动... ($attempt/$max_attempts)"
    done
    
    log_message "Jenkins 启动超时"
    exit 1
}

# 主执行流程
main() {
    log_message "开始启动Jenkins服务"
    
    check_prerequisites
    check_jenkins_running
    cleanup_jenkins_environment
    create_directories
    create_logging_config
    optimize_system
    start_jenkins
    health_check
    
    log_message "Jenkins启动流程完成"
}

# 执行主函数
main "$@"
