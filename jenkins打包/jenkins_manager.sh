#!/bin/bash

# Jenkins管理脚本
# 集成Jenkins启动、停止、认证配置等功能

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
JENKINS_HOME="${JENKINS_HOME:-/data/jenkins/jenkins_home}"
PID_FILE="/var/run/jenkins.pid"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S'): $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S'): $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S'): $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S'): $1"
}

# 检查Jenkins状态
check_jenkins_status() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "running"
            return 0
        else
            rm -f "$PID_FILE"
            echo "stopped"
            return 1
        fi
    else
        echo "stopped"
        return 1
    fi
}

# 启动Jenkins
start_jenkins() {
    log_info "启动Jenkins服务..."
    
    local status=$(check_jenkins_status)
    if [ "$status" = "running" ]; then
        log_warn "Jenkins已在运行"
        return 0
    fi
    
    # 检查启动脚本
    if [ ! -f "$SCRIPT_DIR/jenkins_start.sh" ]; then
        log_error "启动脚本不存在: $SCRIPT_DIR/jenkins_start.sh"
        return 1
    fi
    
    # 执行启动脚本
    cd "$SCRIPT_DIR"
    ./jenkins_start.sh
}

# 停止Jenkins
stop_jenkins() {
    log_info "停止Jenkins服务..."
    
    local status=$(check_jenkins_status)
    if [ "$status" = "stopped" ]; then
        log_warn "Jenkins未运行"
        return 0
    fi
    
    local pid=$(cat "$PID_FILE")
    log_info "停止Jenkins进程 (PID: $pid)"
    
    # 优雅停止
    kill -TERM "$pid"
    
    # 等待进程停止
    local count=0
    while [ $count -lt 30 ] && kill -0 "$pid" 2>/dev/null; do
        sleep 1
        ((count++))
        echo -n "."
    done
    echo ""
    
    # 检查是否已停止
    if kill -0 "$pid" 2>/dev/null; then
        log_warn "优雅停止失败，强制停止进程"
        kill -KILL "$pid"
        sleep 2
    fi
    
    # 清理PID文件
    rm -f "$PID_FILE"
    log_info "Jenkins服务已停止"
}

# 重启Jenkins
restart_jenkins() {
    log_info "重启Jenkins服务..."
    stop_jenkins
    sleep 3
    start_jenkins
}

# 显示Jenkins状态
show_status() {
    local status=$(check_jenkins_status)
    
    echo "=== Jenkins服务状态 ==="
    if [ "$status" = "running" ]; then
        local pid=$(cat "$PID_FILE")
        echo -e "状态: ${GREEN}运行中${NC}"
        echo "PID: $pid"
        echo "访问地址: http://***********:8080"
        
        # 检查端口监听
        if netstat -tln 2>/dev/null | grep -q ":8080 " || ss -tln 2>/dev/null | grep -q ":8080 "; then
            echo -e "端口状态: ${GREEN}正常监听${NC}"
        else
            echo -e "端口状态: ${YELLOW}未监听${NC}"
        fi
        
        # 检查HTTP响应
        local http_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080 2>/dev/null || echo "000")
        if [ "$http_code" != "000" ]; then
            echo -e "HTTP状态: ${GREEN}$http_code${NC}"
        else
            echo -e "HTTP状态: ${RED}无响应${NC}"
        fi
    else
        echo -e "状态: ${RED}已停止${NC}"
    fi
    
    echo ""
    echo "=== 系统信息 ==="
    echo "Jenkins目录: $JENKINS_HOME"
    echo "Java版本: $(java -version 2>&1 | head -n 1 | cut -d'"' -f2)"
    echo "系统负载: $(uptime | awk -F'load average:' '{print $2}')"
    echo "内存使用: $(free -h | grep Mem | awk '{print $3"/"$2}')"
}

# 查看日志
show_logs() {
    local lines="${1:-50}"
    
    echo "=== Jenkins启动日志 (最近${lines}行) ==="
    if [ -f "$JENKINS_HOME/jenkins.log" ]; then
        tail -n "$lines" "$JENKINS_HOME/jenkins.log"
    else
        log_warn "日志文件不存在: $JENKINS_HOME/jenkins.log"
    fi
    
    echo ""
    echo "=== 系统日志 (最近${lines}行) ==="
    if [ -f "/var/log/jenkins_start.log" ]; then
        tail -n "$lines" "/var/log/jenkins_start.log"
    else
        log_warn "系统日志文件不存在: /var/log/jenkins_start.log"
    fi
}

# 配置认证
setup_auth() {
    log_info "配置Jenkins认证..."
    
    if [ ! -f "$SCRIPT_DIR/jenkins_auth_setup.sh" ]; then
        log_error "认证配置脚本不存在: $SCRIPT_DIR/jenkins_auth_setup.sh"
        return 1
    fi
    
    cd "$SCRIPT_DIR"
    ./jenkins_auth_setup.sh "$@"
}

# 安装字体
install_fonts() {
    log_info "安装字体包..."

    if [ ! -f "$SCRIPT_DIR/install_fonts.sh" ]; then
        log_error "字体安装脚本不存在: $SCRIPT_DIR/install_fonts.sh"
        return 1
    fi

    cd "$SCRIPT_DIR"
    sudo ./install_fonts.sh
}

# 修复Git问题
fix_git() {
    log_info "修复Git问题..."

    if [ ! -f "$SCRIPT_DIR/fix_git_issue.sh" ]; then
        log_error "Git修复脚本不存在: $SCRIPT_DIR/fix_git_issue.sh"
        return 1
    fi

    cd "$SCRIPT_DIR"
    sudo ./fix_git_issue.sh "$@"
}

# 配置Git凭据
setup_git_credentials() {
    log_info "配置Git凭据..."

    if [ ! -f "$SCRIPT_DIR/setup_git_credentials.sh" ]; then
        log_error "Git凭据配置脚本不存在: $SCRIPT_DIR/setup_git_credentials.sh"
        return 1
    fi

    cd "$SCRIPT_DIR"
    ./setup_git_credentials.sh "$@"
}

# 备份配置
backup_config() {
    local backup_dir="${1:-$JENKINS_HOME/backup/$(date +%Y%m%d_%H%M%S)}"
    
    log_info "备份Jenkins配置到: $backup_dir"
    
    mkdir -p "$backup_dir"
    
    # 备份主要配置文件
    if [ -f "$JENKINS_HOME/config.xml" ]; then
        cp "$JENKINS_HOME/config.xml" "$backup_dir/"
    fi
    
    # 备份用户配置
    if [ -d "$JENKINS_HOME/users" ]; then
        cp -r "$JENKINS_HOME/users" "$backup_dir/"
    fi
    
    # 备份作业配置
    if [ -d "$JENKINS_HOME/jobs" ]; then
        find "$JENKINS_HOME/jobs" -name "config.xml" -exec cp --parents {} "$backup_dir/" \;
    fi
    
    # 备份插件列表
    if [ -d "$JENKINS_HOME/plugins" ]; then
        ls "$JENKINS_HOME/plugins" | grep -E '\.jpi$|\.hpi$' > "$backup_dir/plugins.list"
    fi
    
    log_info "备份完成: $backup_dir"
}

# 显示帮助信息
show_help() {
    echo "Jenkins管理脚本"
    echo ""
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "命令:"
    echo "  start                启动Jenkins服务"
    echo "  stop                 停止Jenkins服务"
    echo "  restart              重启Jenkins服务"
    echo "  status               显示Jenkins状态"
    echo "  logs [行数]          查看日志 (默认50行)"
    echo "  auth [选项]          配置用户认证"
    echo "  fonts                安装字体包"
    echo "  git                  修复Git问题"
    echo "  credentials [选项]   配置Git凭据"
    echo "  backup [目录]        备份配置文件"
    echo "  help                 显示此帮助信息"
    echo ""
    echo "认证选项:"
    echo "  -i, --interactive    交互式配置"
    echo ""
    echo "示例:"
    echo "  $0 start             启动Jenkins"
    echo "  $0 status            查看状态"
    echo "  $0 logs 100          查看最近100行日志"
    echo "  $0 auth -i           交互式配置认证"
    echo "  $0 git               修复Git问题"
    echo "  $0 credentials -i    交互式配置Git凭据"
    echo "  $0 backup            备份配置"
}

# 主函数
main() {
    case "${1:-}" in
        start)
            start_jenkins
            ;;
        stop)
            stop_jenkins
            ;;
        restart)
            restart_jenkins
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "${2:-50}"
            ;;
        auth)
            shift
            setup_auth "$@"
            ;;
        fonts)
            install_fonts
            ;;
        git)
            shift
            fix_git "$@"
            ;;
        credentials)
            shift
            setup_git_credentials "$@"
            ;;
        backup)
            backup_config "$2"
            ;;
        help|--help|-h)
            show_help
            ;;
        "")
            echo "错误: 缺少命令参数"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
        *)
            echo "错误: 未知命令 '$1'"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
