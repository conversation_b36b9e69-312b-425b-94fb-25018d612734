#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_build_test.py
# @Time    : 2025/07/28 10:51
# <AUTHOR> chenyifei
# @Version : 1.0

import os
import sys
import argparse
import logging
import traceback
import hashlib
import threading
import queue
import re
import time
from datetime import datetime
from typing import Optional, Tuple, Dict, Any
from contextlib import contextmanager
import pymysql
from pymysql.cursors import DictCursor
import requests
import json
import git
import oss2
from oss2 import Auth, Bucket

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))

# 基于原始代码的配置
CUSTOMER_LIST = {
    "zhongxinjiantou": "中信建投", 
    "guangdalicai": "光大理财", 
    "renshou": "人寿", 
    "renbao": "人保"
}

# 数据库配置
DB_CONFIG = {
    'host': '***********',
    'port': 3306,
    'user': 'root',
    'password': 'QWer12#$',
    'database': 'jenkins_db',
    'charset': 'utf8mb4',
    'autocommit': True
}

# 飞书配置
FEISHU_WEBHOOK_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/053a9872-d18f-4d3d-a433-6c3fef797e4c"
DEFAULT_PUBLIC_DOWNLOAD_LINK = "https://whale-ops.oss-cn-wulanchabu.aliyuncs.com"
DEFAULT_INTRANET_DOWNLOAD_LINK = "https://whale-ops.oss-cn-wulanchabu-internal.aliyuncs.com"

# 飞书消息模板
send_confirmation_message_json = '''{{
    "msg_type": "interactive",
    "card": {{
        "elements": [
            {{
                "tag": "div",
                "text": {{
                    "content": "🚀 **{build_message}**\\n\\n📋 **构建信息**:\\n- 分支: {branch}\\n\\n🔗 [查看Jenkins构建]({jenkins_url})",
                    "tag": "lark_md"
                }}
            }}
        ],
        "header": {{
            "title": {{
                "content": "WhaleStudio 构建通知",
                "tag": "plain_text"
            }},
            "template": "blue"
        }}
    }}
}}'''

end_message_json = '''{{
    "msg_type": "interactive",
    "card": {{
        "elements": [
            {{
                "tag": "div",
                "text": {{
                    "content": "{content}",
                    "tag": "lark_md"
                }}
            }}
        ],
        "header": {{
            "title": {{
                "content": "WhaleStudio 构建结果",
                "tag": "plain_text"
            }},
            "template": "{color}"
        }}
    }}
}}'''

webhook_url = FEISHU_WEBHOOK_URL

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(CURRENT_DIRECTORY, "whaleStudio_build.log"), encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def connect_to_db():
    """数据库连接"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        return conn, cursor
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise

def calculate_file_md5(file_path: str, chunk_size: int = 8192) -> str:
    """计算文件MD5值"""
    md5_hash = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(chunk_size), b""):
                md5_hash.update(chunk)
        return md5_hash.hexdigest()
    except Exception as e:
        logger.error(f"计算MD5失败: {e}")
        raise

def get_file_md5_async(file_path: str, result_queue: queue.Queue) -> None:
    """异步获取文件的md5"""
    logger.info(f"Get the package MD5 asynchronously, start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    try:
        md5_value = calculate_file_md5(file_path)
        result_queue.put(md5_value)
    except Exception as e:
        logger.error(f"get package md5 error: {e}")
        result_queue.put(None)
    finally:
        logger.info(f"Get the package MD5 asynchronously, end time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

class BuildWhaleStudio:
    """WhaleStudio构建类 - 基于原始代码重构"""

    def __init__(self, operation: str, branch: Optional[str] = None,
                 customer: Optional[str] = None, parser: Optional[argparse.ArgumentParser] = None,
                 file_path: Optional[str] = None, jenkins_url: Optional[str] = None,
                 build_status: Optional[str] = None, latest_version: Optional[str] = None,
                 commit_id: Optional[str] = None, build_path: Optional[str] = None):
        self.operation = operation
        self.branch = branch
        self.customer = customer if customer != "None" else None
        self.parser = parser
        self.file_path = file_path
        self.jenkins_url = jenkins_url
        self.build_status = build_status
        self.latest_version = latest_version
        self.commit_id = commit_id
        self.build_path = build_path

        # 解析commit_id
        if not commit_id:
            logger.warning("The commit id is not provided.")
            self.scheduler_id = self.scheduler_ui_id = self.tunnel_id = self.tunnel_web_id = "unknown"
        else:
            try:
                commit_parts = commit_id.split(",")
                if len(commit_parts) >= 4:
                    self.scheduler_id, self.scheduler_ui_id, self.tunnel_id, self.tunnel_web_id = commit_parts[:4]
                else:
                    logger.warning(f"commit_id格式不完整: {commit_id}")
                    self.scheduler_id = self.scheduler_ui_id = self.tunnel_id = self.tunnel_web_id = "unknown"
            except Exception as e:
                logger.error(f"解析commit_id失败: {e}")
                self.scheduler_id = self.scheduler_ui_id = self.tunnel_id = self.tunnel_web_id = "unknown"

        # 从Jenkins URL中提取构建次数
        self.build_number = self.extract_build_number_from_url()

        logger.info(f"初始化BuildWhaleStudio: operation={operation}, branch={branch}, build_number={self.build_number}")

    def extract_build_number_from_url(self) -> Optional[str]:
        """从Jenkins URL中提取构建次数"""
        if not self.jenkins_url:
            logger.warning("Jenkins URL未提供")
            return None

        try:
            # Jenkins URL格式: http://***********:8080/blue/organizations/jenkins/WhaleStudio_2.6-test/detail/WhaleStudio_2.6-test/{BUILD_ID}/pipeline
            # 使用正则表达式提取BUILD_ID
            pattern = r'/detail/[^/]+/(\d+)/'
            match = re.search(pattern, self.jenkins_url)

            if match:
                build_number = match.group(1)
                logger.info(f"从URL中提取到构建次数: {build_number}")
                return build_number
            else:
                logger.warning(f"无法从URL中提取构建次数: {self.jenkins_url}")
                return None

        except Exception as e:
            logger.error(f"提取构建次数失败: {e}")
            return None

    def build(self) -> None:
        """构建入口方法"""
        operation_map = {
            'send_confirmation_message': self.send_confirmation_message,
            'send_message': self.send_message,
            'send_end_message': self.send_end_message,
            'get_version': self.get_version,
            'update_commit_id': self.update_commit_id,
            'upload_to_oss': self.upload_to_oss,
            'delete_version': self.delete_version_record
        }

        if self.operation not in operation_map:
            logger.error(f"不支持的操作: {self.operation}")
            if self.parser:
                self.parser.print_help()
            raise ValueError(f"不支持的操作: {self.operation}")

        try:
            operation_map[self.operation]()
        except Exception as e:
            logger.error(f"执行操作 {self.operation} 失败: {e}")
            logger.error(traceback.format_exc())
            raise

    def send_confirmation_message(self):
        """发送确认消息"""
        try:
            logger.info(f"Send confirmation message, branch: {self.branch}, jenkins_url: {self.jenkins_url}")
            
            # 构建消息内容，包含构建次数
            build_message = "构建确认"
            if self.build_number:
                build_message += f" (第{self.build_number}次构建)"

            message_content = send_confirmation_message_json.format(
                jenkins_url=self.jenkins_url,
                build_message=build_message,
                branch=self.branch
            )
            
            response = requests.post(
                webhook_url,
                data=message_content,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info("Confirmation message sent successfully")
            else:
                logger.error(f"Failed to send confirmation message: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Send confirmation message error: {e}")
            logger.error(traceback.format_exc())

    def send_message(self):
        """发送消息"""
        try:
            logger.info(f"Send message, build_status: {self.build_status}, jenkins_url: {self.jenkins_url}")
            
            status_messages = {
                'success': '✅ 构建成功',
                'error': '❌ 构建失败', 
                'cancel': '⚠️ 构建取消',
                'timeout': '⏰ 构建超时'
            }
            
            content = status_messages.get(self.build_status, f"📢 构建状态: {self.build_status}")
            content += f"\\n🔗 [Jenkins链接]({self.jenkins_url})"
            
            color = "green" if self.build_status == 'success' else "red"
            
            message_content = end_message_json.format(
                content=content,
                color=color
            )
            
            response = requests.post(
                webhook_url,
                data=message_content,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"Message sent successfully: {self.build_status}")
            else:
                logger.error(f"Failed to send message: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Send message error: {e}")
            logger.error(traceback.format_exc())

    def send_end_message(self):
        """发送结束消息"""
        try:
            logger.info(f"Send end message, build_status: {self.build_status}")

            # 处理版本号为unknown的情况
            version_display = self.latest_version if self.latest_version and self.latest_version != 'unknown' else '未知'

            if self.build_status == "success":
                # 获取下载链接和MD5
                public_download_link, intranet_download_link, package_md5 = self.get_download_link_and_md5()

                content = f"**🎉 构建成功完成！**\\n\\n"
                content += f"📦 **版本号**: {version_display}\\n"
                content += f"🌿 **分支**: {self.branch}\\n"

                # 构建完成时间
                build_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                content += f"⏰ **构建完成时间**: {build_end_time}\\n"

                # 添加构建次数信息
                if self.build_number:
                    content += f"🔢 **构建次数**: 第{self.build_number}次\\n"

                if self.customer:
                    customer_name = CUSTOMER_LIST.get(self.customer.replace('-P', ''), self.customer)
                    content += f"👤 **客户**: {customer_name}\\n"

                content += f"📝 **提交ID**:\\n"
                content += f"  - WhalScheduler: {self.scheduler_id}\\n"
                content += f"  - WhalScheduler-UI: {self.scheduler_ui_id}\\n"
                content += f"  - WhaleTunnel: {self.tunnel_id}\\n"
                content += f"  - WhaleTunnel-Web: {self.tunnel_web_id}\\n"

                # 添加MD5和下载链接信息
                if package_md5:
                    content += f"🔐 **安装包MD5**: {package_md5}\\n"

                if intranet_download_link and intranet_download_link != "暂无":
                    content += f"🔗 **内网下载链接**: {intranet_download_link}\\n"

                if public_download_link and public_download_link != "暂无":
                    content += f"🌐 **外网下载链接**: {public_download_link}\\n"

                if self.build_path:
                    content += f"📁 **构建路径**: {self.build_path}\\n"

                content += f"🔗 **Jenkins链接**: {self.jenkins_url}\\n"

                # 添加版本变更记录
                content += "\\n📋 ---------- 版本变更记录 -----------"
                scheduler_commit_message, scheduler_ui_commit_message, tunnel_commit_message, tunnel_web_commit_message = self.get_commit_message()

                # WhaleScheduler 变更记录
                if scheduler_commit_message:
                    content += "\\n**🐋 WhaleScheduler 基于上一次版本的变更记录:**"
                    for commit_id, commit_message in scheduler_commit_message.items():
                        content += f"\\n  📝 {commit_id}:\\n    {commit_message}"
                else:
                    content += "\\n**🐋 WhaleScheduler 无基于上一次版本的变更记录**"
                    scheduler_current_commit_message = self.get_current_commit_message("whalescheduler")
                    if scheduler_current_commit_message:
                        content += "\\n**✨ WhaleScheduler 本次提交内容:**"
                        for message in scheduler_current_commit_message:
                            content += f"\\n  • {message}"
                        content += "\\n  ━━━━━━━━━━━━━━━━━━━━━━━━━━━"
                content += "\\n"

                # WhaleScheduler UI 变更记录
                if scheduler_ui_commit_message:
                    content += "\\n**🎨 WhaleScheduler UI 基于上一次版本的变更记录:**"
                    for commit_id, commit_message in scheduler_ui_commit_message.items():
                        content += f"\\n  📝 {commit_id}:\\n    {commit_message}"
                else:
                    content += "\\n**🎨 WhaleScheduler UI 无基于上一次版本的变更记录**"
                    scheduler_ui_current_commit_message = self.get_current_commit_message("whalescheduler-ui")
                    if scheduler_ui_current_commit_message:
                        content += "\\n**✨ WhaleScheduler UI 本次提交内容:**"
                        for message in scheduler_ui_current_commit_message:
                            content += f"\\n  • {message}"
                        content += "\\n  ━━━━━━━━━━━━━━━━━━━━━━━━━━━"
                content += "\\n"

                # WhaleTunnel 变更记录
                if tunnel_commit_message:
                    content += "\\n**🚇 WhaleTunnel 基于上一次版本的变更记录:**"
                    for commit_id, commit_message in tunnel_commit_message.items():
                        content += f"\\n  📝 {commit_id}:\\n    {commit_message}"
                else:
                    content += "\\n**🚇 WhaleTunnel 无基于上一次版本的变更记录**"
                    tunnel_current_commit_message = self.get_current_commit_message("whaletunnel")
                    if tunnel_current_commit_message:
                        content += "\\n**✨ WhaleTunnel 本次提交内容:**"
                        for message in tunnel_current_commit_message:
                            content += f"\\n  • {message}"
                        content += "\\n  ━━━━━━━━━━━━━━━━━━━━━━━━━━━"
                content += "\\n"

                # WhaleTunnel Web 变更记录
                if tunnel_web_commit_message:
                    content += "\\n**🌐 WhaleTunnel Web 基于上一次版本的变更记录:**"
                    for commit_id, commit_message in tunnel_web_commit_message.items():
                        content += f"\\n  📝 {commit_id}:\\n    {commit_message}"
                else:
                    content += "\\n**🌐 WhaleTunnel Web 无基于上一次版本的变更记录**"
                    tunnel_web_current_commit_message = self.get_current_commit_message("whaletunnel-web")
                    if tunnel_web_current_commit_message:
                        content += "\\n**✨ WhaleTunnel Web 本次提交内容:**"
                        for message in tunnel_web_current_commit_message:
                            content += f"\\n  • {message}"
                        content += "\\n  ━━━━━━━━━━━━━━━━━━━━━━━━━━━"

                color = "green"
                
            elif self.build_status == "error":
                content = f"**❌ 构建失败**\\n\\n"
                content += f"📦 **版本号**: {version_display}\\n"
                content += f"🌿 **分支**: {self.branch}\\n"

                # 添加构建次数信息
                if self.build_number:
                    content += f"🔢 **构建次数**: 第{self.build_number}次\\n"

                content += f"💡 请检查构建日志排查问题\\n"
                content += f"🔗 [Jenkins链接]({self.jenkins_url})"
                color = "red"

            elif self.build_status == "cancel":
                content = f"**⚠️ 构建已取消**\\n\\n"
                content += f"📦 **版本号**: {version_display}\\n"
                content += f"🌿 **分支**: {self.branch}\\n"

                # 添加构建次数信息
                if self.build_number:
                    content += f"🔢 **构建次数**: 第{self.build_number}次\\n"

                content += f"🔗 [Jenkins链接]({self.jenkins_url})"
                color = "orange"

            else:
                content = f"**📢 构建状态**: {self.build_status}\\n\\n"
                content += f"📦 **版本号**: {version_display}\\n"
                content += f"🌿 **分支**: {self.branch}\\n"

                # 添加构建次数信息
                if self.build_number:
                    content += f"🔢 **构建次数**: 第{self.build_number}次\\n"

                content += f"🔗 [Jenkins链接]({self.jenkins_url})"
                color = "blue"
            
            message_content = end_message_json.format(
                content=content,
                color=color
            )
            
            response = requests.post(
                webhook_url,
                data=message_content,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"End message sent successfully: {self.build_status}")
            else:
                logger.error(f"Failed to send end message: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Send end message error: {e}")
            logger.error(traceback.format_exc())

    def get_version(self) -> None:
        """获取版本号 - 基于原始逻辑"""
        logger.info(f"获取 {self.branch} 版本号")
        
        try:
            conn, cursor = connect_to_db()
            
            if self.customer:
                # 客户版本逻辑
                sql = "SELECT minor_version FROM minor_version_number WHERE branch = %s AND customer IS NULL ORDER BY id DESC LIMIT 1"
                cursor.execute(sql, (self.branch,))
                result = cursor.fetchone()

                if result:
                    logger.info(f"找到基础版本: {result[0]}")
                    new_version = result[0]
                else:
                    new_version = self.minor_version_update()
            else:
                # 标准版本逻辑
                sql = "SELECT minor_version FROM minor_version_number WHERE branch = %s ORDER BY id DESC LIMIT 1"
                cursor.execute(sql, (self.branch,))
                result = cursor.fetchone()

                if result:
                    logger.info(f"找到最新版本: {result[0]}")
                    new_version = self.minor_version_update(version=result[0])
                else:
                    new_version = self.minor_version_update()

            conn.close()
            logger.info(f"新版本号: {new_version}")

            # 记录新版本号
            success = self.record_minor_version_number(self.branch, new_version, self.customer)

            if success:
                print(new_version)
            else:
                logger.error("版本号记录失败")

        except Exception as e:
            logger.error(f"获取版本号失败: {e}")
            logger.error(traceback.format_exc())
            raise

    def minor_version_update(self, version=None):
        """更新小版本号 - 保持原始逻辑"""
        if not version:
            version = self.branch

        logger.info(f"更新版本号: {version}")

        try:
            version_part, branch_part = version.split("-", 1)
            split_version = version_part.split(".")
        except ValueError as e:
            logger.error(f"版本号格式错误: {version}")
            raise ValueError(f"版本号格式错误: {version}")

        if len(split_version) == 2:
            major_version, minor_version = split_version
            new_version = f"{major_version}.{minor_version}.1-{branch_part}"
        elif len(split_version) == 3:
            major_version, minor_version, patch_version = split_version
            new_version = f"{major_version}.{minor_version}.{int(patch_version) + 1}-{branch_part}"
        else:
            logger.error(f"版本号格式不正确: {version}")
            raise ValueError(f"版本号格式不正确: {version}")

        return new_version

    def record_minor_version_number(self, branch: str, version: str, customer: Optional[str] = None) -> bool:
        """记录小版本号到数据库 - 不使用create_time字段"""
        try:
            conn, cursor = connect_to_db()
            
            if customer:
                sql = "INSERT INTO minor_version_number (branch, customer, minor_version) VALUES (%s, %s, %s)"
                cursor.execute(sql, (branch, customer, version))
            else:
                sql = "INSERT INTO minor_version_number (branch, minor_version) VALUES (%s, %s)"
                cursor.execute(sql, (branch, version))
            
            conn.commit()
            conn.close()
            logger.info(f"版本号记录成功: {version}")
            return True
        except Exception as e:
            logger.error(f"记录版本号失败: {e}")
            logger.error(traceback.format_exc())
            return False

    def update_commit_id(self) -> None:
        """更新提交ID"""
        if not self.commit_id or not self.latest_version:
            logger.error("缺少必要参数: commit_id 或 latest_version")
            return

        logger.info(f"更新提交ID: {self.commit_id} for version {self.latest_version}")
        
        try:
            conn, cursor = connect_to_db()
            
            if self.customer:
                sql = """
                      UPDATE minor_version_number 
                      SET scheduler_commit_id = %s,
                          scheduler_ui_commit_id = %s,
                          tunnel_commit_id = %s,
                          tunnel_web_commit_id = %s
                      WHERE minor_version = %s AND customer = %s
                      """
                cursor.execute(sql, (self.scheduler_id, self.scheduler_ui_id, 
                                   self.tunnel_id, self.tunnel_web_id, 
                                   self.latest_version, self.customer))
            else:
                sql = """
                      UPDATE minor_version_number 
                      SET scheduler_commit_id = %s,
                          scheduler_ui_commit_id = %s,
                          tunnel_commit_id = %s,
                          tunnel_web_commit_id = %s
                      WHERE minor_version = %s AND customer IS NULL
                      """
                cursor.execute(sql, (self.scheduler_id, self.scheduler_ui_id, 
                                   self.tunnel_id, self.tunnel_web_id, 
                                   self.latest_version))
            
            conn.commit()
            conn.close()
            logger.info(f"提交ID更新成功: {self.latest_version}")
            print(f"提交ID已更新: {self.commit_id}")
            
        except Exception as e:
            logger.error(f"更新提交ID失败: {e}")
            logger.error(traceback.format_exc())

    def delete_version_record(self) -> None:
        """删除版本记录"""
        if not self.latest_version or self.latest_version == 'unknown':
            logger.warning(f"版本号无效或未知，跳过删除操作: {self.latest_version}")
            return

        try:
            conn, cursor = connect_to_db()
            
            if self.customer:
                sql = "DELETE FROM minor_version_number WHERE minor_version = %s AND customer = %s"
                cursor.execute(sql, (self.latest_version, self.customer))
            else:
                sql = "DELETE FROM minor_version_number WHERE minor_version = %s AND customer IS NULL"
                cursor.execute(sql, (self.latest_version,))

            conn.commit()
            conn.close()
            logger.info(f"版本记录删除成功: {self.latest_version}")
            print(f"版本记录删除成功: {self.latest_version}")
            
        except Exception as e:
            logger.error(f"删除版本记录失败: {e}")
            logger.error(traceback.format_exc())
            print(f"版本记录删除失败: {self.latest_version}")

    def upload_to_oss(self):
        """上传到OSS"""
        print("\n\n🚀 开始上传安装包到OSS\n\n")

        if not self.file_path or not os.path.exists(self.file_path):
            logger.error(f"安装包文件不存在: {self.file_path}")
            return

        logger.info(f"开始上传文件到OSS: {self.file_path}")

        # OSS配置
        try:
            AccessKeyID = "LTAI5tQ5SWAFG55MSEuQLFsY"
            AccessKeySecret = "******************************"
            Endpoint = "https://oss-cn-wulanchabu-internal.aliyuncs.com"
            auth = Auth(AccessKeyID, AccessKeySecret)
            bucket = Bucket(auth, Endpoint, "whale-ops")
        except Exception as e:
            logger.error("阿里云OSS配置错误，请检查配置")
            raise

        # 确定OSS文件路径
        file_name = os.path.basename(self.file_path)
        try:
            if file_name.split("-")[-1].startswith("release"):
                oss_file_path = "three-in-one/release/"
            else:
                oss_file_path = "three-in-one/test/"
        except Exception as e:
            logger.error(f"获取OSS文件路径失败: {e}")
            oss_file_path = "three-in-one/test/"

        if self.customer:
            oss_file_path += f"{self.customer}/"

        # 异步计算MD5
        md5_queue = queue.Queue()
        md5_thread = threading.Thread(
            target=get_file_md5_async,
            args=(self.file_path, md5_queue)
        )
        md5_thread.start()

        logger.info(f"开始上传到OSS，开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        try:
            start_time = time.time()
            print(f"📤 上传路径: {oss_file_path}{file_name}")
            logger.info(f"OSS端点: {Endpoint}")
            logger.info(f"上传文件: {file_name}, OSS路径: {oss_file_path}")
            logger.info(f"本地文件路径: {self.file_path}")
            print(f"📁 本地文件路径: {self.file_path}")

            # 上传文件到OSS
            bucket.put_object_from_file(
                key=os.path.join(oss_file_path, file_name),
                filename=self.file_path
            )

            # 计算耗时
            cost_time = (time.time() - start_time) / 60
            print(f"✅ 上传成功，耗时: {cost_time:.2f}分钟")
            logger.info(f"上传成功，耗时: {cost_time:.2f}分钟")

        except Exception as e:
            logger.error(f"上传到OSS失败: {e}")
            logger.error(traceback.format_exc())
            raise

        # 设置文件为公共读权限
        try:
            logger.info(f"设置文件为公共读权限: {oss_file_path}{file_name}")
            bucket.put_object_acl(
                key=oss_file_path + file_name,
                permission=oss2.OBJECT_ACL_PUBLIC_READ
            )
            logger.info("文件权限设置成功")
        except Exception as e:
            logger.error(f"设置文件权限失败: {e}")
            raise

        # 生成下载链接
        public_download_link = f"{DEFAULT_PUBLIC_DOWNLOAD_LINK}/{oss_file_path}{file_name}"
        intranet_download_link = f"{DEFAULT_INTRANET_DOWNLOAD_LINK}/{oss_file_path}{file_name}"

        print(f"\n🌐 内网下载链接: {intranet_download_link}")
        print(f"🌍 公网下载链接: {public_download_link}")

        # 等待MD5计算完成
        md5_thread.join()
        package_md5 = md5_queue.get()

        if package_md5 is None:
            logger.error("MD5计算失败")
            return

        print(f"🔐 安装包MD5: {package_md5}")
        logger.info(f"上传结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 更新数据库
        if self.latest_version:
            success = self.update_download_links(
                self.latest_version, public_download_link,
                intranet_download_link, package_md5, self.customer
            )
            if success:
                logger.info("💾 数据库更新成功")
                print("💾 下载链接和MD5已更新到数据库")
            else:
                logger.error("数据库更新失败")

    def update_download_links(self, version: str, public_link: str, intranet_link: str,
                            md5_sum: str, customer: Optional[str] = None) -> bool:
        """更新下载链接和MD5"""
        try:
            conn, cursor = connect_to_db()
            
            if customer:
                sql = """
                      UPDATE minor_version_number
                      SET public_download_link = %s,
                          intranet_download_link = %s,
                          package_md5sum = %s
                      WHERE minor_version = %s AND customer = %s
                      """
                cursor.execute(sql, (public_link, intranet_link, md5_sum, version, customer))
            else:
                sql = """
                      UPDATE minor_version_number
                      SET public_download_link = %s,
                          intranet_download_link = %s,
                          package_md5sum = %s
                      WHERE minor_version = %s AND customer IS NULL
                      """
                cursor.execute(sql, (public_link, intranet_link, md5_sum, version))

            conn.commit()
            conn.close()
            logger.info(f"下载链接和MD5更新成功: {version}")
            return True
        except Exception as e:
            logger.error(f"更新下载链接失败: {e}")
            logger.error(traceback.format_exc())
            return False

    def get_download_link_and_md5(self):
        """获取包的下载链接和MD5"""
        try:
            conn, cursor = connect_to_db()
            if self.customer:
                sql = "SELECT public_download_link, intranet_download_link, package_md5sum FROM minor_version_number WHERE minor_version = %s AND customer = %s"
                cursor.execute(sql, (self.latest_version, self.customer))
            else:
                sql = "SELECT public_download_link, intranet_download_link, package_md5sum FROM minor_version_number WHERE minor_version = %s AND customer IS NULL"
                cursor.execute(sql, (self.latest_version,))

            result = cursor.fetchone()
            conn.close()

            if result:
                return result[0], result[1], result[2]
            else:
                return None, None, None
        except Exception as e:
            logger.error(f"获取下载链接和MD5失败: {e}")
            logger.error(traceback.format_exc())
            return None, None, None

    def get_commit_message(self):
        """获取最新提交的提交信息"""
        if not self.build_path:
            logger.warning("构建路径未提供，无法获取提交信息")
            return None, None, None, None

        scheduler_path = os.path.join(self.build_path, "whalescheduler")
        scheduler_ui_path = os.path.join(self.build_path, "whalescheduler/whalescheduler-ui")
        tunnel_path = os.path.join(self.build_path, "whaletunnel")
        tunnel_web_path = os.path.join(self.build_path, "whaletunnel-web")

        try:
            conn, cursor = connect_to_db()

            # 获取上一次的Commit ID
            if self.customer:
                sql = """SELECT minor_version, scheduler_commit_id, scheduler_ui_commit_id,
                        tunnel_commit_id, tunnel_web_commit_id
                        FROM minor_version_number
                        WHERE branch = %s AND customer = %s
                        ORDER BY id DESC LIMIT 2"""
                cursor.execute(sql, (self.branch, self.customer))
            else:
                sql = """SELECT minor_version, scheduler_commit_id, scheduler_ui_commit_id,
                        tunnel_commit_id, tunnel_web_commit_id
                        FROM minor_version_number
                        WHERE branch = %s AND customer IS NULL
                        ORDER BY id DESC LIMIT 2"""
                cursor.execute(sql, (self.branch,))

            result = cursor.fetchall()
            conn.close()

            if not result or len(result) != 2:
                logger.warning("无法获取足够的版本记录进行对比")
                return None, None, None, None

            # 上一次的Commit ID
            previous_scheduler_id = result[1][1]
            previous_scheduler_ui_id = result[1][2]
            previous_tunnel_id = result[1][3]
            previous_tunnel_web_id = result[1][4]

            # 获取各组件的提交信息
            scheduler_commit_message = None
            if previous_scheduler_id and self.scheduler_id != previous_scheduler_id:
                scheduler_commit_message = self.get_commit_message_by_id(scheduler_path, previous_scheduler_id)

            scheduler_ui_commit_message = None
            if previous_scheduler_ui_id and self.scheduler_ui_id != previous_scheduler_ui_id:
                scheduler_ui_commit_message = self.get_commit_message_by_id(scheduler_ui_path, previous_scheduler_ui_id)

            tunnel_commit_message = None
            if previous_tunnel_id and self.tunnel_id != previous_tunnel_id:
                tunnel_commit_message = self.get_commit_message_by_id(tunnel_path, previous_tunnel_id)

            tunnel_web_commit_message = None
            if previous_tunnel_web_id and self.tunnel_web_id != previous_tunnel_web_id:
                tunnel_web_commit_message = self.get_commit_message_by_id(tunnel_web_path, previous_tunnel_web_id)

            return scheduler_commit_message, scheduler_ui_commit_message, tunnel_commit_message, tunnel_web_commit_message

        except Exception as e:
            logger.error(f"获取提交信息失败: {e}")
            logger.error(traceback.format_exc())
            return None, None, None, None

    def get_commit_message_by_id(self, path, commit_id):
        """根据提交ID获取提交信息"""
        try:
            if not os.path.exists(path):
                logger.warning(f"路径不存在: {path}")
                return None

            repo = git.Repo(path)
            commits = list(repo.iter_commits(f"{commit_id}..HEAD"))

            if not commits:
                return None

            commit_messages = {}
            for commit in commits:
                short_commit_id = f"{commit.hexsha[:7]}"
                commit_message = "\n".join([
                    line.strip().lstrip("* ")
                    for line in commit.message.split('\n')
                    if line.strip() and not line.startswith((
                        "Co-authored-by", "* improve", "* update", "* optimize", "--"
                    ))
                ])
                if commit_message:
                    commit_messages[short_commit_id] = commit_message

            return commit_messages if commit_messages else None

        except Exception as e:
            logger.error(f"获取提交信息失败 {path}: {e}")
            return None

    def get_current_commit_message(self, project_name):
        """获取当前提交信息"""
        try:
            if not self.build_path:
                return None

            path_map = {
                "whalescheduler": os.path.join(self.build_path, "whalescheduler"),
                "whalescheduler-ui": os.path.join(self.build_path, "whalescheduler/whalescheduler-ui"),
                "whaletunnel": os.path.join(self.build_path, "whaletunnel"),
                "whaletunnel-web": os.path.join(self.build_path, "whaletunnel-web")
            }

            if project_name not in path_map:
                logger.error(f"不支持的项目名称: {project_name}")
                return None

            path = path_map[project_name]
            if not os.path.exists(path):
                logger.warning(f"路径不存在: {path}")
                return None

            repo = git.Repo(path)
            commit_messages = [
                line.strip().lstrip("* ")
                for line in repo.head.commit.message.split("\n")
                if line.strip() and not line.startswith((
                    "Co-authored-by", "* improve", "* update", "* optimize", "--"
                ))
            ]

            return commit_messages if commit_messages else None

        except Exception as e:
            logger.error(f"获取当前提交信息失败 {project_name}: {e}")
            return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Build whaleStudio.')
    parser.add_argument(
        '--operation', '-o', type=str, required=True,
        help='The operation of whaleStudio to build.'
    )
    parser.add_argument(
        '--branch', '-b', type=str,
        help='The branch of whaleStudio to build.'
    )
    parser.add_argument(
        '--customer', '-P', type=str,
        help='The customer name of whaleStudio to build.'
    )
    parser.add_argument(
        '--file', '-f', type=str,
        help="package file path"
    )
    parser.add_argument(
        "--jenkins_url", type=str,
        help="Jenkins URL"
    )
    parser.add_argument(
        "--build_status", type=str,
        help="The build status of whaleStudio"
    )
    parser.add_argument(
        "--latest_version", type=str,
        help="The latest version of whaleStudio"
    )
    parser.add_argument(
        "--commit_id", type=str,
        help="The commit id of whaleStudio"
    )
    parser.add_argument(
        "--build_path", type=str,
        help="The build path of whaleStudio"
    )

    try:
        args = parser.parse_args()

        build_whaleStudio = BuildWhaleStudio(
            operation=args.operation,
            branch=args.branch,
            customer=args.customer,
            parser=parser,
            file_path=args.file,
            jenkins_url=args.jenkins_url,
            build_status=args.build_status,
            latest_version=args.latest_version,
            commit_id=args.commit_id,
            build_path=args.build_path
        )

        build_whaleStudio.build()

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        logger.error(traceback.format_exc())
        print('Please input the correct parameters.')
        sys.exit(1)


if __name__ == '__main__':
    main()

