#!/bin/bash

# Jenkins用户认证初始化脚本
# 用于设置Jenkins管理员账户和启用安全认证

set -e

# 配置变量
JENKINS_HOME="${JENKINS_HOME:-/data/jenkins/jenkins_home}"
ADMIN_USERNAME="${ADMIN_USERNAME:-admin}"
ADMIN_PASSWORD="${ADMIN_PASSWORD:-admin123}"
ADMIN_EMAIL="${ADMIN_EMAIL:-<EMAIL>}"
ADMIN_FULLNAME="${ADMIN_FULLNAME:-Administrator}"

# 日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S'): $1"
}

# 检查Jenkins是否运行
check_jenkins_status() {
    log_message "检查Jenkins运行状态..."
    
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080 | grep -q "200\|403\|404"; then
            log_message "Jenkins服务已启动"
            return 0
        fi
        
        sleep 2
        ((attempt++))
        log_message "等待Jenkins启动... ($attempt/$max_attempts)"
    done
    
    log_message "错误: Jenkins服务未启动或无法访问"
    return 1
}

# 创建初始管理员用户配置
create_admin_user() {
    log_message "创建管理员用户配置..."
    
    # 创建用户目录
    mkdir -p "$JENKINS_HOME/users/$ADMIN_USERNAME"
    
    # 生成密码哈希 (使用Jenkins的BCrypt格式)
    # 这里使用简单的方法，实际生产环境建议使用更安全的密码哈希
    local password_hash
    if command -v python3 &> /dev/null; then
        password_hash=$(python3 -c "
import bcrypt
import sys
password = '$ADMIN_PASSWORD'.encode('utf-8')
hashed = bcrypt.hashpw(password, bcrypt.gensalt(rounds=10))
print('#jbcrypt:' + hashed.decode('utf-8'))
")
    else
        # 如果没有python3，使用Jenkins内置的密码哈希方法
        log_message "警告: 未找到python3，将使用默认密码哈希"
        password_hash="#jbcrypt:\$2a\$10\$DdaS0.guY50SE2YmQMfkMOjs2pIlNa8qQce4HPiLdieqwL8msXuZm"  # admin123
    fi
    
    # 创建用户配置文件
    cat > "$JENKINS_HOME/users/$ADMIN_USERNAME/config.xml" << EOF
<?xml version='1.1' encoding='UTF-8'?>
<user>
  <version>10</version>
  <id>$ADMIN_USERNAME</id>
  <fullName>$ADMIN_FULLNAME</fullName>
  <description></description>
  <properties>
    <jenkins.security.ApiTokenProperty>
      <tokenStore>
        <tokenList/>
      </tokenStore>
    </jenkins.security.ApiTokenProperty>
    <com.cloudbees.plugins.credentials.UserCredentialsProvider_-UserCredentialsProperty plugin="credentials@2.6.1">
      <domainCredentialsMap class="hudson.util.CopyOnWriteMap\$Hash"/>
    </com.cloudbees.plugins.credentials.UserCredentialsProvider_-UserCredentialsProperty>
    <hudson.plugins.emailext.watching.EmailExtWatchAction_-UserProperty plugin="email-ext@2.87">
      <triggers/>
    </hudson.plugins.emailext.watching.EmailExtWatchAction_-UserProperty>
    <hudson.model.MyViewsProperty>
      <views>
        <hudson.model.AllView>
          <owner class="hudson.model.MyViewsProperty" reference="../../.."/>
          <name>all</name>
          <description></description>
          <filterExecutors>false</filterExecutors>
          <filterQueue>false</filterQueue>
          <properties class="hudson.model.View\$PropertyList"/>
        </hudson.model.AllView>
      </views>
    </hudson.model.MyViewsProperty>
    <org.jenkinsci.plugins.displayurlapi.user.PreferredProviderUserProperty plugin="display-url-api@2.3.5">
      <providerId>default</providerId>
    </org.jenkinsci.plugins.displayurlapi.user.PreferredProviderUserProperty>
    <hudson.model.PaneStatusProperties>
      <collapsed/>
    </hudson.model.PaneStatusProperties>
    <hudson.security.HudsonPrivateSecurityRealm_-Details>
      <passwordHash>$password_hash</passwordHash>
    </hudson.security.HudsonPrivateSecurityRealm_-Details>
    <hudson.model.TimeZoneProperty>
      <timeZoneName></timeZoneName>
    </hudson.model.TimeZoneProperty>
    <hudson.search.UserSearchProperty>
      <insensitiveSearch>true</insensitiveSearch>
    </hudson.search.UserSearchProperty>
    <hudson.model.UserProperty>
      <emailAddress>$ADMIN_EMAIL</emailAddress>
    </hudson.model.UserProperty>
  </properties>
</user>
EOF
    
    log_message "管理员用户配置已创建: $ADMIN_USERNAME"
}

# 配置Jenkins安全设置
configure_security() {
    log_message "配置Jenkins安全设置..."
    
    # 备份原有配置
    if [ -f "$JENKINS_HOME/config.xml" ]; then
        cp "$JENKINS_HOME/config.xml" "$JENKINS_HOME/config.xml.backup.$(date +%Y%m%d_%H%M%S)"
        log_message "已备份原有配置文件"
    fi
    
    # 创建安全配置
    cat > "$JENKINS_HOME/config.xml" << 'EOF'
<?xml version='1.1' encoding='UTF-8'?>
<hudson>
  <disabledAdministrativeMonitors/>
  <version>2.521</version>
  <installStateName>RUNNING</installStateName>
  <numExecutors>2</numExecutors>
  <mode>NORMAL</mode>
  <useSecurity>true</useSecurity>
  <authorizationStrategy class="hudson.security.FullControlOnceLoggedInAuthorizationStrategy">
    <denyAnonymousReadAccess>true</denyAnonymousReadAccess>
  </authorizationStrategy>
  <securityRealm class="hudson.security.HudsonPrivateSecurityRealm">
    <disableSignup>true</disableSignup>
    <enableCaptcha>false</enableCaptcha>
  </securityRealm>
  <disableRememberMe>false</disableRememberMe>
  <projectNamingStrategy class="jenkins.model.ProjectNamingStrategy$DefaultProjectNamingStrategy"/>
  <workspaceDir>${JENKINS_HOME}/workspace/${ITEM_FULLNAME}</workspaceDir>
  <buildsDir>${ITEM_ROOTDIR}/builds</buildsDir>
  <markupFormatter class="hudson.markup.PlainTextMarkupFormatter"/>
  <jdks/>
  <viewsTabBar class="hudson.views.DefaultViewsTabBar"/>
  <myViewsTabBar class="hudson.views.DefaultMyViewsTabBar"/>
  <clouds/>
  <quietPeriod>5</quietPeriod>
  <scmCheckoutRetryCount>0</scmCheckoutRetryCount>
  <views>
    <hudson.model.AllView>
      <owner class="hudson" reference="../../.."/>
      <name>all</name>
      <description></description>
      <filterExecutors>false</filterExecutors>
      <filterQueue>false</filterQueue>
      <properties class="hudson.model.View$PropertyList"/>
    </hudson.model.AllView>
  </views>
  <primaryView>all</primaryView>
  <slaveAgentPort>-1</slaveAgentPort>
  <label></label>
  <crumbIssuer class="hudson.security.csrf.DefaultCrumbIssuer">
    <excludeClientIPFromCrumb>false</excludeClientIPFromCrumb>
  </crumbIssuer>
  <nodeProperties/>
  <globalNodeProperties/>
</hudson>
EOF
    
    log_message "Jenkins安全配置已更新"
}

# 创建初始化完成标记
create_init_marker() {
    log_message "创建初始化完成标记..."
    
    # 创建.jenkins目录下的标记文件
    touch "$JENKINS_HOME/.jenkins-initialized"
    
    # 创建用户初始化标记
    echo "admin" > "$JENKINS_HOME/users/users.xml"
    
    log_message "初始化标记已创建"
}

# 重启Jenkins服务
restart_jenkins() {
    log_message "重启Jenkins服务..."
    
    # 查找Jenkins进程
    local jenkins_pid=$(pgrep -f "jenkins.war" || true)
    
    if [ -n "$jenkins_pid" ]; then
        log_message "停止Jenkins进程 (PID: $jenkins_pid)"
        kill -TERM "$jenkins_pid"
        
        # 等待进程停止
        local count=0
        while [ $count -lt 30 ] && kill -0 "$jenkins_pid" 2>/dev/null; do
            sleep 1
            ((count++))
        done
        
        if kill -0 "$jenkins_pid" 2>/dev/null; then
            log_message "强制停止Jenkins进程"
            kill -KILL "$jenkins_pid"
        fi
    fi
    
    log_message "请手动重启Jenkins服务"
    log_message "运行命令: ./jenkins_start.sh"
}

# 显示登录信息
show_login_info() {
    log_message "Jenkins认证配置完成！"
    log_message ""
    log_message "=== 登录信息 ==="
    log_message "访问地址: http://172.16.3.74:8080"
    log_message "用户名: $ADMIN_USERNAME"
    log_message "密码: $ADMIN_PASSWORD"
    log_message "邮箱: $ADMIN_EMAIL"
    log_message ""
    log_message "=== 重要提示 ==="
    log_message "1. 请立即登录并修改默认密码"
    log_message "2. 建议配置更强的密码策略"
    log_message "3. 可以在 '系统管理' -> '全局安全配置' 中调整安全设置"
    log_message "4. 如需添加更多用户，请在 '系统管理' -> '管理用户' 中操作"
}

# 交互式设置管理员信息
interactive_setup() {
    echo "=== Jenkins管理员账户设置 ==="
    echo ""
    
    read -p "管理员用户名 (默认: admin): " input_username
    ADMIN_USERNAME="${input_username:-$ADMIN_USERNAME}"
    
    read -s -p "管理员密码 (默认: admin123): " input_password
    echo ""
    ADMIN_PASSWORD="${input_password:-$ADMIN_PASSWORD}"
    
    read -p "管理员邮箱 (默认: <EMAIL>): " input_email
    ADMIN_EMAIL="${input_email:-$ADMIN_EMAIL}"
    
    read -p "管理员全名 (默认: Administrator): " input_fullname
    ADMIN_FULLNAME="${input_fullname:-$ADMIN_FULLNAME}"
    
    echo ""
    echo "=== 配置确认 ==="
    echo "用户名: $ADMIN_USERNAME"
    echo "密码: [已设置]"
    echo "邮箱: $ADMIN_EMAIL"
    echo "全名: $ADMIN_FULLNAME"
    echo ""
    
    read -p "确认以上配置? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_message "配置已取消"
        exit 0
    fi
}

# 主函数
main() {
    log_message "开始Jenkins认证初始化..."
    
    # 检查参数
    if [[ "$1" == "--interactive" || "$1" == "-i" ]]; then
        interactive_setup
    fi
    
    # 检查Jenkins目录
    if [ ! -d "$JENKINS_HOME" ]; then
        log_message "错误: Jenkins目录不存在: $JENKINS_HOME"
        exit 1
    fi
    
    # 执行配置步骤
    create_admin_user
    configure_security
    create_init_marker
    
    log_message "配置完成，需要重启Jenkins服务"
    
    # 询问是否重启
    read -p "是否现在重启Jenkins服务? (y/N): " restart_confirm
    if [[ "$restart_confirm" =~ ^[Yy]$ ]]; then
        restart_jenkins
    fi
    
    show_login_info
}

# 显示帮助信息
show_help() {
    echo "Jenkins认证初始化脚本"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -i, --interactive    交互式设置管理员信息"
    echo "  -h, --help          显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  JENKINS_HOME        Jenkins主目录 (默认: /data/jenkins/jenkins_home)"
    echo "  ADMIN_USERNAME      管理员用户名 (默认: admin)"
    echo "  ADMIN_PASSWORD      管理员密码 (默认: admin123)"
    echo "  ADMIN_EMAIL         管理员邮箱 (默认: <EMAIL>)"
    echo "  ADMIN_FULLNAME      管理员全名 (默认: Administrator)"
    echo ""
    echo "示例:"
    echo "  $0                  使用默认配置"
    echo "  $0 -i               交互式配置"
    echo "  ADMIN_PASSWORD=mypass $0  使用环境变量设置密码"
}

# 处理命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -i|--interactive)
        main "$@"
        ;;
    "")
        main "$@"
        ;;
    *)
        echo "未知选项: $1"
        echo "使用 -h 或 --help 查看帮助信息"
        exit 1
        ;;
esac
