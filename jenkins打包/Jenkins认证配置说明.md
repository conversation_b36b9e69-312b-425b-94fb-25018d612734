# Jenkins认证配置说明

## 概述

本文档说明如何为Jenkins配置用户名密码认证，包括初始化管理员账户和启用安全认证。

## 方法一：使用认证初始化脚本（推荐）

### 1. 快速配置（使用默认设置）

```bash
# 给脚本添加执行权限
chmod +x jenkins_auth_setup.sh

# 使用默认配置运行
./jenkins_auth_setup.sh
```

默认配置：
- 用户名：`admin`
- 密码：`admin123`
- 邮箱：`<EMAIL>`
- 全名：`Administrator`

### 2. 交互式配置

```bash
# 交互式设置管理员信息
./jenkins_auth_setup.sh --interactive
```

### 3. 使用环境变量配置

```bash
# 设置自定义管理员信息
export ADMIN_USERNAME="myadmin"
export ADMIN_PASSWORD="MySecurePassword123!"
export ADMIN_EMAIL="<EMAIL>"
export ADMIN_FULLNAME="System Administrator"

# 运行配置脚本
./jenkins_auth_setup.sh
```

## 方法二：启用Jenkins设置向导

### 1. 启用设置向导模式

```bash
# 设置环境变量启用设置向导
export ENABLE_SETUP_WIZARD=true

# 启动Jenkins
./jenkins_start.sh
```

### 2. 通过Web界面完成设置

1. 访问 `http://172.16.3.74:8080`
2. 查看初始管理员密码：
   ```bash
   cat /data/jenkins/jenkins_home/secrets/initialAdminPassword
   ```
3. 按照向导完成初始化设置

## 方法三：手动配置

### 1. 停止Jenkins服务

```bash
# 查找并停止Jenkins进程
pkill -f jenkins.war
```

### 2. 修改配置文件

编辑 `$JENKINS_HOME/config.xml`，启用安全认证：

```xml
<useSecurity>true</useSecurity>
<authorizationStrategy class="hudson.security.FullControlOnceLoggedInAuthorizationStrategy">
  <denyAnonymousReadAccess>true</denyAnonymousReadAccess>
</authorizationStrategy>
<securityRealm class="hudson.security.HudsonPrivateSecurityRealm">
  <disableSignup>true</disableSignup>
  <enableCaptcha>false</enableCaptcha>
</securityRealm>
```

### 3. 重启Jenkins

```bash
./jenkins_start.sh
```

## 常见问题解决

### 1. 忘记管理员密码

```bash
# 方法1：重置为默认密码
./jenkins_auth_setup.sh

# 方法2：禁用安全认证（临时）
# 编辑 $JENKINS_HOME/config.xml，将 <useSecurity>true</useSecurity> 改为 false
# 重启Jenkins后可以无密码访问，然后重新配置用户
```

### 2. 无法访问Jenkins

检查以下几点：
1. Jenkins服务是否正常运行
2. 端口8080是否被占用
3. 防火墙设置是否正确
4. 字体问题是否已解决

### 3. 字体相关错误

```bash
# 运行字体修复脚本
sudo ./install_fonts.sh
```

## 安全建议

### 1. 密码安全
- 使用强密码（至少8位，包含大小写字母、数字和特殊字符）
- 定期更换密码
- 不要使用默认密码

### 2. 访问控制
- 启用CSRF保护
- 配置适当的用户权限
- 定期审查用户账户

### 3. 网络安全
- 使用HTTPS（生产环境）
- 配置防火墙规则
- 限制管理员访问IP

## 配置文件说明

### 主要配置文件位置

```
$JENKINS_HOME/
├── config.xml              # Jenkins主配置文件
├── users/                  # 用户配置目录
│   └── admin/
│       └── config.xml      # 管理员用户配置
├── secrets/                # 密钥目录
│   └── initialAdminPassword # 初始管理员密码
└── .jenkins-initialized    # 初始化完成标记
```

### 重要配置参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `useSecurity` | 是否启用安全认证 | `false` |
| `denyAnonymousReadAccess` | 拒绝匿名读取访问 | `false` |
| `disableSignup` | 禁用用户注册 | `false` |
| `enableCaptcha` | 启用验证码 | `false` |

## 故障排除

### 查看Jenkins日志

```bash
# 查看启动日志
tail -f /data/jenkins/jenkins_home/jenkins.log

# 查看系统日志
tail -f /var/log/jenkins_start.log
```

### 检查配置文件

```bash
# 验证XML配置文件语法
xmllint --noout $JENKINS_HOME/config.xml

# 查看用户配置
ls -la $JENKINS_HOME/users/
```

### 重置到初始状态

```bash
# 备份当前配置
cp -r $JENKINS_HOME $JENKINS_HOME.backup

# 删除安全配置（谨慎操作）
rm -f $JENKINS_HOME/config.xml
rm -rf $JENKINS_HOME/users/
rm -f $JENKINS_HOME/.jenkins-initialized

# 重启Jenkins
./jenkins_start.sh
```

## 联系支持

如果遇到问题，请提供以下信息：
1. Jenkins版本
2. 操作系统版本
3. Java版本
4. 错误日志
5. 配置文件内容（去除敏感信息）
