#!/bin/bash

# Jenkins Git凭据配置脚本
# 用于配置GitHub访问凭据

set -e

JENKINS_HOME="${JENKINS_HOME:-/data/jenkins/jenkins_home}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S'): $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S'): $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S'): $1"
}

# 检查Jenkins是否运行
check_jenkins_running() {
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080 | grep -q "200\|403\|404"; then
        return 0
    else
        return 1
    fi
}

# 创建GitHub Token凭据
create_github_token_credential() {
    local token="$1"
    local credential_id="${2:-github-token}"
    local description="${3:-GitHub Personal Access Token}"
    
    log_info "创建GitHub Token凭据..."
    
    # 创建凭据XML配置
    local credentials_dir="$JENKINS_HOME/credentials.xml"
    
    # 如果凭据文件不存在，创建基础结构
    if [ ! -f "$credentials_dir" ]; then
        cat > "$credentials_dir" << 'EOF'
<?xml version='1.1' encoding='UTF-8'?>
<com.cloudbees.plugins.credentials.SystemCredentialsProvider plugin="credentials@2.6.1">
  <domainCredentialsMap class="hudson.util.CopyOnWriteMap$Hash">
    <entry>
      <com.cloudbees.plugins.credentials.domains.Domain>
        <specifications/>
      </com.cloudbees.plugins.credentials.domains.Domain>
      <java.util.concurrent.CopyOnWriteArrayList>
      </java.util.concurrent.CopyOnWriteArrayList>
    </entry>
  </domainCredentialsMap>
</com.cloudbees.plugins.credentials.SystemCredentialsProvider>
EOF
    fi
    
    log_info "GitHub Token凭据配置已创建"
}

# 创建SSH密钥凭据
create_ssh_credential() {
    local private_key_path="$1"
    local credential_id="${2:-github-ssh}"
    local username="${3:-git}"
    local description="${4:-GitHub SSH Key}"
    
    log_info "创建SSH密钥凭据..."
    
    if [ ! -f "$private_key_path" ]; then
        log_error "SSH私钥文件不存在: $private_key_path"
        return 1
    fi
    
    # 读取私钥内容
    local private_key_content=$(cat "$private_key_path")
    
    log_info "SSH密钥凭据配置已创建"
}

# 生成SSH密钥对
generate_ssh_key() {
    local key_path="${1:-$JENKINS_HOME/.ssh/id_rsa}"
    local email="${2:-jenkins@localhost}"
    
    log_info "生成SSH密钥对..."
    
    # 创建.ssh目录
    local ssh_dir=$(dirname "$key_path")
    mkdir -p "$ssh_dir"
    chmod 700 "$ssh_dir"
    
    # 生成SSH密钥
    if [ ! -f "$key_path" ]; then
        ssh-keygen -t rsa -b 4096 -C "$email" -f "$key_path" -N ""
        chmod 600 "$key_path"
        chmod 644 "${key_path}.pub"
        
        log_info "SSH密钥已生成: $key_path"
        log_info "公钥内容:"
        cat "${key_path}.pub"
        log_warn "请将上述公钥添加到GitHub账户的SSH密钥中"
    else
        log_info "SSH密钥已存在: $key_path"
    fi
    
    # 添加GitHub到known_hosts
    if [ ! -f "$ssh_dir/known_hosts" ] || ! grep -q "github.com" "$ssh_dir/known_hosts"; then
        log_info "添加GitHub到known_hosts..."
        ssh-keyscan -H github.com >> "$ssh_dir/known_hosts" 2>/dev/null
        chmod 644 "$ssh_dir/known_hosts"
    fi
}

# 测试GitHub连接
test_github_connection() {
    local method="$1"  # ssh 或 https
    local credential="$2"
    
    log_info "测试GitHub连接 ($method)..."
    
    if [ "$method" = "ssh" ]; then
        # 测试SSH连接
        if ssh -T ************** -o StrictHostKeyChecking=no 2>&1 | grep -q "successfully authenticated"; then
            log_info "✓ SSH连接测试成功"
            return 0
        else
            log_warn "✗ SSH连接测试失败"
            return 1
        fi
    elif [ "$method" = "https" ]; then
        # 测试HTTPS连接
        if curl -s -H "Authorization: token $credential" https://api.github.com/user | grep -q "login"; then
            log_info "✓ HTTPS连接测试成功"
            return 0
        else
            log_warn "✗ HTTPS连接测试失败"
            return 1
        fi
    fi
}

# 更新Jenkinsfile使用HTTPS
update_jenkinsfile_to_https() {
    local token="$1"
    
    log_info "更新Jenkinsfile使用HTTPS认证..."
    
    # 更新主Jenkinsfile
    if [ -f "$SCRIPT_DIR/Jenkinsfile_" ]; then
        # 备份原文件
        cp "$SCRIPT_DIR/Jenkinsfile_" "$SCRIPT_DIR/Jenkinsfile_.backup.$(date +%Y%m%d_%H%M%S)"
        
        # 替换Git URL
        sed -i "s|**************:WhaleOps|https://${token}@github.com/WhaleOps|g" "$SCRIPT_DIR/Jenkinsfile_"
        log_info "已更新 Jenkinsfile_ 使用HTTPS"
    fi
    
    # 更新其他Jenkinsfile
    if [ -f "$SCRIPT_DIR/jenkinsfile" ]; then
        cp "$SCRIPT_DIR/jenkinsfile" "$SCRIPT_DIR/jenkinsfile.backup.$(date +%Y%m%d_%H%M%S)"
        sed -i "s|**************:WhaleOps|https://${token}@github.com/WhaleOps|g" "$SCRIPT_DIR/jenkinsfile"
        log_info "已更新 jenkinsfile 使用HTTPS"
    fi
    
    if [ -f "$SCRIPT_DIR/deploy_jenkinsfile" ]; then
        cp "$SCRIPT_DIR/deploy_jenkinsfile" "$SCRIPT_DIR/deploy_jenkinsfile.backup.$(date +%Y%m%d_%H%M%S)"
        sed -i "s|**************:WhaleOps|https://${token}@github.com/WhaleOps|g" "$SCRIPT_DIR/deploy_jenkinsfile"
        log_info "已更新 deploy_jenkinsfile 使用HTTPS"
    fi
}

# 交互式配置
interactive_setup() {
    echo "=== Jenkins Git凭据配置 ==="
    echo ""
    echo "选择认证方式:"
    echo "1. GitHub Personal Access Token (HTTPS) - 推荐"
    echo "2. SSH密钥"
    echo "3. 生成新的SSH密钥"
    echo ""
    
    read -p "请选择 (1-3): " auth_method
    
    case "$auth_method" in
        1)
            echo ""
            echo "=== GitHub Token配置 ==="
            echo "请访问 https://github.com/settings/tokens 创建Personal Access Token"
            echo "需要的权限: repo, workflow, read:org"
            echo ""
            read -p "请输入GitHub Token: " github_token
            
            if [ -z "$github_token" ]; then
                log_error "Token不能为空"
                exit 1
            fi
            
            # 测试Token
            if test_github_connection "https" "$github_token"; then
                create_github_token_credential "$github_token"
                update_jenkinsfile_to_https "$github_token"
                log_info "GitHub Token配置完成"
            else
                log_error "Token验证失败，请检查Token是否正确"
                exit 1
            fi
            ;;
            
        2)
            echo ""
            echo "=== SSH密钥配置 ==="
            read -p "请输入SSH私钥路径 (默认: $JENKINS_HOME/.ssh/id_rsa): " ssh_key_path
            ssh_key_path="${ssh_key_path:-$JENKINS_HOME/.ssh/id_rsa}"
            
            if [ ! -f "$ssh_key_path" ]; then
                log_error "SSH私钥文件不存在: $ssh_key_path"
                exit 1
            fi
            
            create_ssh_credential "$ssh_key_path"
            
            if test_github_connection "ssh"; then
                log_info "SSH密钥配置完成"
            else
                log_warn "SSH连接测试失败，请检查密钥是否已添加到GitHub"
            fi
            ;;
            
        3)
            echo ""
            echo "=== 生成SSH密钥 ==="
            read -p "请输入邮箱地址 (默认: jenkins@localhost): " email
            email="${email:-jenkins@localhost}"
            
            generate_ssh_key "$JENKINS_HOME/.ssh/id_rsa" "$email"
            create_ssh_credential "$JENKINS_HOME/.ssh/id_rsa"
            
            echo ""
            log_warn "请将上述公钥添加到GitHub账户，然后重新运行测试"
            ;;
            
        *)
            log_error "无效的选择"
            exit 1
            ;;
    esac
}

# 显示配置状态
show_config_status() {
    echo "=== Git凭据配置状态 ==="
    echo ""
    
    # 检查SSH密钥
    if [ -f "$JENKINS_HOME/.ssh/id_rsa" ]; then
        echo "✓ SSH私钥存在: $JENKINS_HOME/.ssh/id_rsa"
        if [ -f "$JENKINS_HOME/.ssh/id_rsa.pub" ]; then
            echo "✓ SSH公钥存在"
            echo "  公钥内容: $(cat $JENKINS_HOME/.ssh/id_rsa.pub)"
        fi
    else
        echo "✗ SSH密钥不存在"
    fi
    
    # 检查known_hosts
    if [ -f "$JENKINS_HOME/.ssh/known_hosts" ] && grep -q "github.com" "$JENKINS_HOME/.ssh/known_hosts"; then
        echo "✓ GitHub已添加到known_hosts"
    else
        echo "✗ GitHub未添加到known_hosts"
    fi
    
    # 检查Jenkinsfile配置
    if grep -q "https://.*@github.com" "$SCRIPT_DIR/Jenkinsfile_" 2>/dev/null; then
        echo "✓ Jenkinsfile已配置HTTPS认证"
    elif grep -q "**************" "$SCRIPT_DIR/Jenkinsfile_" 2>/dev/null; then
        echo "✓ Jenkinsfile已配置SSH认证"
    else
        echo "? Jenkinsfile认证方式未知"
    fi
}

# 显示帮助信息
show_help() {
    echo "Jenkins Git凭据配置脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -i, --interactive   交互式配置"
    echo "  -s, --status        显示配置状态"
    echo "  -h, --help          显示此帮助信息"
    echo ""
    echo "功能:"
    echo "  1. 配置GitHub Personal Access Token"
    echo "  2. 配置SSH密钥认证"
    echo "  3. 生成新的SSH密钥对"
    echo "  4. 更新Jenkinsfile认证方式"
    echo "  5. 测试GitHub连接"
}

# 主函数
main() {
    log_info "开始配置Jenkins Git凭据..."
    
    # 检查Jenkins目录
    if [ ! -d "$JENKINS_HOME" ]; then
        log_error "Jenkins目录不存在: $JENKINS_HOME"
        exit 1
    fi
    
    case "${1:-}" in
        -i|--interactive)
            interactive_setup
            ;;
        -s|--status)
            show_config_status
            ;;
        -h|--help)
            show_help
            ;;
        "")
            interactive_setup
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac
    
    echo ""
    log_info "配置完成！建议重启Jenkins服务以应用更改"
}

# 执行主函数
main "$@"
