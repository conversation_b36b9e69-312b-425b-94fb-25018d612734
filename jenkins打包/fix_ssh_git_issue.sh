#!/bin/bash

# Jenkins SSH Git问题修复脚本
# 解决SSH连接和Git克隆问题

set -e

JENKINS_HOME="${JENKINS_HOME:-/data/jenkins/jenkins_home}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S'): $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S'): $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S'): $1"
}

# 检查并安装netcat
install_netcat() {
    log_info "检查并安装netcat..."
    
    if command -v nc &> /dev/null; then
        log_info "netcat已安装: $(which nc)"
        return 0
    fi
    
    # 检查权限
    if [ "$EUID" -ne 0 ] && ! sudo -n true 2>/dev/null; then
        log_error "需要root权限来安装netcat"
        return 1
    fi
    
    # 根据包管理器安装netcat
    if command -v yum &> /dev/null; then
        log_info "使用yum安装netcat..."
        if [ "$EUID" -eq 0 ]; then
            yum install -y nc nmap-ncat
        else
            sudo yum install -y nc nmap-ncat
        fi
    elif command -v dnf &> /dev/null; then
        log_info "使用dnf安装netcat..."
        if [ "$EUID" -eq 0 ]; then
            dnf install -y nc nmap-ncat
        else
            sudo dnf install -y nc nmap-ncat
        fi
    elif command -v apt-get &> /dev/null; then
        log_info "使用apt-get安装netcat..."
        if [ "$EUID" -eq 0 ]; then
            apt-get update
            apt-get install -y netcat-openbsd
        else
            sudo apt-get update
            sudo apt-get install -y netcat-openbsd
        fi
    elif command -v apk &> /dev/null; then
        log_info "使用apk安装netcat..."
        if [ "$EUID" -eq 0 ]; then
            apk add --no-cache netcat-openbsd
        else
            sudo apk add --no-cache netcat-openbsd
        fi
    else
        log_error "未识别的包管理器，无法自动安装netcat"
        return 1
    fi
    
    log_info "netcat安装完成"
}

# 配置SSH客户端
configure_ssh_client() {
    log_info "配置SSH客户端..."
    
    local ssh_dir="$JENKINS_HOME/.ssh"
    mkdir -p "$ssh_dir"
    chmod 700 "$ssh_dir"
    
    # 创建SSH配置文件
    cat > "$ssh_dir/config" << 'EOF'
# GitHub SSH配置
Host github.com
    HostName github.com
    User git
    Port 22
    IdentityFile ~/.ssh/id_rsa
    IdentitiesOnly yes
    StrictHostKeyChecking no
    UserKnownHostsFile ~/.ssh/known_hosts
    ServerAliveInterval 60
    ServerAliveCountMax 10
    TCPKeepAlive yes
    
# 禁用代理（如果有代理问题）
Host *
    ProxyCommand none
EOF
    
    chmod 600 "$ssh_dir/config"
    log_info "SSH配置文件已创建: $ssh_dir/config"
}

# 生成SSH密钥
generate_ssh_key() {
    log_info "生成SSH密钥..."
    
    local ssh_dir="$JENKINS_HOME/.ssh"
    local key_file="$ssh_dir/id_rsa"
    
    if [ -f "$key_file" ]; then
        log_info "SSH密钥已存在: $key_file"
        return 0
    fi
    
    mkdir -p "$ssh_dir"
    chmod 700 "$ssh_dir"
    
    # 生成SSH密钥
    ssh-keygen -t rsa -b 4096 -C "jenkins@localhost" -f "$key_file" -N ""
    chmod 600 "$key_file"
    chmod 644 "${key_file}.pub"
    
    log_info "SSH密钥已生成"
    log_info "公钥内容:"
    echo "----------------------------------------"
    cat "${key_file}.pub"
    echo "----------------------------------------"
    log_warn "请将上述公钥添加到GitHub账户的SSH密钥中"
    log_warn "访问: https://github.com/settings/ssh/new"
}

# 添加GitHub到known_hosts
add_github_to_known_hosts() {
    log_info "添加GitHub到known_hosts..."
    
    local ssh_dir="$JENKINS_HOME/.ssh"
    local known_hosts="$ssh_dir/known_hosts"
    
    mkdir -p "$ssh_dir"
    
    # 清除可能存在的GitHub条目
    if [ -f "$known_hosts" ]; then
        ssh-keygen -R github.com 2>/dev/null || true
        ssh-keygen -R ************ 2>/dev/null || true
        ssh-keygen -R ************ 2>/dev/null || true
    fi
    
    # 添加GitHub主机密钥
    ssh-keyscan -H github.com >> "$known_hosts" 2>/dev/null
    chmod 644 "$known_hosts"
    
    log_info "GitHub已添加到known_hosts"
}

# 测试SSH连接
test_ssh_connection() {
    log_info "测试SSH连接到GitHub..."
    
    # 设置SSH环境
    export SSH_AUTH_SOCK=""
    export GIT_SSH_COMMAND="ssh -i $JENKINS_HOME/.ssh/id_rsa -F $JENKINS_HOME/.ssh/config"
    
    # 测试SSH连接
    local ssh_result
    ssh_result=$(ssh -i "$JENKINS_HOME/.ssh/id_rsa" -F "$JENKINS_HOME/.ssh/config" -T ************** 2>&1 || true)
    
    if echo "$ssh_result" | grep -q "successfully authenticated"; then
        log_info "✓ SSH连接测试成功"
        echo "$ssh_result"
        return 0
    else
        log_warn "✗ SSH连接测试失败"
        echo "SSH输出: $ssh_result"
        return 1
    fi
}

# 配置Git使用SSH
configure_git_ssh() {
    log_info "配置Git使用SSH..."
    
    # 设置Git SSH命令
    git config --global core.sshCommand "ssh -i $JENKINS_HOME/.ssh/id_rsa -F $JENKINS_HOME/.ssh/config"
    
    # 设置Git用户信息
    git config --global user.name "Jenkins"
    git config --global user.email "jenkins@localhost"
    
    # 其他Git配置
    git config --global init.defaultBranch main
    git config --global pull.rebase false
    git config --global core.autocrlf input
    
    log_info "Git SSH配置完成"
}

# 测试Git克隆
test_git_clone() {
    log_info "测试Git克隆..."
    
    local test_dir="/tmp/git_clone_test_$$"
    mkdir -p "$test_dir"
    cd "$test_dir"
    
    # 设置环境变量
    export GIT_SSH_COMMAND="ssh -i $JENKINS_HOME/.ssh/id_rsa -F $JENKINS_HOME/.ssh/config"
    
    # 测试克隆一个小的公共仓库
    if <NAME_EMAIL>:WhaleOps/whalescheduler.git --depth 1 2>/dev/null; then
        log_info "✓ Git克隆测试成功"
        cd /
        rm -rf "$test_dir"
        return 0
    else
        log_warn "✗ Git克隆测试失败"
        cd /
        rm -rf "$test_dir"
        return 1
    fi
}

# 修复代理问题
fix_proxy_issues() {
    log_info "修复代理问题..."
    
    # 检查是否设置了代理
    if [ -n "$http_proxy" ] || [ -n "$https_proxy" ] || [ -n "$HTTP_PROXY" ] || [ -n "$HTTPS_PROXY" ]; then
        log_warn "检测到代理设置，这可能影响SSH连接"
        
        # 为SSH连接禁用代理
        export no_proxy="github.com,*.github.com,$no_proxy"
        export NO_PROXY="github.com,*.github.com,$NO_PROXY"
        
        log_info "已为GitHub禁用代理"
    fi
    
    # 检查Git代理设置
    local git_http_proxy=$(git config --global http.proxy 2>/dev/null || echo "")
    local git_https_proxy=$(git config --global https.proxy 2>/dev/null || echo "")
    
    if [ -n "$git_http_proxy" ] || [ -n "$git_https_proxy" ]; then
        log_warn "检测到Git代理设置，为GitHub禁用代理"
        git config --global http.https://github.com.proxy ""
        git config --global https.https://github.com.proxy ""
    fi
}

# 更新Jenkinsfile使用正确的Git命令
update_jenkinsfile() {
    log_info "检查Jenkinsfile配置..."
    
    local jenkinsfiles=("$SCRIPT_DIR/Jenkinsfile_" "$SCRIPT_DIR/jenkinsfile" "$SCRIPT_DIR/deploy_jenkinsfile")
    
    for jenkinsfile in "${jenkinsfiles[@]}"; do
        if [ -f "$jenkinsfile" ]; then
            log_info "检查文件: $(basename "$jenkinsfile")"
            
            # 检查是否使用了SSH URL
            if grep -q "**************" "$jenkinsfile"; then
                log_info "✓ 文件使用SSH URL"
            else
                log_warn "文件可能需要更新为SSH URL"
            fi
        fi
    done
}

# 创建Jenkins环境脚本
create_jenkins_env_script() {
    log_info "创建Jenkins环境脚本..."
    
    cat > "$JENKINS_HOME/git_env.sh" << EOF
#!/bin/bash
# Jenkins Git环境配置脚本

export GIT_SSH_COMMAND="ssh -i $JENKINS_HOME/.ssh/id_rsa -F $JENKINS_HOME/.ssh/config"
export SSH_AUTH_SOCK=""

# 禁用GitHub代理
export no_proxy="github.com,*.github.com,\$no_proxy"
export NO_PROXY="github.com,*.github.com,\$NO_PROXY"

# Git配置
git config --global core.sshCommand "ssh -i $JENKINS_HOME/.ssh/id_rsa -F $JENKINS_HOME/.ssh/config"
git config --global user.name "Jenkins"
git config --global user.email "jenkins@localhost"
EOF
    
    chmod +x "$JENKINS_HOME/git_env.sh"
    log_info "环境脚本已创建: $JENKINS_HOME/git_env.sh"
}

# 显示解决方案总结
show_solution_summary() {
    log_info "SSH Git问题修复完成！"
    echo ""
    echo "=== 修复总结 ==="
    echo "1. ✓ netcat已安装"
    echo "2. ✓ SSH客户端已配置"
    echo "3. ✓ SSH密钥已生成/检查"
    echo "4. ✓ GitHub已添加到known_hosts"
    echo "5. ✓ Git SSH配置已完成"
    echo "6. ✓ 代理问题已修复"
    echo ""
    echo "=== 重要信息 ==="
    echo "SSH密钥位置: $JENKINS_HOME/.ssh/id_rsa"
    echo "SSH配置文件: $JENKINS_HOME/.ssh/config"
    echo "环境脚本: $JENKINS_HOME/git_env.sh"
    echo ""
    echo "=== 下一步操作 ==="
    echo "1. 将SSH公钥添加到GitHub账户"
    echo "2. 重启Jenkins服务"
    echo "3. 重新运行Pipeline"
    echo ""
    if [ -f "$JENKINS_HOME/.ssh/id_rsa.pub" ]; then
        echo "=== SSH公钥内容 ==="
        cat "$JENKINS_HOME/.ssh/id_rsa.pub"
        echo ""
        echo "请访问 https://github.com/settings/ssh/new 添加上述公钥"
    fi
}

# 主函数
main() {
    log_info "开始修复SSH Git问题..."
    
    # 检查Jenkins目录
    if [ ! -d "$JENKINS_HOME" ]; then
        log_error "Jenkins目录不存在: $JENKINS_HOME"
        exit 1
    fi
    
    install_netcat
    configure_ssh_client
    generate_ssh_key
    add_github_to_known_hosts
    configure_git_ssh
    fix_proxy_issues
    create_jenkins_env_script
    update_jenkinsfile
    
    echo ""
    log_info "测试连接..."
    if test_ssh_connection; then
        if test_git_clone; then
            log_info "✓ 所有测试通过！"
        else
            log_warn "SSH连接成功，但Git克隆失败，请检查仓库权限"
        fi
    else
        log_warn "SSH连接失败，请确保SSH公钥已添加到GitHub"
    fi
    
    show_solution_summary
}

# 执行主函数
main "$@"
