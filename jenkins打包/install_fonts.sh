#!/bin/bash

# Jenkins字体问题修复脚本
# 解决Jenkins在headless模式下的AWT字体初始化问题

set -e

# 日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S'): $1"
}

# 检查操作系统
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    elif type lsb_release >/dev/null 2>&1; then
        OS=$(lsb_release -si)
        VER=$(lsb_release -sr)
    elif [ -f /etc/redhat-release ]; then
        OS="Red Hat Enterprise Linux"
        VER=$(cat /etc/redhat-release | sed 's/.*release //' | sed 's/ .*//')
    else
        OS=$(uname -s)
        VER=$(uname -r)
    fi
    
    log_message "检测到操作系统: $OS $VER"
}

# 安装字体包
install_fonts() {
    log_message "开始安装字体包..."
    
    # 检查权限
    if [ "$EUID" -ne 0 ] && ! sudo -n true 2>/dev/null; then
        log_message "错误: 需要root权限或sudo权限来安装字体包"
        log_message "请使用以下命令之一运行此脚本:"
        log_message "  sudo $0"
        log_message "  或者以root用户运行: $0"
        exit 1
    fi
    
    # 根据包管理器安装字体
    if command -v yum &> /dev/null; then
        # CentOS/RHEL/Fedora
        log_message "使用yum安装字体包..."
        if [ "$EUID" -eq 0 ]; then
            yum install -y fontconfig dejavu-sans-fonts dejavu-serif-fonts dejavu-sans-mono-fonts
        else
            sudo yum install -y fontconfig dejavu-sans-fonts dejavu-serif-fonts dejavu-sans-mono-fonts
        fi
        
    elif command -v dnf &> /dev/null; then
        # Fedora (newer versions)
        log_message "使用dnf安装字体包..."
        if [ "$EUID" -eq 0 ]; then
            dnf install -y fontconfig dejavu-sans-fonts dejavu-serif-fonts dejavu-sans-mono-fonts
        else
            sudo dnf install -y fontconfig dejavu-sans-fonts dejavu-serif-fonts dejavu-sans-mono-fonts
        fi
        
    elif command -v apt-get &> /dev/null; then
        # Ubuntu/Debian
        log_message "使用apt-get安装字体包..."
        if [ "$EUID" -eq 0 ]; then
            apt-get update
            apt-get install -y fontconfig fonts-dejavu-core fonts-dejavu-extra
        else
            sudo apt-get update
            sudo apt-get install -y fontconfig fonts-dejavu-core fonts-dejavu-extra
        fi
        
    elif command -v apk &> /dev/null; then
        # Alpine Linux
        log_message "使用apk安装字体包..."
        if [ "$EUID" -eq 0 ]; then
            apk add --no-cache fontconfig ttf-dejavu
        else
            sudo apk add --no-cache fontconfig ttf-dejavu
        fi
        
    elif command -v zypper &> /dev/null; then
        # openSUSE
        log_message "使用zypper安装字体包..."
        if [ "$EUID" -eq 0 ]; then
            zypper install -y fontconfig dejavu-fonts
        else
            sudo zypper install -y fontconfig dejavu-fonts
        fi
        
    else
        log_message "错误: 未识别的包管理器"
        log_message "请手动安装以下包:"
        log_message "  - fontconfig"
        log_message "  - dejavu字体包"
        exit 1
    fi
    
    log_message "字体包安装完成"
}

# 刷新字体缓存
refresh_font_cache() {
    log_message "刷新字体缓存..."
    
    if command -v fc-cache &> /dev/null; then
        fc-cache -fv
        log_message "字体缓存刷新完成"
    else
        log_message "警告: fc-cache命令不可用，跳过字体缓存刷新"
    fi
}

# 验证字体安装
verify_fonts() {
    log_message "验证字体安装..."
    
    if command -v fc-list &> /dev/null; then
        FONT_COUNT=$(fc-list | wc -l)
        log_message "系统字体数量: $FONT_COUNT"
        
        # 检查DejaVu字体
        DEJAVU_COUNT=$(fc-list | grep -i dejavu | wc -l)
        log_message "DejaVu字体数量: $DEJAVU_COUNT"
        
        if [ "$DEJAVU_COUNT" -gt 0 ]; then
            log_message "✓ DejaVu字体安装成功"
            log_message "DejaVu字体列表:"
            fc-list | grep -i dejavu | head -5
        else
            log_message "✗ 警告: 未检测到DejaVu字体"
        fi
        
        if [ "$FONT_COUNT" -gt 0 ]; then
            log_message "✓ 字体安装验证成功"
        else
            log_message "✗ 错误: 系统中没有检测到任何字体"
            exit 1
        fi
    else
        log_message "警告: fc-list命令不可用，无法验证字体安装"
    fi
}

# 创建字体配置文件
create_font_config() {
    log_message "创建字体配置文件..."
    
    # 创建用户字体配置目录
    mkdir -p ~/.config/fontconfig
    
    # 创建字体配置文件
    cat > ~/.config/fontconfig/fonts.conf << 'EOF'
<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>
    <!-- 设置默认字体 -->
    <alias>
        <family>serif</family>
        <prefer>
            <family>DejaVu Serif</family>
        </prefer>
    </alias>
    <alias>
        <family>sans-serif</family>
        <prefer>
            <family>DejaVu Sans</family>
        </prefer>
    </alias>
    <alias>
        <family>monospace</family>
        <prefer>
            <family>DejaVu Sans Mono</family>
        </prefer>
    </alias>
</fontconfig>
EOF
    
    log_message "字体配置文件创建完成: ~/.config/fontconfig/fonts.conf"
}

# 显示Java字体信息
show_java_font_info() {
    log_message "显示Java字体信息..."
    
    if command -v java &> /dev/null; then
        # 创建临时Java程序来测试字体
        cat > /tmp/FontTest.java << 'EOF'
import java.awt.*;
import java.awt.font.*;

public class FontTest {
    public static void main(String[] args) {
        System.setProperty("java.awt.headless", "true");
        
        try {
            GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
            String[] fontNames = ge.getAvailableFontFamilyNames();
            
            System.out.println("Java可用字体数量: " + fontNames.length);
            System.out.println("前10个字体:");
            for (int i = 0; i < Math.min(10, fontNames.length); i++) {
                System.out.println("  " + fontNames[i]);
            }
            
            // 检查DejaVu字体
            boolean hasDejaVu = false;
            for (String fontName : fontNames) {
                if (fontName.toLowerCase().contains("dejavu")) {
                    hasDejaVu = true;
                    System.out.println("✓ 找到DejaVu字体: " + fontName);
                }
            }
            
            if (!hasDejaVu) {
                System.out.println("✗ 警告: Java中未找到DejaVu字体");
            }
            
        } catch (Exception e) {
            System.out.println("字体测试失败: " + e.getMessage());
        }
    }
}
EOF
        
        # 编译并运行测试
        if javac /tmp/FontTest.java 2>/dev/null && java -cp /tmp FontTest 2>/dev/null; then
            log_message "Java字体测试完成"
        else
            log_message "警告: Java字体测试失败"
        fi
        
        # 清理临时文件
        rm -f /tmp/FontTest.java /tmp/FontTest.class
    else
        log_message "警告: Java未安装，跳过Java字体测试"
    fi
}

# 主函数
main() {
    log_message "开始Jenkins字体问题修复..."
    
    detect_os
    install_fonts
    refresh_font_cache
    verify_fonts
    create_font_config
    show_java_font_info
    
    log_message "字体修复完成！"
    log_message ""
    log_message "修复说明:"
    log_message "1. 已安装fontconfig和DejaVu字体包"
    log_message "2. 已刷新系统字体缓存"
    log_message "3. 已创建用户字体配置文件"
    log_message "4. 已验证Java字体可用性"
    log_message ""
    log_message "现在可以重新启动Jenkins服务"
}

# 执行主函数
main "$@"
