#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
<PERSON>密码哈希生成工具
用于生成Jenkins兼容的BCrypt密码哈希
"""

import sys
import secrets
import string
import getpass
from typing import Optional

try:
    import bcrypt
except ImportError:
    print("错误: 需要安装bcrypt库")
    print("运行: pip install bcrypt")
    sys.exit(1)


def generate_random_password(length: int = 12) -> str:
    """生成随机密码"""
    # 定义字符集
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special = "!@#$%^&*"
    
    # 确保密码包含各种字符类型
    password = [
        secrets.choice(lowercase),
        secrets.choice(uppercase),
        secrets.choice(digits),
        secrets.choice(special)
    ]
    
    # 填充剩余长度
    all_chars = lowercase + uppercase + digits + special
    for _ in range(length - 4):
        password.append(secrets.choice(all_chars))
    
    # 打乱顺序
    secrets.SystemRandom().shuffle(password)
    
    return ''.join(password)


def hash_password(password: str) -> str:
    """生成Jenkins兼容的密码哈希"""
    # 生成salt并哈希密码
    salt = bcrypt.gensalt(rounds=10)
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    
    # 返回Jenkins格式的哈希
    return f"#jbcrypt:{hashed.decode('utf-8')}"


def verify_password(password: str, hashed: str) -> bool:
    """验证密码是否正确"""
    if hashed.startswith("#jbcrypt:"):
        hashed = hashed[9:]  # 移除前缀
    
    try:
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    except Exception:
        return False


def get_password_input() -> str:
    """安全地获取密码输入"""
    while True:
        password = getpass.getpass("请输入密码: ")
        if not password:
            print("密码不能为空，请重新输入")
            continue
            
        confirm = getpass.getpass("请确认密码: ")
        if password != confirm:
            print("两次输入的密码不一致，请重新输入")
            continue
            
        return password


def check_password_strength(password: str) -> tuple[bool, list[str]]:
    """检查密码强度"""
    issues = []
    
    if len(password) < 8:
        issues.append("密码长度至少8位")
    
    if not any(c.islower() for c in password):
        issues.append("需要包含小写字母")
    
    if not any(c.isupper() for c in password):
        issues.append("需要包含大写字母")
    
    if not any(c.isdigit() for c in password):
        issues.append("需要包含数字")
    
    if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
        issues.append("建议包含特殊字符")
    
    return len(issues) == 0, issues


def main():
    """主函数"""
    print("Jenkins密码哈希生成工具")
    print("=" * 40)
    
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help']:
            print("用法:")
            print("  python3 generate_password.py           # 交互式模式")
            print("  python3 generate_password.py -g        # 生成随机密码")
            print("  python3 generate_password.py -g 16     # 生成指定长度的随机密码")
            print("  python3 generate_password.py -v        # 验证密码哈希")
            return
        
        elif sys.argv[1] == '-g':
            # 生成随机密码
            length = 12
            if len(sys.argv) > 2:
                try:
                    length = int(sys.argv[2])
                    if length < 8:
                        print("密码长度至少为8位")
                        return
                except ValueError:
                    print("无效的密码长度")
                    return
            
            password = generate_random_password(length)
            hashed = hash_password(password)
            
            print(f"生成的随机密码: {password}")
            print(f"密码哈希: {hashed}")
            print("\n请妥善保存密码，哈希值可用于Jenkins配置")
            return
        
        elif sys.argv[1] == '-v':
            # 验证密码哈希
            password = getpass.getpass("请输入密码: ")
            hashed = input("请输入哈希值: ")
            
            if verify_password(password, hashed):
                print("✓ 密码验证成功")
            else:
                print("✗ 密码验证失败")
            return
    
    # 交互式模式
    print("选择操作:")
    print("1. 为现有密码生成哈希")
    print("2. 生成随机密码和哈希")
    print("3. 验证密码哈希")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == '1':
        # 为现有密码生成哈希
        password = get_password_input()
        
        # 检查密码强度
        is_strong, issues = check_password_strength(password)
        if not is_strong:
            print("\n密码强度检查:")
            for issue in issues:
                print(f"  ⚠ {issue}")
            
            confirm = input("\n是否继续使用此密码? (y/N): ")
            if confirm.lower() != 'y':
                print("操作已取消")
                return
        
        hashed = hash_password(password)
        print(f"\n密码哈希: {hashed}")
        print("\n此哈希值可用于Jenkins用户配置")
    
    elif choice == '2':
        # 生成随机密码
        try:
            length = int(input("密码长度 (默认12): ") or "12")
            if length < 8:
                print("密码长度至少为8位")
                return
        except ValueError:
            print("无效的密码长度")
            return
        
        password = generate_random_password(length)
        hashed = hash_password(password)
        
        print(f"\n生成的随机密码: {password}")
        print(f"密码哈希: {hashed}")
        print("\n请妥善保存密码，哈希值可用于Jenkins配置")
    
    elif choice == '3':
        # 验证密码哈希
        password = getpass.getpass("请输入密码: ")
        hashed = input("请输入哈希值: ")
        
        if verify_password(password, hashed):
            print("✓ 密码验证成功")
        else:
            print("✗ 密码验证失败")
    
    else:
        print("无效的选择")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n操作已取消")
    except Exception as e:
        print(f"\n错误: {e}")
        sys.exit(1)
