# 如何启用Jenkins登录认证

## 方法一：启用设置向导（推荐新安装）

### 1. 启用设置向导模式

```bash
# 设置环境变量启用设置向导
export ENABLE_SETUP_WIZARD=true

# 启动Jenkins
./jenkins_start.sh
```

### 2. 通过Web界面完成设置

1. 访问 `http://172.16.3.74:8080`
2. 您会看到"解锁Jenkins"页面
3. 获取初始管理员密码：
   ```bash
   cat /data/jenkins/jenkins_home/secrets/initialAdminPassword
   ```
4. 输入密码并点击"继续"
5. 选择"安装推荐的插件"或"选择插件来安装"
6. 创建第一个管理员用户
7. 配置Jenkins URL
8. 完成设置

## 方法二：使用认证配置脚本（推荐已有安装）

### 1. 停止Jenkins服务

```bash
# 使用管理脚本停止
./jenkins_manager.sh stop

# 或手动停止
pkill -f jenkins.war
```

### 2. 运行认证配置脚本

```bash
# 使用默认配置（用户名：admin，密码：admin123）
./jenkins_auth_setup.sh

# 或交互式配置
./jenkins_auth_setup.sh --interactive

# 或使用环境变量自定义
export ADMIN_USERNAME="myadmin"
export ADMIN_PASSWORD="MySecurePassword123!"
./jenkins_auth_setup.sh
```

### 3. 重启Jenkins

```bash
./jenkins_manager.sh start
```

## 方法三：手动配置（高级用户）

### 1. 停止Jenkins服务

```bash
./jenkins_manager.sh stop
```

### 2. 删除跳过向导的配置

编辑启动脚本，将：
```bash
ENABLE_SETUP_WIZARD="${ENABLE_SETUP_WIZARD:-false}"
```

改为：
```bash
ENABLE_SETUP_WIZARD="${ENABLE_SETUP_WIZARD:-true}"
```

### 3. 清理现有配置（可选）

如果要重新开始设置：
```bash
# 备份现有配置
cp -r /data/jenkins/jenkins_home /data/jenkins/jenkins_home.backup

# 删除安全配置（将重新显示设置向导）
rm -f /data/jenkins/jenkins_home/config.xml
rm -rf /data/jenkins/jenkins_home/users/
rm -f /data/jenkins/jenkins_home/secrets/initialAdminPassword
```

### 4. 重启Jenkins

```bash
./jenkins_manager.sh start
```

## 验证登录配置

### 1. 检查Jenkins状态

```bash
./jenkins_manager.sh status
```

### 2. 访问Web界面

打开浏览器访问：`http://172.16.3.74:8080`

您应该看到：
- **设置向导模式**：解锁Jenkins页面
- **已配置认证**：登录页面

### 3. 查看日志

如果遇到问题，查看日志：
```bash
./jenkins_manager.sh logs 100
```

## 常见问题解决

### 1. 找不到初始密码文件

```bash
# 检查密码文件是否存在
ls -la /data/jenkins/jenkins_home/secrets/

# 如果不存在，重新生成
rm -f /data/jenkins/jenkins_home/config.xml
./jenkins_manager.sh restart
```

### 2. 页面显示"Jenkins正在启动"

等待几分钟让Jenkins完全启动，然后刷新页面。

### 3. 无法访问8080端口

```bash
# 检查端口是否监听
netstat -tlnp | grep 8080

# 检查防火墙
sudo firewall-cmd --list-ports
sudo firewall-cmd --add-port=8080/tcp --permanent
sudo firewall-cmd --reload
```

### 4. 忘记管理员密码

```bash
# 方法1：重新配置认证
./jenkins_auth_setup.sh

# 方法2：临时禁用安全认证
# 编辑 /data/jenkins/jenkins_home/config.xml
# 将 <useSecurity>true</useSecurity> 改为 <useSecurity>false</useSecurity>
# 重启Jenkins后无密码访问，然后重新配置
```

## 安全建议

1. **立即更改默认密码**：首次登录后立即更改密码
2. **使用强密码**：至少8位，包含大小写字母、数字和特殊字符
3. **限制用户权限**：不要给所有用户管理员权限
4. **启用CSRF保护**：在全局安全配置中启用
5. **定期备份**：定期备份Jenkins配置

## 快速命令参考

```bash
# 启用登录模式启动
export ENABLE_SETUP_WIZARD=true && ./jenkins_start.sh

# 配置认证（交互式）
./jenkins_auth_setup.sh -i

# 查看状态
./jenkins_manager.sh status

# 查看日志
./jenkins_manager.sh logs

# 备份配置
./jenkins_manager.sh backup
```
