pipeline {
    agent any
    options {
        timestamps()
    }
    environment {
        whale_scheduler_version = '2.6-release'
        currentPath = pwd()
        jenkins_url = "http://***********:8080/blue/organizations/jenkins/WhaleStudio_2.6-release/detail/WhaleStudio_2.6-release/${env.BUILD_ID}/pipeline"
        PYTHON_PATH = '/data/miniconda3/bin/python'
        BUILD_SCRIPT = '/data/jenkins/whaleStudio_build_test.py'
        GIT_TOKEN = '****************************************'
        GITHUB_BASE_URL = "https://${GIT_TOKEN}@github.com/WhaleOps"
    }

    stages {
        stage("Clean workspace") {
            steps {
                script {
                    echo "Clean workspace"
                    cleanWs()
                }
            }
        }

        stage("Send Confirmation Message"){
            steps{
                script{
                    retry(3){
                        def command = "${PYTHON_PATH} ${BUILD_SCRIPT} --operation send_confirmation_message --jenkins_url ${jenkins_url} --branch ${whale_scheduler_version}"
                        sh "${command}"
                    }
                }
            }
        }

        stage("Get User Input") {
            steps {
                script {
                    def timeoutTime = 10
                    try {
                        def userInput = timeout(time: timeoutTime, unit: 'MINUTES') {
                            input(
                                id: 'userInput',
                                message: '请选择版本号,客户版本,部署服务器,是否上传OSS',
                                parameters: [
                                    choice(name: "customer", choices: ['None', '-Pzhongxinjiantou', '-Pguangdalicai', '-Pthird-party-IT', '-Prenshou', '-Prenbao'], description: "Please select the customer"),
                                    choice(name: "host_name", choices: ['None','st01', 'st02', 'st03', 'st04', 'st05', 'ws2', 'ws3', 'ws4', 'ws5'], description: "Please select the deployment host"),
                                    choice(name: "update_to_OSS", choices: ['Yes', 'No'], description: "Update to OSS"),
                                    string(name: "baseline_version", defaultValue: "${whale_scheduler_version}", description: "Please input the baseline version"),
                                    string(name: "remote_path", defaultValue: "/data/whalestudio/package", description: "Please input the ssh remote save package path"),
                                    choice(name: "build_images", choices: ['No', 'Yes'], description: "Please select whether to build images")
                                ]
                            )
                        }
                        echo "userInput: ${userInput}"
                        customer = userInput.customer
                        hostname = userInput.host_name
                        update_to_OSS = userInput.update_to_OSS
                        baseline_version = userInput.baseline_version
                        remote_path = userInput.remote_path
                        build_images = userInput.build_images
                        echo "customer: ${customer}"
                        echo "hostname: ${hostname}"
                        echo "update_to_OSS: ${update_to_OSS}"
                        echo "baseline_version: ${baseline_version}"
                        echo "remote_path: ${remote_path}"
                    } catch (org.jenkinsci.plugins.workflow.steps.FlowInterruptedException e) {
                        if (e.isCausedByUser()) {
                            currentBuild.result = 'ABORTED'
                            echo "用户主动终止构建"
                            send_message_command = "${PYTHON_PATH} ${BUILD_SCRIPT} --operation send_message --jenkins_url ${jenkins_url} --build_status cancel"
                            sh "${send_message_command}"
                        } else {
                            currentBuild.result = 'ABORTED'
                            echo "用户输入超时"
                            send_message_command = "${PYTHON_PATH} ${BUILD_SCRIPT} --operation send_message --jenkins_url ${jenkins_url} --build_status timeout"
                            sh "${send_message_command}"
                        }
                    } catch (Exception e) {
                        currentBuild.result = 'ABORTED'
                        echo "用户输入超时"
                        send_message_command = "${PYTHON_PATH} ${BUILD_SCRIPT} --operation send_message --jenkins_url ${jenkins_url} --build_status timeout"
                        sh "${send_message_command}"
                    }
                }
            }
        }

        stage("Clone the  WhaleStudio Code") {
            steps {
                script {
                    retry(3) {
                            echo "Git clone the WhaleStudio Code"
                            sh "git clone -b ${whale_scheduler_version} ${GITHUB_BASE_URL}/whalescheduler.git"
                            sh "git clone -b ${whale_scheduler_version} ${GITHUB_BASE_URL}/whaletunnel.git"
                            sh "git clone -b ${whale_scheduler_version} ${GITHUB_BASE_URL}/whaletunnel-web.git"
                            sh "cd whalescheduler && git clone -b ${whale_scheduler_version} ${GITHUB_BASE_URL}/whalescheduler-ui.git"
                    }
                }
            }
        }
        stage("Get Current Commit ID") {
            steps {
                script {
                    echo "Get Current Commit ID"
                    whalescheduler_commit_id = sh(script: "git -C whalescheduler rev-parse --short=7 HEAD", returnStdout: true).trim()
                    whalescheduler_ui_commit_id = sh(script: "git -C whalescheduler/whalescheduler-ui rev-parse --short=7 HEAD", returnStdout: true).trim()
                    whaletunnel_commit_id = sh(script: "git -C whaletunnel rev-parse --short=7 HEAD", returnStdout: true).trim()
                    whaletunnel_web_commit_id = sh(script: "git -C whaletunnel-web rev-parse --short=7 HEAD", returnStdout: true).trim()
                    echo "whalescheduler_commit_id: ${whalescheduler_commit_id}"
                    echo "whalescheduler_ui_commit_id: ${whalescheduler_ui_commit_id}"
                    echo "whaletunnel_commit_id: ${whaletunnel_commit_id}"
                    echo "whaletunnel_web_commit_id: ${whaletunnel_web_commit_id}"
                }
            }
        }

        stage("Build WhaleStudio UI") {
            steps {
                script {
                    retry(3) {
                        echo "Build WhaleStudio UI"
                        sh "cd whalescheduler/whalescheduler-ui && mvnd clean package -Prelease"
                    }
                }
            }
        }
        stage("Get Latest Version") {
            steps {
                script {
                    echo "获取最新小版本号"

                    def command = "${PYTHON_PATH} ${BUILD_SCRIPT} --operation get_version -b ${whale_scheduler_version}"
                    def get_version_command = ("${customer}" == "None" ? command : "${command} ${customer}")
                    minor_version = sh(script: get_version_command, returnStdout: true).trim()
                    echo "最新小版本号: ${minor_version}"
                }
            }
        }
        stage("Update Commit ID") {
            steps {
                script {
                    echo " Update Commit ID"
                    sh "${PYTHON_PATH} ${BUILD_SCRIPT} --operation update_commit_id --commit_id ${whalescheduler_commit_id},${whalescheduler_ui_commit_id},${whaletunnel_commit_id},${whaletunnel_web_commit_id} --latest_version ${minor_version} "

                }
            }
        }
        stage("Modify the Version number") {
            steps {
                script {
                    echo "修改版本号"
                    echo "modify whalescheduler version"
                    sh "cd whalescheduler && mvnd versions:set -DnewVersion=${minor_version}"
                    echo "modify whaletunnel version"
                    sh "cd whaletunnel && mvnd versions:set -DnewVersion=${minor_version}"
                    echo "modify whaletunnel-web version"
                    sh "cd whaletunnel-web && mvnd versions:set -DnewVersion=${minor_version}"
                }
            }

        }


        stage("Build WhaleTunnel"){
            steps {
                script {
                    retry(3) {
                        echo "Build WhaleTunnel"
                        sh "cd whaletunnel && mvnd clean install  -T2 -Dmaven.test.skip -Dcheckstyle.skip -Dspotless.check.skip -Drevision=${minor_version}"
                    }
                }
            }
        }
        stage("Build WhaleTunnelWeb"){
            steps {
                script {
                    retry(3) {
                        echo "Build WhaleTunnelWeb"
                        sh "cd whaletunnel-web && mvnd clean install -T2 -Dmaven.test.skip -Dcheckstyle.skip -Dspotless.check.skip -Dseatunnel-framework.version=${minor_version}"
                    }
                }
            }
        }
        stage("Build WhaleStudio"){
            steps {
                script {
                    retry(3) {
                        echo "Build WhaleStudio"
                        def command = "cd whalescheduler && mvnd clean package  -T2 -Dmaven.test.skip -Dcheckstyle.skip=true -Dspotless.skip=true -Prelease -Dseatunnel-framework.version=${minor_version} -Dwhaletunnel-web.version=${minor_version} "
                        def build_command = ("${customer}" == "None" ? command : "${command} ${customer}")
                        echo "build_command: ${build_command}"
                        sh build_command
                    }
                }

            }
        }


        stage("Make three-in-one directory"){
            steps {
                script {
                    echo "Make three-in-one directory"
                    sh "mkdir -p whalestudio_${minor_version}/{whaletunnel,datasource,whalescheduler}"
                }
            }
        }

        stage("Form apache tree-in-one package"){
            parallel {
                stage("Description file for whalestudio"){
                    steps {
                        script {
                            sh "tar -zxvf whalescheduler/whalescheduler-dist/target/whalescheduler-*-bin.tar.gz -C whalestudio_${minor_version}/whalescheduler"
                            sh "mv whalestudio_${minor_version}/whalescheduler/whalescheduler-*/* whalestudio_${minor_version}/whalescheduler"
                            sh "rm -rf whalestudio_${minor_version}/whalescheduler/whalescheduler-*-bin"
                            sh "rm -rf whalescheduler/whalescheduler-dist/target/*.tar.gz"
                        }
                    }
                }

                stage("Description file for whaletunnel"){
                    steps {
                        script {
                            sh "tar -zxvf whaletunnel/seatunnel-dist/target/apache-seatunnel-*-bin.tar.gz -C whalestudio_${minor_version}/whaletunnel"
                            sh "mv whalestudio_${minor_version}/whaletunnel/apache-seatunnel-*/* whalestudio_${minor_version}/whaletunnel"
                            sh "rm -rf whalestudio_${minor_version}/whaletunnel/apache-seatunnel-*"
                            sh "rm -rf whaletunnel/seatunnel-dist/target/*.tar.gz"
                        }
                    }
                }

                stage("Description file for whaletunnel-web"){
                    steps {
                        script {
                            sh "tar -zxvf whaletunnel-web/whaletunnel-web-dist/target/apache-seatunnel-web-*.tar.gz -C whalestudio_${minor_version}/datasource"
                            sh "mv whalestudio_${minor_version}/datasource/apache-seatunnel-web-*/* whalestudio_${minor_version}/datasource"
                            sh "rm -rf whalestudio_${minor_version}/datasource/apache-seatunnel-web-*"
                            sh "rm -rf whaletunnel-web/whaletunnel-web-dist/target/*.tar.gz"
                        }
                    }
                }
            }
        }
        stage("Compress three-in-one package"){
            steps {
                script {
                    echo "Compress three-in-one package"
                    if ("${customer}" == "None") {
                        compressed_package_name="whalestudio_${minor_version}.tar.gz"
                    } else {
                        compressed_package_name="whalestudio_${minor_version}_for_${customer.replace('-P', '')}.tar.gz"
                    }
                    sh "tar zcvf ${compressed_package_name} -C whalestudio_${minor_version} ."
                    echo "Package Path: ${currentPath}/${compressed_package_name}"
                    sh "rm -rf whalestudio_${minor_version}"
                }
            }
        }

        stage("Upload the installation package to oss"){
            steps {
                script {
                    currentDir = sh(script: 'pwd', returnStdout: true).trim()
                    if ("${update_to_OSS}" == "Yes") {
                        echo "Upload the installation package to oss"
                        def command = "${PYTHON_PATH} ${BUILD_SCRIPT} --operation upload_to_oss -f ${currentDir}/${compressed_package_name} --latest_version ${minor_version}"
                        def upload_command = ("${customer}" == "None" ? command : "${command} ${customer}")
                        echo "upload_command: ${upload_command}"
                        sh upload_command
                    } else {
                        echo "No need to upload the installation package to oss"
                    }
                }
            }
        }

    }
    post {
        success {
            script {
                def commonCommand = "${PYTHON_PATH} ${BUILD_SCRIPT} --operation send_end_message --jenkins_url ${jenkins_url} --branch ${whale_scheduler_version} --latest_version ${minor_version} --build_status success --build_path ${currentPath} --commit_id ${whalescheduler_commit_id},${whalescheduler_ui_commit_id},${whaletunnel_commit_id},${whaletunnel_web_commit_id}"
                def command = ("${customer}" == "None") ? commonCommand : "${commonCommand} ${customer}"
                sh "${command}"
                cleanWs()
            }
        }
        failure {
            script{
                def commonCommand = "${PYTHON_PATH} ${BUILD_SCRIPT} --operation send_end_message --jenkins_url ${jenkins_url} --branch ${whale_scheduler_version} --latest_version ${minor_version} --build_status error"
                def command = ("${customer}" == "None") ? commonCommand : "${commonCommand} ${customer}"
                sh "${command}"


                // 删除当前版本号
                def deleteCommand = "${PYTHON_PATH} ${BUILD_SCRIPT} --operation delete_version --latest_version ${minor_version}"
                def delete_command = ("${customer}" == "None") ? deleteCommand : "${deleteCommand} ${customer}"
                sh "${delete_command}"
                cleanWs()
            }
        }
        aborted {
            script {
                def commonCommand = "${PYTHON_PATH} ${BUILD_SCRIPT} --operation send_end_message --jenkins_url ${jenkins_url} --branch ${whale_scheduler_version} --latest_version ${minor_version} --build_status cancel"
                def command = ("${customer}" == "None") ? commonCommand : "${commonCommand} ${customer}"
                sh "${command}"
                // 删除当前版本号
                def deleteCommand = "${PYTHON_PATH} ${BUILD_SCRIPT} --operation delete_version --latest_version ${minor_version}"
                def delete_command = ("${customer}" == "None") ? deleteCommand : "${deleteCommand} ${customer}"
                sh "${delete_command}"
                cleanWs()
            }
        }
    }
}