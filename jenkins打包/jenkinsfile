pipeline {
    agent any
    environment {
        branch = '2.6-release'
        buildStatus = 'SUCCESS'
        jenkinsUrl = 'http://**************/blue/organizations/jenkins/whalestudio-2.6-release/detail/whalestudio-2.6-release/${env.BUILD_NUMBER}/pipeline'
        gitUrl = '**************:WhaleOps'
    }
    stages {
        stage('Clean Workspace') {
            steps {
                echo "Cleaning workspace"
                cleanWs()
                echo "Workspace cleaned"
            }
        }
        stage('clone whalescheduler') {
            steps {
                retry(3) {
                    echo "Cloning code from ${env.gitUrl}"
                    git branch: "${env.branch}", url: "${env.gitUrl}/whalescheduler.git"
                    echo "Code cloned"
                }
            }
        }
        stage('clone whalescheduler ui') {
            steps {
                retry(3) {
                    echo "Cloning code from ${env.gitUrl}"
                    sh "cd whalescheduler && git clone -b ${env.branch} ${env.gitUrl}/whalescheduler-ui.git"
                    echo "Code cloned"
                }
            }
        }
    }
}