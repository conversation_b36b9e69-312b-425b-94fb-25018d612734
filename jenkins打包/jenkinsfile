pipeline {
    agent any
    environment {
        branch = '2.6-release'
        buildStatus = 'SUCCESS'
        jenkinsUrl = 'http://**************/blue/organizations/jenkins/whalestudio-2.6-release/detail/whalestudio-2.6-release/${env.BUILD_NUMBER}/pipeline'
        gitUrl = '**************:WhaleOps'
    }
    stages {
        stage('Clean Workspace') {
            steps {
                echo "Cleaning workspace"
                cleanWs()
                echo "Workspace cleaned"
            }
        }

        stage('user input') {
            steps {
                script {
                    def timeoutTime = 10 // minutes
                    try {
                        def userInput = timeout(time: timeoutTime, unit: 'MINUTES') {
                            input(
                                id: "userInput",
                                message: "请选择版本号客户版本等信息",
                                parameters: [
                                    choice(name: "customer", choices: ['None', '-Pzhongxinjiantou', '-Pguangdalicai', '-Pthird-party-IT', '-Prenshou', '-Prenbao'], description: "请选择客户名称"),
                                    choice(name: "update_to_OSS", choices: ['Yes', 'No'], description: "是否更新到OSS, Yes: 更新, No: 不更新，默认Yes"),
                                    string(name: "baseline_version", defaultValue: "${branch}", description: "请输入代码分支名，默认${branch}")
                                ]
                            )
                        }
                        echo "userInput: ${userInput}"
                        env.customer = userInput.customer
                        env.update_to_OSS = userInput.update_to_OSS
                        env.baseline_version = userInput.baseline_version
                        echo "customer: ${env.customer}"
                        echo "update_to_OSS: ${env.update_to_OSS}"
                        echo "baseline_version: ${env.baseline_version}"
                    } catch (org.jenkinsci.plugins.workflow.steps.FlowInterruptedException e) {
                        echo "用户输入超时，需要发送告警"
                        currentBuild.result = 'ABORTED'
                    } catch (Exception e) {
                        echo "用户输入超时，需要发送告警"
                        currentBuild.result = 'ABORTED'
                    }
                }
            }
        }

        stage('makedirs') {
            steps {
                script {
                    sh "mkdir ${WORKSPACE}/{whalescheduler,whaletunnel,whaletunnel-web}"
                    sh "mkdir ${WORKSPACE}/whalescheduler/whalescheduler-ui"
                }
            }
        }

        stage('clone whalescheduler') {
            steps {
                retry(3) {
                    echo "Cloning code from ${env.gitUrl}"
                    // 需要指定存储目录
                    dir("whalescheduler") {
                        git branch: "${env.baseline_version}", url: "${env.gitUrl}/whalescheduler.git"
                    }
                    echo "Code cloned"
                }
            }
        }
        stage('clone whalescheduler ui') {
            steps {
                retry(3) {
                    echo "Cloning code from ${env.gitUrl}"
                    dir("whalescheduler/whalescheduler-ui") {
                        git branch: "${env.baseline_version}", url: "${env.gitUrl}/whalescheduler-ui.git"
                    }
                    echo "Code cloned"
                }
            }
        }
        stage('clone whaletunnel') {
            steps {
                retry(3) {
                    echo "Cloning code from ${env.gitUrl}"
                    dir("whaletunnel") {
                        git branch: "${env.baseline_version}", url: "${env.gitUrl}/whaletunnel.git"
                    }
                    echo "Code cloned"
                }
            }
        }

        stage('clone whaletunnel web') {
            steps {
                retry(3) {
                    echo "Cloning code from ${env.gitUrl}"
                    dir("whaletunnel-web") {
                        git branch: "${env.baseline_version}", url: "${env.gitUrl}/whaletunnel-web.git"
                    }
                    echo "Code cloned"
                }
            }
        }
    }
}