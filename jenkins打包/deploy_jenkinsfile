pipeline {
    agent any
    options {
        timestamps()
    }
    environment {
        GIT_TOKEN = '****************************************'
        GITHUB_BASE_URL = "https://${GIT_TOKEN}@github.com/WhaleOps"

    }
    stages {
        stage('Clean workspace') {
            steps {
                cleanWs()
            }
        }
        stage('Clone WhaleTunnel and deploy to Nexus') {
            steps {
                sh "git clone -b 2.6-test ${GITHUB_BASE_URL}/whaletunnel.git"
                sh "cd whaletunnel && mvnd -T6 --batch-mode  clean deploy \
                -DaltDeploymentRepository=my-nexus::default::http://*************:8081/repository/whalestudio-snapshot \
                -Dmaven.test.skip -Dcheckstyle.skip -DretryFailedDeploymentCount=5"
                sh "cd whaletunnel && git checkout 2.6-release"
                sh "cd whaletunnel && mvnd -T6 --batch-mode  clean deploy \
                -DaltDeploymentRepository=my-nexus::default::http://*************:8081/repository/whalestudio-snapshot \
                -Dmaven.test.skip -Dcheckstyle.skip -DretryFailedDeploymentCount=5"
            }
        }
        stage('Clone WhaleTunnel-Web and deploy to Nexus') {
            steps {
                sh "git clone -b 2.6-test ${GITHUB_BASE_URL}/whaletunnel-web.git"
                sh "cd whaletunnel-web && mvnd -T6 --batch-mode  clean deploy \
                -DaltDeploymentRepository=my-nexus::default::http://*************:8081/repository/whalestudio-snapshot \
                -Dmaven.test.skip -Dcheckstyle.skip -DretryFailedDeploymentCount=5"
                sh "cd whaletunnel-web && git checkout 2.6-release"
                sh "cd whaletunnel-web && mvnd -T6 --batch-mode  clean deploy \
                -DaltDeploymentRepository=my-nexus::default::http://*************:8081/repository/whalestudio-snapshot \
                -Dmaven.test.skip -Dcheckstyle.skip -DretryFailedDeploymentCount=5"
            }
        }

    }

}
