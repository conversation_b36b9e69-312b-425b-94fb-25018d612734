#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : weekly_reminder_new.py
# @Time    : 2025/07/23 13:33
# <AUTHOR> chen<PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys
import datetime
import traceback

import requests
from typing import Optional

from anaconda_project.internal.conda_api import result

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))

"""
1. 判断今天是否是周四，如果是 则检查今天属于几月， 几月的第几周
2. 通过飞书API接口，copy一个Excel文档。新文档名称为 XXX年xx月第xx周-线上问题回顾
3. 在知识库下创建一个 xx年xx月的父文档，如果已存在则不创建
4. 将新创建的文档添加到父文档下
5. 通过飞书API接口，将新创建的文档添加到指定的群组中
"""


class WeeklyReminder:

    @classmethod
    def get_week_of_month(cls, date):
        """
        计算指定日期是本月的第几周
        Args:
            date: datetime对象
        Returns:
            int: 本月第几周
        """
        # 获取本月第一天
        first_day = date.replace(day = 1)
        # 计算第一天是周几（0=周一，6=周日）
        first_weekday = first_day.weekday()

        # 计算当前日期是本月第几周
        # 如果第一天不是周一，则第一周可能不完整
        week_number = (date.day + first_weekday - 1) // 7 + 1

        return week_number

    @classmethod
    def thursday_reminder(cls):
        """
        周四提醒主函数
        """
        today = datetime.date.today()

        # 检查今天是否为周四（weekday() 返回 3 表示周四）
        if today.weekday() == 2:  # 周四
            week_num = cls.get_week_of_month(today)
            month_name = today.strftime("%Y年%m月")
            # 1. 判断 month_name 的文档是否存在
            is_month_exist, month_token = FeishuTools.is_month_exist(month_name)
            if not is_month_exist:
                # 2. 创建 month_name 的文档
                print("月份文档不存在，创建月份文档")
                FeishuTools.create_month_doc(month_name = month_name)

            print(f"今天是{month_name}的第{week_num}周，是周四，发送提醒")
            return f"{month_name}第{week_num}周-线上问题回顾"
        else:
            print(
                f"今天是{today.strftime('%Y年%m月%d日')} {['周一', '周二', '周三', '周四', '周五', '周六', '周日'][today.weekday()]}"
            )
            print("不是周四，无需提醒")
            return False


class FeishuTools:
    # 知识库 space id
    WIKI_SPACE_ID = "7530121834991091740"

    @classmethod
    def get_tenant_access_token(cls):
        """
        Get headers for Feishu API.
        Returns:
        """
        try:
            result = requests.post(
                headers = {
                    "Content-Type": "application/json"
                },
                json = {
                    "app_id": "cli_a80aebb677fcd013",
                    "app_secret": "n2KbftnWf5kiCAKJXklfLhBZuy01l4b5"
                },
                url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
            ).json()
            if result.get("code") != 0:
                print("获取 access token 失败")
                print(result.get("msg"))
                sys.exit(1)
            tenant_access_token = result.get("tenant_access_token")
            return tenant_access_token
        except Exception as e:
            print("获取 access token 失败")
            print(e)
            print(traceback.format_exc())
            sys.exit(1)

    @classmethod
    def is_month_exist(cls, month_name: Optional[str] = None) -> tuple[bool, str]:
        """
        检查月份文档是否存在
        Args:
            month_name:
        Returns:
            bool: True表示存在，False表示不存在
        """
        if not month_name:
            month_name = datetime.date.today().strftime("%Y年%m月")
            print(
                f"没有传入月份名称，使用当前日期作为月份名称：{month_name}"
            )

        # 检查月份文档是否存在
        docs = cls.get_wiki_docs()
        if not docs:
            return False, ""
        for doc in docs:
            if doc.get("title") == month_name:
                print(f"月份文档已存在：{month_name}")
                return True, doc.get("token")
        return False, ""

    # 获取知识库下所有的文档
    @classmethod
    def get_wiki_docs(cls):
        """
        获取知识库下所有的文档
        Returns:
            list: 文档列表
        """
        try:
            result = requests.get(
                "https://open.feishu.cn/open-apis/wiki/v2/spaces/7530121834991091740/nodes",
                headers = {
                    "Authorization": "Bearer u-fOtDjqgtt86FHLb0kcxqB4h4hmvw010rNgG05h.a2KoM"
                }
            )
            result_json = result.json()
            if result_json.get("code") != 0:
                print("获取知识库下所有的文档失败")
                print(result_json.msg)
                return []

            docs = result_json.get("data", {}).get("items", [])
            if not docs:
                print("没有文档")
                return []
            else:
                docs = [
                    {
                        "token": doc.get("obj_token"),
                        "title": doc.get("title")
                    } for doc in docs
                ]
                print(docs)
                return docs

        except Exception as e:
            print("获取知识库下所有的文档失败")
            print(e)

    @classmethod
    def create_month_doc(cls, month_name: str):
        """
        创建月份文档
        Args:
            month_name: 月份名称
        Returns:
            str: 文档token
        """
        result = requests.post(
            headers = {
                "Authorization": f"Bearer {cls.get_tenant_access_token()}",
                "Content-Type": "application/json"
            },
            url = "https://open.feishu.cn/open-apis/docx/v1/documents",
            json = {
                "folder_token": "G7s0fgVHWl8gVCdDUpvcM9O7nTh",
                "title": month_name
            }
        )
        print(result.json())


if __name__ == '__main__':
    FeishuTools.get_tenant_access_token()